server:
  port: 8080
  servlet:
    context-path: /api

spring:
  application:
    name: flight-price-prediction
  mvc:
    pathmatch:
      matching-strategy: ANT_PATH_MATCHER
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: ******************************************************************************************************************************************************************************************************************************************************
    username: root
    password: root
    hikari:
      # 连接池最大连接数
      maximum-pool-size: 20
      # 最小空闲连接数
      minimum-idle: 5
      # 空闲连接存活最大时间（毫秒）
      idle-timeout: 300000
      # 连接最大存活时间（毫秒）
      max-lifetime: 1800000
      # 连接超时时间（毫秒）
      connection-timeout: 60000
      # 测试连接是否有效的查询语句
      connection-test-query: SELECT 1
      # 自动提交
      auto-commit: true
      # 连接池名
      pool-name: FlightPriceHikariCP
      # 验证超时时间
      validation-timeout: 5000
      # 连接保持活跃的时间间隔（新增，确保连接不会闲置太久）
      keepalive-time: 60000
      # 是否允许池暂停
      allow-pool-suspension: false
      # 初始化失败是否快速返回
      initialization-fail-timeout: 1
      # 连接泄漏检测阈值（新增）
      leak-detection-threshold: 60000
      # 注册JDBC驱动程序的MBean（新增）
      register-mbeans: true
  jpa:
    database-platform: org.hibernate.dialect.MySQL8Dialect
    hibernate:
      ddl-auto: update
    show-sql: true
    properties:
      hibernate:
        format_sql: true
  redis:
    host: localhost
    port: 6379
    password:
    database: 0
    lettuce:
      pool:
        max-active: 8
        max-wait: -1ms
        max-idle: 8
        min-idle: 0
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8

mybatis-plus:
  mapper-locations: classpath:mapper/*.xml
  type-aliases-package: com.flightprice.prediction.entity
  configuration:
    map-underscore-to-camel-case: true
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  global-config:
    db-config:
      id-type: auto
      logic-delete-field: is_deleted
      logic-delete-value: 1
      logic-not-delete-value: 0

# JWT配置
jwt:
  # JWT加密密钥
  secret: flight-price-prediction-secret-key
  # token有效时间（单位：秒）
  expiration: 86400

# 文件上传路径
file:
  upload-path: /upload/

# 模型配置
model:
  # 模型文件保存路径
  save-path: /models/
  # 是否在启动时训练模型
  train-on-startup: false
  # 是否使用通用模型（不指定航线ID）
  use-general-model: true
  # 训练数据最小条数，少于这个数量将生成模拟数据
  min-training-data: 100
  # 模型参数
  parameters:
    # 学习率
    learning-rate: 0.001
    # 训练轮数
    epochs: 100
    # 批次大小
    batch-size: 32

# 日志配置
logging:
  level:
    com.flightprice.prediction: debug
    org.springframework: info