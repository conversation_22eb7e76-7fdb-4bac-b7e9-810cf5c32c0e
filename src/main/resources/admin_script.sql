-- 检查并创建ADMIN角色
INSERT INTO roles (name, code, display_name, description, created_at, updated_at)
SELECT 'ADMIN', 'ROLE_ADMIN', '系统管理员', '拥有系统所有权限', NOW(), NOW()
WHERE NOT EXISTS (SELECT 1 FROM roles WHERE name = 'ADMIN');

-- 获取刚刚创建的ADMIN角色ID
SET @admin_role_id = (SELECT id FROM roles WHERE name = 'ADMIN');

-- 创建admin用户 (密码使用BCrypt加密的"admin")
INSERT INTO users (username, password, full_name, email, enabled, created_at, updated_at)
SELECT 'admin', '$2a$10$kqnqGJUQluQl/UKPSfq5xuIj33TrOp72iRz9bsOJGVhB7X3yeQHCG', '系统管理员', '<EMAIL>', 1, NOW(), NOW()
WHERE NOT EXISTS (SELECT 1 FROM users WHERE username = 'admin');

-- 获取用户ID
SET @admin_user_id = (SELECT id FROM users WHERE username = 'admin');

-- 关联用户和角色
INSERT INTO user_roles (user_id, role_id)
SELECT @admin_user_id, @admin_role_id
WHERE NOT EXISTS (SELECT 1 FROM user_roles WHERE user_id = @admin_user_id AND role_id = @admin_role_id);

-- 输出创建的管理员信息
SELECT 'Admin用户已创建! 用户名: admin, 密码: admin' AS message; 