<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.flightprice.prediction.mapper.FlightDataMapper">

    <!-- 获取训练数据 -->
    <select id="getTrainingData" resultType="java.util.HashMap">
        SELECT
            id,
            departure_city as departureCity,
            arrival_city as arrivalCity,
            mileage as distance,
            airline,
            aircraft_type as aircraftType,
            punctuality_rate as punctualityRate,
            CASE 
                WHEN DAYOFWEEK(STR_TO_DATE(flight_date, '%Y-%m-%d')) = 1 THEN 7
                ELSE DAYOFWEEK(STR_TO_DATE(flight_date, '%Y-%m-%d')) - 1
            END as dayOfWeek,
            MONTH(STR_TO_DATE(flight_date, '%Y-%m-%d')) as month,
            departure_time as departureTime,
            passenger_count as passengerCount,
            price
        FROM
            flight_data
        WHERE
            is_deleted = 0
            <if test="routeId != null">
                AND CONCAT(departure_city, '-', arrival_city) IN (
                    SELECT CONCAT(departure_city, '-', arrival_city) FROM route WHERE id = #{routeId}
                )
            </if>
            <if test="startDate != null">
                AND STR_TO_DATE(flight_date, '%Y-%m-%d') >= #{startDate}
            </if>
            <if test="endDate != null">
                AND STR_TO_DATE(flight_date, '%Y-%m-%d') &lt;= #{endDate}
            </if>
        ORDER BY
            flight_date, departure_time
    </select>

    <!-- 获取历史价格数据 -->
    <select id="getHistoryPrices" resultType="java.util.HashMap">
        SELECT
            flight_date as flightDate,
            price,
            passenger_count as passengerCount
        FROM
            flight_data
        WHERE
            is_deleted = 0
            AND departure_city = #{departureCity}
            AND arrival_city = #{arrivalCity}
            <if test="airline != null and airline != ''">
                AND airline = #{airline}
            </if>
        ORDER BY
            flight_date
        LIMIT 90
    </select>

</mapper> 