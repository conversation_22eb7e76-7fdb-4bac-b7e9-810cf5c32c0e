<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.flightprice.prediction.mapper.TerminalMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.flightprice.prediction.entity.Terminal">
        <id column="id" property="id" />
        <result column="terminal_code" property="terminalCode" />
        <result column="name" property="name" />
        <result column="airport_id" property="airportId" />
        <result column="type" property="type" />
        <result column="gate_count" property="gateCount" />
        <result column="status" property="status" />
        <result column="remark" property="remark" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>
    
    <!-- 带机场信息的结果映射 -->
    <resultMap id="TerminalWithAirportMap" type="com.flightprice.prediction.entity.Terminal" extends="BaseResultMap">
        <association property="airport" javaType="com.flightprice.prediction.entity.Airport">
            <id column="airport_id" property="id" />
            <result column="airport_name" property="name" />
            <result column="airport_code" property="code" />
        </association>
    </resultMap>

    <!-- 分页查询航站楼信息 -->
    <select id="selectTerminalPage" resultMap="TerminalWithAirportMap">
        SELECT
            t.*,
            a.name AS airport_name,
            a.code AS airport_code
        FROM
            terminals t
        LEFT JOIN
            airport a ON t.airport_id = a.id
        <where>
            <if test="name != null and name != ''">
                AND t.name LIKE CONCAT('%', #{name}, '%')
            </if>
            <if test="airportId != null">
                AND t.airport_id = #{airportId}
            </if>
            <if test="status != null">
                AND t.status = #{status}
            </if>
        </where>
        ORDER BY
            t.create_time DESC
    </select>
</mapper> 