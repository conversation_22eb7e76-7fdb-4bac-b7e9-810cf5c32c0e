<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.flightprice.prediction.mapper.UserMapper">

    <!-- 基础列映射 -->
    <resultMap id="BaseResultMap" type="com.flightprice.prediction.entity.User">
        <id column="id" property="id"/>
        <result column="username" property="username"/>
        <result column="password" property="password"/>
        <result column="full_name" property="fullName"/>
        <result column="email" property="email"/>
        <result column="phone" property="phone"/>
        <result column="enabled" property="enabled"/>
        <result column="created_at" property="createdAt"/>
        <result column="updated_at" property="updatedAt"/>
        <result column="last_login" property="lastLogin"/>
        <result column="avatar" property="avatar"/>
        <result column="description" property="description"/>
    </resultMap>

    <!-- 查询用户基本信息，按用户名查询 -->
    <select id="selectOneByUsername" resultMap="BaseResultMap">
        SELECT 
            id, username, password, full_name, email, phone, 
            enabled, created_at, updated_at, last_login, 
            avatar, description
        FROM users
        WHERE username = #{username}
    </select>

</mapper> 