-- 创建航班数据表
CREATE TABLE IF NOT EXISTS `flight_data` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `departure_city` varchar(50) DEFAULT NULL COMMENT '出发城市',
  `departure_city_y` double DEFAULT NULL COMMENT '出发城市y坐标',
  `departure_city_x` double DEFAULT NULL COMMENT '出发城市x坐标',
  `arrival_city` varchar(50) DEFAULT NULL COMMENT '到达城市',
  `arrival_city_y` double DEFAULT NULL COMMENT '到达城市y坐标',
  `arrival_city_x` double DEFAULT NULL COMMENT '到达城市x坐标',
  `mileage` int(11) DEFAULT NULL COMMENT '里程（公里）',
  `flight_number` varchar(20) DEFAULT NULL COMMENT '航班班次',
  `airline` varchar(50) DEFAULT NULL COMMENT '航空公司',
  `aircraft_type` varchar(50) DEFAULT NULL COMMENT '机型',
  `departure_airport` varchar(100) DEFAULT NULL COMMENT '起飞机场',
  `departure_airport_y` double DEFAULT NULL COMMENT '起飞机场y坐标',
  `departure_airport_x` double DEFAULT NULL COMMENT '起飞机场x坐标',
  `arrival_airport` varchar(100) DEFAULT NULL COMMENT '降落机场',
  `arrival_airport_y` double DEFAULT NULL COMMENT '降落机场y坐标',
  `arrival_airport_x` double DEFAULT NULL COMMENT '降落机场x坐标',
  `punctuality_rate` double DEFAULT NULL COMMENT '准点率',
  `average_delay_time` varchar(20) DEFAULT NULL COMMENT '平均误点时间',
  `monday_schedule` varchar(20) DEFAULT NULL COMMENT '周一班期',
  `tuesday_schedule` varchar(20) DEFAULT NULL COMMENT '周二班期',
  `wednesday_schedule` varchar(20) DEFAULT NULL COMMENT '周三班期',
  `thursday_schedule` varchar(20) DEFAULT NULL COMMENT '周四班期',
  `friday_schedule` varchar(20) DEFAULT NULL COMMENT '周五班期',
  `saturday_schedule` varchar(20) DEFAULT NULL COMMENT '周六班期',
  `sunday_schedule` varchar(20) DEFAULT NULL COMMENT '周日班期',
  `city_x` varchar(50) DEFAULT NULL COMMENT '城市_x',
  `departure_province` varchar(50) DEFAULT NULL COMMENT '出发省份',
  `city_y` varchar(50) DEFAULT NULL COMMENT '城市_y',
  `arrival_province` varchar(50) DEFAULT NULL COMMENT '到达省份',
  `departure_time` varchar(20) DEFAULT NULL COMMENT '起飞时间',
  `arrival_time` varchar(20) DEFAULT NULL COMMENT '降落时间',
  `flight_date` varchar(20) DEFAULT NULL COMMENT '日期',
  `price` decimal(10,2) DEFAULT NULL COMMENT '价格（元）',
  `passenger_count` int(11) DEFAULT NULL COMMENT '人数',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `is_deleted` tinyint(1) DEFAULT 0 COMMENT '是否删除',
  PRIMARY KEY (`id`),
  INDEX `idx_flight_number` (`flight_number`),
  INDEX `idx_departure_arrival` (`departure_city`, `arrival_city`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='航班数据表'; 