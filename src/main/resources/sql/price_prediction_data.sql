-- 价格预测数据表
CREATE TABLE IF NOT EXISTS price_prediction_data (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    model_id BIGINT NOT NULL COMMENT '模型ID',
    departure_city VARCHAR(50) NOT NULL COMMENT '出发城市',
    arrival_city VARCHAR(50) NOT NULL COMMENT '到达城市',
    airline VARCHAR(50) COMMENT '航空公司',
    flight_date DATE NOT NULL COMMENT '飞行日期',
    departure_time VARCHAR(10) COMMENT '出发时间',
    predicted_price DECIMAL(10,2) NOT NULL COMMENT '预测价格',
    actual_price DECIMAL(10,2) COMMENT '实际价格',
    error_rate DECIMAL(10,4) COMMENT '误差率',
    prediction_time DATETIME NOT NULL COMMENT '预测时间',
    feature_data JSON COMMENT '预测使用的特征数据',
    is_deleted TINYINT(1) DEFAULT 0 COMMENT '是否删除',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_model_id(model_id),
    INDEX idx_departure_arrival(departure_city, arrival_city),
    INDEX idx_flight_date(flight_date)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='价格预测数据表'; 