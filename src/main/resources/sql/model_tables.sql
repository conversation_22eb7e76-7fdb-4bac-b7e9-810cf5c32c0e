-- 预测模型表
CREATE TABLE IF NOT EXISTS model (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '模型ID',
    model_name VARCHAR(100) NOT NULL COMMENT '模型名称',
    description VARCHAR(500) COMMENT '模型描述',
    departure_city VARCHAR(50) COMMENT '起始城市',
    arrival_city VARCHAR(50) COMMENT '目的城市',
    airlines JSON COMMENT '航空公司列表',
    algorithm_type VARCHAR(50) NOT NULL COMMENT '算法类型',
    parameters JSON COMMENT '算法参数',
    features JSON COMMENT '使用的特征',
    model_path VARCHAR(255) COMMENT '模型文件路径',
    status VARCHAR(20) NOT NULL COMMENT '模型状态：CREATED, TRAINING, TRAINED, FAILED',
    active BOOLEAN DEFAULT FALSE COMMENT '是否激活使用中',
    accuracy DOUBLE COMMENT '训练准确率',
    mean_error DOUBLE COMMENT '平均预测误差',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    last_train_time DATETIME COMMENT '最后训练时间',
    create_user_id BIGINT COMMENT '创建用户ID',
    use_count INT DEFAULT 0 COMMENT '使用次数计数',
    INDEX idx_model_name(model_name),
    INDEX idx_algorithm(algorithm_type),
    INDEX idx_status(status),
    INDEX idx_active(active)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='预测模型表';

-- 模型训练记录表
CREATE TABLE IF NOT EXISTS model_training_record (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '记录ID',
    model_id BIGINT NOT NULL COMMENT '模型ID',
    start_time DATETIME NOT NULL COMMENT '开始时间',
    end_time DATETIME COMMENT '结束时间',
    status VARCHAR(20) NOT NULL COMMENT '状态：RUNNING, COMPLETED, FAILED',
    train_data_count INT COMMENT '训练数据量',
    test_data_count INT COMMENT '测试数据量',
    accuracy DOUBLE COMMENT '训练准确率',
    mean_error DOUBLE COMMENT '平均误差',
    metrics JSON COMMENT '详细评估指标',
    error_message TEXT COMMENT '错误信息',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    INDEX idx_model_id(model_id),
    INDEX idx_status(status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='模型训练记录表';

-- 模型训练任务表
CREATE TABLE IF NOT EXISTS model_training_task (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '任务ID',
    model_id BIGINT NOT NULL COMMENT '模型ID',
    task_id VARCHAR(50) NOT NULL COMMENT '训练任务标识',
    start_time DATETIME NOT NULL COMMENT '开始时间',
    end_time DATETIME COMMENT '完成时间',
    status VARCHAR(20) NOT NULL COMMENT '状态：QUEUED,RUNNING,COMPLETED,FAILED',
    progress DECIMAL(5,2) DEFAULT 0 COMMENT '训练进度(0-100)',
    training_params JSON COMMENT '训练参数',
    result_metrics JSON COMMENT '训练结果指标',
    error_message TEXT COMMENT '错误信息',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_model_id(model_id),
    INDEX idx_task_id(task_id),
    INDEX idx_status(status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='模型训练任务表';

-- 价格预测历史记录表
CREATE TABLE IF NOT EXISTS prediction_history (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '记录ID',
    user_id BIGINT COMMENT '用户ID',
    model_id BIGINT NOT NULL COMMENT '模型ID',
    departure_city VARCHAR(50) NOT NULL COMMENT '出发城市',
    arrival_city VARCHAR(50) NOT NULL COMMENT '到达城市',
    airline VARCHAR(50) COMMENT '航空公司',
    flight_date DATE NOT NULL COMMENT '飞行日期',
    departure_time VARCHAR(10) COMMENT '出发时间',
    predicted_price DECIMAL(10,2) NOT NULL COMMENT '预测价格',
    confidence DECIMAL(5,2) COMMENT '置信度',
    search_params JSON COMMENT '搜索参数',
    prediction_time DATETIME NOT NULL COMMENT '预测时间',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    INDEX idx_user_id(user_id),
    INDEX idx_model_id(model_id),
    INDEX idx_cities(departure_city, arrival_city),
    INDEX idx_flight_date(flight_date),
    INDEX idx_prediction_time(prediction_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='价格预测历史记录表'; 