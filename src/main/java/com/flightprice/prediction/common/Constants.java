package com.flightprice.prediction.common;

/**
 * 系统常量
 */
public class Constants {

    /**
     * 用户角色
     */
    public static class Role {
        /**
         * 管理员
         */
        public static final String ADMIN = "ADMIN";
        /**
         * 普通用户
         */
        public static final String USER = "USER";
    }

    /**
     * 订单状态
     */
    public static class OrderStatus {
        /**
         * 未支付
         */
        public static final String UNPAID = "UNPAID";
        /**
         * 已支付
         */
        public static final String PAID = "PAID";
        /**
         * 已取消
         */
        public static final String CANCELLED = "CANCELLED";
        /**
         * 已完成
         */
        public static final String COMPLETED = "COMPLETED";
    }

    /**
     * 航班状态
     */
    public static class FlightStatus {
        /**
         * 正常
         */
        public static final String NORMAL = "NORMAL";
        /**
         * 延误
         */
        public static final String DELAYED = "DELAYED";
        /**
         * 取消
         */
        public static final String CANCELLED = "CANCELLED";
    }

    /**
     * 舱位等级
     */
    public static class CabinClass {
        /**
         * 经济舱
         */
        public static final String ECONOMY = "ECONOMY";
        /**
         * 商务舱
         */
        public static final String BUSINESS = "BUSINESS";
        /**
         * 头等舱
         */
        public static final String FIRST = "FIRST";
    }

    /**
     * 价格因素类型
     */
    public static class PriceFactorType {
        /**
         * 季节性
         */
        public static final String SEASONAL = "SEASONAL";
        /**
         * 节假日
         */
        public static final String HOLIDAY = "HOLIDAY";
        /**
         * 周末
         */
        public static final String WEEKEND = "WEEKEND";
        /**
         * 油价
         */
        public static final String OIL_PRICE = "OIL_PRICE";
        /**
         * 竞争
         */
        public static final String COMPETITION = "COMPETITION";
    }

    /**
     * Redis键前缀
     */
    public static class RedisKey {
        /**
         * 用户Token前缀
         */
        public static final String TOKEN_PREFIX = "user:token:";
        /**
         * 价格预测结果缓存前缀
         */
        public static final String PRICE_PREDICTION = "price:prediction:";
    }

    /**
     * 安全常量
     */
    public static class Security {
        /**
         * JWT Token前缀
         */
        public static final String TOKEN_PREFIX = "Bearer ";
        /**
         * Header中的Token键名
         */
        public static final String TOKEN_HEADER = "Authorization";
    }
    
    /**
     * 日期格式
     */
    public static class DateFormat {
        /**
         * 日期时间格式
         */
        public static final String DATETIME_FORMAT = "yyyy-MM-dd HH:mm:ss";
        /**
         * 日期格式
         */
        public static final String DATE_FORMAT = "yyyy-MM-dd";
    }
} 