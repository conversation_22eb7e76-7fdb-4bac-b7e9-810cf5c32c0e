package com.flightprice.prediction.common;

import lombok.Data;

import java.util.List;

/**
 * 分页查询结果
 */
@Data
public class PageResult<T> {

    /**
     * 总记录数
     */
    private Long total;

    /**
     * 数据列表
     */
    private List<T> list;

    /**
     * 当前页码
     */
    private Integer pageNum;

    /**
     * 每页数量
     */
    private Integer pageSize;

    /**
     * 总页数
     */
    private Integer pages;

    /**
     * 是否有下一页
     */
    private Boolean hasNext;

    /**
     * 是否有上一页
     */
    private Boolean hasPrev;

    /**
     * 私有构造函数，通过静态方法创建实例
     */
    private PageResult() {
    }

    /**
     * 创建分页结果
     *
     * @param list     数据列表
     * @param total    总记录数
     * @param pageNum  当前页码
     * @param pageSize 每页数量
     * @param <T>      数据类型
     * @return 分页结果
     */
    public static <T> PageResult<T> build(List<T> list, Long total, Integer pageNum, Integer pageSize) {
        PageResult<T> result = new PageResult<>();
        result.setList(list);
        result.setTotal(total);
        result.setPageNum(pageNum);
        result.setPageSize(pageSize);
        
        // 计算总页数
        int pages = (int) ((total + pageSize - 1) / pageSize);
        result.setPages(pages);
        
        // 是否有下一页
        result.setHasNext(pageNum < pages);
        
        // 是否有上一页
        result.setHasPrev(pageNum > 1);
        
        return result;
    }
    
    /**
     * 创建空的分页结果
     *
     * @param pageNum  当前页码
     * @param pageSize 每页数量
     * @param <T>      数据类型
     * @return 空的分页结果
     */
    public static <T> PageResult<T> empty(Integer pageNum, Integer pageSize) {
        return build(List.of(), 0L, pageNum, pageSize);
    }
} 