package com.flightprice.prediction.common.result;

import java.io.Serializable;

/**
 * 通用返回结果
 */
public class R<T> implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 状态码
     */
    private Integer code;

    /**
     * 消息
     */
    private String message;

    /**
     * 数据
     */
    private T data;

    /**
     * 是否成功
     */
    private Boolean success;

    /**
     * 私有构造函数，通过静态方法创建实例
     */
    private R() {
    }

    /**
     * 成功结果
     *
     * @param <T> 数据类型
     * @return R对象
     */
    public static <T> R<T> ok() {
        return ok(null);
    }

    /**
     * 成功结果
     *
     * @param data 数据
     * @param <T>  数据类型
     * @return R对象
     */
    public static <T> R<T> ok(T data) {
        return ok("操作成功", data);
    }

    /**
     * 成功结果
     *
     * @param message 消息
     * @param data    数据
     * @param <T>     数据类型
     * @return R对象
     */
    public static <T> R<T> ok(String message, T data) {
        R<T> result = new R<>();
        result.code = 200;
        result.message = message;
        result.data = data;
        result.success = true;
        return result;
    }

    /**
     * 失败结果
     *
     * @param <T> 数据类型
     * @return R对象
     */
    public static <T> R<T> error() {
        return error("操作失败");
    }

    /**
     * 失败结果
     *
     * @param message 消息
     * @param <T>     数据类型
     * @return R对象
     */
    public static <T> R<T> error(String message) {
        return error(500, message);
    }

    /**
     * 失败结果
     *
     * @param code    状态码
     * @param message 消息
     * @param <T>     数据类型
     * @return R对象
     */
    public static <T> R<T> error(Integer code, String message) {
        R<T> result = new R<>();
        result.code = code;
        result.message = message;
        result.data = null;
        result.success = false;
        return result;
    }

    /**
     * 失败结果（等同于 error 方法）
     *
     * @param message 消息
     * @param <T>     数据类型
     * @return R对象
     */
    public static <T> R<T> fail(String message) {
        return error(message);
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public T getData() {
        return data;
    }

    public void setData(T data) {
        this.data = data;
    }

    public Boolean getSuccess() {
        return success;
    }

    public void setSuccess(Boolean success) {
        this.success = success;
    }
}
