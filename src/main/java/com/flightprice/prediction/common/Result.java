package com.flightprice.prediction.common;

import lombok.Getter;

import java.io.Serializable;

/**
 * 通用响应结果
 */
@Getter
public class Result<T> implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 状态码
     */
    private Integer code;

    /**
     * 消息
     */
    private String message;

    /**
     * 数据
     */
    private T data;

    /**
     * 是否成功
     */
    private Boolean success;

    /**
     * 私有构造函数，通过静态方法创建实例
     */
    private Result() {
    }

    /**
     * 成功结果
     *
     * @param <T> 数据类型
     * @return Result对象
     */
    public static <T> Result<T> success() {
        return success(null);
    }

    /**
     * 成功结果
     *
     * @param data 数据
     * @param <T>  数据类型
     * @return Result对象
     */
    public static <T> Result<T> success(T data) {
        return success("操作成功", data);
    }

    /**
     * 成功结果
     *
     * @param message 消息
     * @param data    数据
     * @param <T>     数据类型
     * @return Result对象
     */
    public static <T> Result<T> success(String message, T data) {
        Result<T> result = new Result<>();
        result.setCode(200);
        result.setMessage(message);
        result.setData(data);
        result.setSuccess(true);
        return result;
    }

    /**
     * 失败结果
     *
     * @param <T> 数据类型
     * @return Result对象
     */
    public static <T> Result<T> error() {
        return error("操作失败");
    }

    /**
     * 失败结果
     *
     * @param message 消息
     * @param <T>     数据类型
     * @return Result对象
     */
    public static <T> Result<T> error(String message) {
        return error(500, message);
    }

    /**
     * 失败结果
     *
     * @param code    状态码
     * @param message 消息
     * @param <T>     数据类型
     * @return Result对象
     */
    public static <T> Result<T> error(Integer code, String message) {
        Result<T> result = new Result<>();
        result.setCode(code);
        result.setMessage(message);
        result.setData(null);
        result.setSuccess(false);
        return result;
    }

    /**
     * 失败结果（等同于 error 方法）
     *
     * @param message 消息
     * @param <T>     数据类型
     * @return Result对象
     */
    public static <T> Result<T> fail(String message) {
        return error(message);
    }

    /**
     * 设置状态码
     */
    public void setCode(Integer code) {
        this.code = code;
    }

    /**
     * 设置消息
     */
    public void setMessage(String message) {
        this.message = message;
    }

    /**
     * 设置数据
     */
    public void setData(T data) {
        this.data = data;
    }

    /**
     * 设置是否成功
     */
    public void setSuccess(Boolean success) {
        this.success = success;
    }
}