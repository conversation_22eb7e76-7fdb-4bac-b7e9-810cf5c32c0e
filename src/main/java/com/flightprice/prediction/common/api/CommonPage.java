package com.flightprice.prediction.common.api;

import com.baomidou.mybatisplus.core.metadata.IPage;
import lombok.Data;
import java.util.List;

/**
 * 分页数据封装类
 */
@Data
public class CommonPage<T> {
    /**
     * 当前页码
     */
    private Integer pageNum;
    /**
     * 每页数量
     */
    private Integer pageSize;
    /**
     * 总页数
     */
    private Integer totalPage;
    /**
     * 总记录数
     */
    private Long total;
    /**
     * 分页数据
     */
    private List<T> list;

    /**
     * 将MyBatis Plus分页结果转化为通用结果
     */
    public static <T> CommonPage<T> restPage(IPage<T> pageResult) {
        CommonPage<T> result = new CommonPage<T>();
        result.setPageNum((int) pageResult.getCurrent());
        result.setPageSize((int) pageResult.getSize());
        result.setTotal(pageResult.getTotal());
        result.setTotalPage((int) pageResult.getPages());
        result.setList(pageResult.getRecords());
        return result;
    }

    /**
     * 将分页参数转化为通用结果
     */
    public static <T> CommonPage<T> restPage(List<T> list, Integer pageNum, Integer pageSize, Long total) {
        CommonPage<T> result = new CommonPage<T>();
        result.setPageNum(pageNum);
        result.setPageSize(pageSize);
        result.setTotal(total);
        result.setTotalPage((int) Math.ceil((double) total / pageSize));
        result.setList(list);
        return result;
    }
}
