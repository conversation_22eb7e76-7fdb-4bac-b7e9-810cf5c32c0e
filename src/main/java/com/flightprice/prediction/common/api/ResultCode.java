package com.flightprice.prediction.common.api;

/**
 * 常用API返回对象枚举类
 */
public enum ResultCode {
    SUCCESS(200, "操作成功"),
    FAILED(500, "操作失败"),
    VALIDATE_FAILED(404, "参数检验失败"),
    UNAUTHORIZED(401, "暂未登录或token已经过期"),
    FORBIDDEN(403, "没有相关权限"),
    
    // 业务错误码
    FLIGHT_NOT_FOUND(1001, "航班不存在"),
    ROUTE_NOT_FOUND(1002, "航线不存在"),
    AIRLINE_NOT_FOUND(1003, "航空公司不存在"),
    TICKET_NOT_FOUND(1004, "机票不存在"),
    PRICE_PREDICTION_FAILED(1005, "价格预测失败"),
    MODEL_TRAINING_FAILED(1006, "模型训练失败"),
    DATA_COLLECTION_FAILED(1007, "数据收集失败"),
    PRICE_FACTOR_UPDATE_FAILED(1008, "价格因素更新失败");

    private long code;
    private String message;

    private ResultCode(long code, String message) {
        this.code = code;
        this.message = message;
    }

    public long getCode() {
        return code;
    }

    public String getMessage() {
        return message;
    }
} 