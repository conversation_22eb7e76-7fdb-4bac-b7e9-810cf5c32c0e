package com.flightprice.prediction.spark;

import com.flightprice.prediction.entity.FlightData;
import lombok.extern.slf4j.Slf4j;
import org.apache.spark.api.java.JavaRDD;
import org.apache.spark.api.java.JavaSparkContext;
import org.apache.spark.sql.*;
import org.apache.spark.sql.types.DataTypes;
import org.apache.spark.sql.types.StructField;
import org.apache.spark.sql.types.StructType;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.Serializable;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;
import java.util.Properties;

/**
 * Spark数据处理器 - 用于从Excel读取数据，进行清洗计算，并写入MySQL
 */
@Slf4j
@Component
public class SparkDataProcessor implements Serializable {

    private static final long serialVersionUID = 1L;

    @Value("${spring.datasource.url}")
    private String dbUrl;

    @Value("${spring.datasource.username}")
    private String dbUsername;

    @Value("${spring.datasource.password}")
    private String dbPassword;

    /**
     * 从Excel读取数据，使用Spark进行处理后写入MySQL
     *
     * @param excelFilePath Excel文件路径
     * @return 处理的数据条数
     */
    public int processExcelData(String excelFilePath) {
        log.info("开始使用Spark处理Excel数据: {}", excelFilePath);
        
        // 创建SparkSession
        SparkSession spark = SparkSession.builder()
                .appName("FlightDataProcessor")
                .master("local[*]")
                .getOrCreate();
        
        try {
            // 读取Excel文件
            Dataset<Row> df = spark.read()
                    .format("com.crealytics.spark.excel")
                    .option("header", "true")
                    .option("inferSchema", "true")
                    .option("dataAddress", "'Sheet1'!A1")
                    .load(excelFilePath);
            
            log.info("成功读取Excel数据，总行数: {}", df.count());
            
            // 数据清洗和转换
            Dataset<Row> cleanedDf = cleanAndTransformData(spark, df);
            
            // 执行一些聚合计算
            performAggregations(cleanedDf);
            
            // 写入MySQL
            int rowCount = saveToMySql(cleanedDf, "flight_data");
            
            log.info("Spark处理完成，共写入 {} 条数据到MySQL", rowCount);
            return rowCount;
        } catch (Exception e) {
            log.error("Spark处理Excel数据时发生错误: {}", e.getMessage(), e);
            return 0;
        } finally {
            // 关闭SparkSession
            spark.stop();
        }
    }
    
    /**
     * 数据清洗和转换
     */
    private Dataset<Row> cleanAndTransformData(SparkSession spark, Dataset<Row> df) {
        // 1. 处理列名 - 确保列名符合DataFrame要求
        List<String> originalColumns = new ArrayList<>();
        for (String colName : df.columns()) {
            originalColumns.add(colName);
        }
        
        // 2. 处理空值
        Dataset<Row> cleanedDf = df;
        for (String column : df.columns()) {
            cleanedDf = cleanedDf.na().fill(0, new String[]{column}); // 数值型空值填充为0
        }
        
        // 将字符串类型的列中的空值填充为空字符串
        cleanedDf = cleanedDf.na().fill("", df.columns());
        
        // 3. 派生新特征
        cleanedDf = cleanedDf.withColumn("flight_distance_km", 
                functions.col("mileage").cast(DataTypes.DoubleType));
        
        // 4. 添加行程时间特征(小时) - 如果出发和到达时间存在的话
        cleanedDf = cleanedDf.withColumn("flight_duration_hours", 
                functions.lit(2.0)); // 默认值，实际项目中应该计算真实的飞行时间
        
        // 5. 添加每公里价格特征
        cleanedDf = cleanedDf.withColumn("price_per_km", 
                functions.col("price").divide(functions.col("flight_distance_km"))
                        .cast(DataTypes.DoubleType));
        
        log.info("数据清洗和转换完成");
        return cleanedDf;
    }
    
    /**
     * 执行数据聚合计算
     */
    private void performAggregations(Dataset<Row> df) {
        // 1. 按航空公司分组，计算平均价格
        Dataset<Row> airlineStats = df.groupBy("airline")
                .agg(
                        functions.count("*").as("flight_count"),
                        functions.avg("price").as("avg_price"),
                        functions.min("price").as("min_price"),
                        functions.max("price").as("max_price")
                );
        
        log.info("航空公司价格统计:");
        airlineStats.show();
        
        // 2. 按航线(出发城市-到达城市)分组统计
        Dataset<Row> routeStats = df.groupBy("departureCity", "arrivalCity")
                .agg(
                        functions.count("*").as("flight_count"),
                        functions.avg("price").as("avg_price"),
                        functions.min("price").as("min_price"),
                        functions.max("price").as("max_price"),
                        functions.avg("flight_distance_km").as("avg_distance")
                );
        
        log.info("航线价格统计:");
        routeStats.show();
        
        // 3. 计算总体价格分布
        log.info("价格分布统计:");
        df.select("price").summary().show();
    }
    
    /**
     * 将处理后的数据保存到MySQL
     */
    private int saveToMySql(Dataset<Row> df, String tableName) {
        // 设置数据库连接属性
        Properties connectionProperties = new Properties();
        connectionProperties.put("user", dbUsername);
        connectionProperties.put("password", dbPassword);
        connectionProperties.put("driver", "com.mysql.cj.jdbc.Driver");
        
        // 获取行数
        long rowCount = df.count();
        
        // 将DataFrame转换为可写入MySQL的格式
        // 注意: 实际项目中应根据表结构进行列映射
        Dataset<Row> dfToSave = df.select(
                "departureCity", "departureCityY", "departureCityX",
                "arrivalCity", "arrivalCityY", "arrivalCityX", 
                "mileage", "flightNumber", "airline", "aircraftType",
                "departureAirport", "departureAirportY", "departureAirportX",
                "arrivalAirport", "arrivalAirportY", "arrivalAirportX",
                "punctualityRate", "averageDelayTime",
                "mondaySchedule", "tuesdaySchedule", "wednesdaySchedule",
                "thursdaySchedule", "fridaySchedule", "saturdaySchedule", "sundaySchedule",
                "cityX", "departureProvince", "cityY", "arrivalProvince",
                "departureTime", "arrivalTime", "flightDate", "price", "passengerCount",
                "flight_distance_km", "flight_duration_hours", "price_per_km"
        );
        
        try {
            // 写入MySQL - 使用append模式，不覆盖现有数据
            dfToSave.write()
                    .mode(SaveMode.Append)  // 如果需要覆盖使用SaveMode.Overwrite
                    .jdbc(dbUrl, tableName, connectionProperties);
            
            log.info("成功将数据写入MySQL表: {}", tableName);
            return (int) rowCount;
        } catch (Exception e) {
            log.error("写入MySQL失败: {}", e.getMessage(), e);
            return 0;
        }
    }
    
    /**
     * 转换FlightData对象列表为Spark DataFrame
     */
    public Dataset<Row> convertToDataFrame(SparkSession spark, List<FlightData> dataList) {
        if (dataList == null || dataList.isEmpty()) {
            log.warn("输入的航班数据列表为空");
            return spark.emptyDataFrame();
        }
        
        // 创建JavaSparkContext
        JavaSparkContext jsc = JavaSparkContext.fromSparkContext(spark.sparkContext());
        
        // 将Java对象列表转换为Spark RDD
        JavaRDD<FlightData> rdd = jsc.parallelize(dataList);
        
        // 从RDD创建DataFrame
        Dataset<Row> df = spark.createDataFrame(rdd, FlightData.class);
        
        log.info("成功将 {} 条FlightData对象转换为DataFrame", df.count());
        return df;
    }
} 