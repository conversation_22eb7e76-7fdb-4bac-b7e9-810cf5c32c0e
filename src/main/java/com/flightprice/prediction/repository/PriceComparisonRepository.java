package com.flightprice.prediction.repository;

import com.flightprice.prediction.entity.PriceComparison;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface PriceComparisonRepository extends JpaRepository<PriceComparison, Long> {
    List<PriceComparison> findByFlightId(Long flightId);
    List<PriceComparison> findByCabinClass(String cabinClass);
    List<PriceComparison> findByFlightIdAndCabinClass(Long flightId, String cabinClass);
} 