package com.flightprice.prediction.repository;

import com.flightprice.prediction.entity.Model;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * 预测模型数据访问层
 */
@Repository
public interface ModelRepository extends JpaRepository<Model, Long> {
    
    /**
     * 根据名称查找模型
     *
     * @param name 模型名称
     * @return 模型对象
     */
    Optional<Model> findByName(String name);
    
    /**
     * 检查模型名称是否存在
     *
     * @param name 模型名称
     * @return 是否存在
     */
    boolean existsByName(String name);
    
    /**
     * 根据模型类型查找模型列表
     *
     * @param type 模型类型
     * @return 模型列表
     */
    List<Model> findByType(String type);
    
    /**
     * 查找激活的模型
     *
     * @return 激活的模型
     */
    Optional<Model> findByIsActiveTrue();
    
    /**
     * 根据关键字查询模型
     *
     * @param keyword 关键字
     * @param pageable 分页参数
     * @return 模型分页列表
     */
    @Query("SELECT m FROM Model m WHERE " +
           "(:keyword IS NULL OR " +
           "LOWER(m.name) LIKE LOWER(CONCAT('%', :keyword, '%')) OR " +
           "LOWER(m.type) LIKE LOWER(CONCAT('%', :keyword, '%')) OR " +
           "LOWER(m.description) LIKE LOWER(CONCAT('%', :keyword, '%')) OR " +
           "LOWER(m.version) LIKE LOWER(CONCAT('%', :keyword, '%')))")
    Page<Model> findByKeyword(@Param("keyword") String keyword, Pageable pageable);
    
    /**
     * 根据状态查询模型列表
     *
     * @param status 状态
     * @return 模型列表
     */
    List<Model> findByStatus(String status);
    
    /**
     * 根据准确率区间查询模型列表
     *
     * @param minAccuracy 最小准确率
     * @param maxAccuracy 最大准确率
     * @return 模型列表
     */
    List<Model> findByAccuracyBetween(Double minAccuracy, Double maxAccuracy);
    
    /**
     * 根据创建者ID查询模型列表
     *
     * @param createdBy 创建者ID
     * @return 模型列表
     */
    List<Model> findByCreatedBy(Long createdBy);
} 