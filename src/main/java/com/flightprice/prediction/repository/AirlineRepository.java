package com.flightprice.prediction.repository;

import com.flightprice.prediction.entity.Airline;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * 航空公司数据访问层
 */
@Repository
public interface AirlineRepository extends JpaRepository<Airline, Long> {

    /**
     * 根据代码查找航空公司
     *
     * @param code 航空公司代码
     * @return 航空公司
     */
    Optional<Airline> findByCode(String code);

    /**
     * 根据航空公司名称查找航空公司
     *
     * @param name 航空公司名称
     * @return 航空公司对象
     */
    Optional<Airline> findByName(String name);

    /**
     * 检查代码是否存在
     *
     * @param code 航空公司代码
     * @return 是否存在
     */
    boolean existsByCode(String code);

    /**
     * 检查航空公司名称是否存在
     *
     * @param name 航空公司名称
     * @return 是否存在
     */
    boolean existsByName(String name);

    /**
     * 获取所有启用的航空公司
     *
     * @return 航空公司列表
     */
    @Query("SELECT a FROM Airline a ORDER BY a.code ASC")
    List<Airline> findByEnabledTrueOrderByCodeAsc();

    /**
     * 获取所有启用的航空公司
     *
     * @return 航空公司列表
     */
    @Query("SELECT a FROM Airline a")
    List<Airline> findByEnabledTrue();

    /**
     * 根据关键字查询航空公司
     *
     * @param keyword 关键字
     * @param pageable 分页参数
     * @return 航空公司分页列表
     */
    @Query("SELECT a FROM Airline a WHERE " +
           "(:keyword IS NULL OR " +
           "LOWER(a.code) LIKE LOWER(CONCAT('%', :keyword, '%')) OR " +
           "LOWER(a.name) LIKE LOWER(CONCAT('%', :keyword, '%')))")
    Page<Airline> findByKeyword(@Param("keyword") String keyword, Pageable pageable);
}