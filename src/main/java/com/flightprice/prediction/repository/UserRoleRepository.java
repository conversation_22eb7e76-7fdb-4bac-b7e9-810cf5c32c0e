package com.flightprice.prediction.repository;

import com.flightprice.prediction.entity.UserRole;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 用户角色关联数据访问层
 */
@Repository
public interface UserRoleRepository extends JpaRepository<UserRole, Long>, JpaSpecificationExecutor<UserRole> {
    
    /**
     * 根据用户ID查找用户角色关联列表
     *
     * @param userId 用户ID
     * @return 用户角色关联列表
     */
    List<UserRole> findByUserId(Long userId);
    
    /**
     * 根据角色ID查找用户角色关联列表
     *
     * @param roleId 角色ID
     * @return 用户角色关联列表
     */
    List<UserRole> findByRoleId(Long roleId);
    
    /**
     * 根据用户ID删除用户角色关联
     *
     * @param userId 用户ID
     */
    void deleteByUserId(Long userId);
    
    /**
     * 根据角色ID删除用户角色关联
     *
     * @param roleId 角色ID
     */
    void deleteByRoleId(Long roleId);
} 