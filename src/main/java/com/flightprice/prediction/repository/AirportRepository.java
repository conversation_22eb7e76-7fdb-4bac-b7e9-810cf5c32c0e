package com.flightprice.prediction.repository;

import com.flightprice.prediction.entity.Airport;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * 机场数据访问层
 */
@Repository
public interface AirportRepository extends JpaRepository<Airport, Long> {
    
    /**
     * 根据IATA代码查找机场
     *
     * @param iataCode IATA代码
     * @return 机场
     */
    Optional<Airport> findByIataCode(String iataCode);
    
    /**
     * 根据ICAO代码查找机场
     *
     * @param icaoCode ICAO代码
     * @return 机场
     */
    Optional<Airport> findByIcaoCode(String icaoCode);
    
    /**
     * 检查IATA代码是否存在
     *
     * @param iataCode IATA代码
     * @return 是否存在
     */
    boolean existsByIataCode(String iataCode);
    
    /**
     * 检查ICAO代码是否存在
     *
     * @param icaoCode ICAO代码
     * @return 是否存在
     */
    boolean existsByIcaoCode(String icaoCode);
    
    /**
     * 根据城市查找机场列表
     *
     * @param city 城市
     * @return 机场列表
     */
    List<Airport> findByCity(String city);
    
    /**
     * 根据国家查找机场列表
     *
     * @param country 国家
     * @return 机场列表
     */
    List<Airport> findByCountry(String country);
    
    /**
     * 根据关键字查询机场
     *
     * @param keyword 关键字
     * @param pageable 分页参数
     * @return 机场分页列表
     */
    @Query("SELECT a FROM Airport a WHERE " +
           "(:keyword IS NULL OR " +
           "LOWER(a.iataCode) LIKE LOWER(CONCAT('%', :keyword, '%')) OR " +
           "LOWER(a.icaoCode) LIKE LOWER(CONCAT('%', :keyword, '%')) OR " +
           "LOWER(a.name) LIKE LOWER(CONCAT('%', :keyword, '%')) OR " +
           "LOWER(a.englishName) LIKE LOWER(CONCAT('%', :keyword, '%')) OR " +
           "LOWER(a.city) LIKE LOWER(CONCAT('%', :keyword, '%')) OR " +
           "LOWER(a.country) LIKE LOWER(CONCAT('%', :keyword, '%')))")
    Page<Airport> findByKeyword(@Param("keyword") String keyword, Pageable pageable);
} 