package com.flightprice.prediction.repository;

import com.flightprice.prediction.entity.RolePermission;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 角色权限关联数据访问层
 */
@Repository
public interface RolePermissionRepository extends JpaRepository<RolePermission, Long>, JpaSpecificationExecutor<RolePermission> {
    
    /**
     * 根据角色ID查找角色权限关联列表
     *
     * @param roleId 角色ID
     * @return 角色权限关联列表
     */
    List<RolePermission> findByRoleId(Long roleId);
    
    /**
     * 根据权限ID查找角色权限关联列表
     *
     * @param permissionId 权限ID
     * @return 角色权限关联列表
     */
    List<RolePermission> findByPermissionId(Long permissionId);
    
    /**
     * 根据角色ID删除角色权限关联
     *
     * @param roleId 角色ID
     */
    void deleteByRoleId(Long roleId);
    
    /**
     * 根据权限ID删除角色权限关联
     *
     * @param permissionId 权限ID
     */
    void deleteByPermissionId(Long permissionId);
} 