package com.flightprice.prediction.repository;

import com.flightprice.prediction.entity.ModelTrainingHistory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 模型训练历史数据访问层
 */
@Repository
public interface ModelTrainingHistoryRepository extends JpaRepository<ModelTrainingHistory, Long> {
    
    /**
     * 根据模型ID查询训练历史
     *
     * @param modelId 模型ID
     * @return 训练历史列表
     */
    List<ModelTrainingHistory> findByModelIdOrderByCreatedAtDesc(Long modelId);
    
    /**
     * 根据模型ID分页查询训练历史
     *
     * @param modelId 模型ID
     * @param pageable 分页参数
     * @return 训练历史分页列表
     */
    Page<ModelTrainingHistory> findByModelId(Long modelId, Pageable pageable);
    
    /**
     * 根据训练状态查询训练历史
     *
     * @param status 训练状态
     * @return 训练历史列表
     */
    List<ModelTrainingHistory> findByStatus(String status);
    
    /**
     * 根据训练者ID查询训练历史
     *
     * @param trainedBy 训练者ID
     * @return 训练历史列表
     */
    List<ModelTrainingHistory> findByTrainedBy(Long trainedBy);
    
    /**
     * 根据模型ID查询最近一次成功的训练历史
     *
     * @param modelId 模型ID
     * @return 最近一次成功的训练历史
     */
    ModelTrainingHistory findTopByModelIdAndStatusOrderByEndTimeDesc(Long modelId, String status);
    
    /**
     * 根据模型ID查询最近一次训练历史
     *
     * @param modelId 模型ID
     * @return 最近一次训练历史
     */
    ModelTrainingHistory findTopByModelIdOrderByCreatedAtDesc(Long modelId);
    
    /**
     * 统计模型的训练次数
     *
     * @param modelId 模型ID
     * @return 训练次数
     */
    long countByModelId(Long modelId);
    
    /**
     * 根据模型ID和状态统计训练次数
     *
     * @param modelId 模型ID
     * @param status 训练状态
     * @return 训练次数
     */
    long countByModelIdAndStatus(Long modelId, String status);
    
    /**
     * 查询模型的平均准确率
     *
     * @param modelId 模型ID
     * @return 平均准确率
     */
    @Query("SELECT AVG(h.accuracy) FROM ModelTrainingHistory h WHERE h.modelId = :modelId AND h.status = 'SUCCESS'")
    Double getAverageAccuracyByModelId(@Param("modelId") Long modelId);
    
    /**
     * 查询模型的最高准确率
     *
     * @param modelId 模型ID
     * @return 最高准确率
     */
    @Query("SELECT MAX(h.accuracy) FROM ModelTrainingHistory h WHERE h.modelId = :modelId AND h.status = 'SUCCESS'")
    Double getMaxAccuracyByModelId(@Param("modelId") Long modelId);
    
    /**
     * 查询模型的最低准确率
     *
     * @param modelId 模型ID
     * @return 最低准确率
     */
    @Query("SELECT MIN(h.accuracy) FROM ModelTrainingHistory h WHERE h.modelId = :modelId AND h.status = 'SUCCESS'")
    Double getMinAccuracyByModelId(@Param("modelId") Long modelId);
} 