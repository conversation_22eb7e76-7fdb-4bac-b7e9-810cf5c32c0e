package com.flightprice.prediction.repository;

import com.flightprice.prediction.entity.Permission;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * 权限数据访问层
 */
@Repository
public interface PermissionRepository extends JpaRepository<Permission, Long>, JpaSpecificationExecutor<Permission> {

    /**
     * 根据权限编码查找权限
     *
     * @param code 权限编码
     * @return 权限对象
     */
    Optional<Permission> findByCode(String code);

    /**
     * 根据权限类型查找权限列表
     *
     * @param category 权限类型
     * @return 权限列表
     */
    List<Permission> findByCategory(String category);

    /**
     * 根据父权限ID查找权限列表
     *
     * @param parentId 父权限ID
     * @return 权限列表
     */
    List<Permission> findByParentId(Long parentId);

    /**
     * 检查权限编码是否存在
     *
     * @param code 权限编码
     * @return 是否存在
     */
    boolean existsByCode(String code);
}