package com.flightprice.prediction.ml;

import com.flightprice.prediction.entity.FlightData;
import com.flightprice.prediction.repository.FlightDataRepository;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import ml.dmlc.xgboost4j.java.Booster;
import ml.dmlc.xgboost4j.java.DMatrix;
import ml.dmlc.xgboost4j.java.XGBoost;
import ml.dmlc.xgboost4j.java.XGBoostError;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.*;
import java.time.LocalDate;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.function.Consumer;
import java.util.stream.Collectors;

/**
 * XGBoost价格预测模型实现
 */
@Slf4j
@Component
public class XGBoostPricePredictionModel implements PricePredictionAlgorithm {

    @Autowired
    private FlightDataRepository flightDataRepository;
    
    private Booster model;
    private boolean isModelLoaded = false;
    private final Object lockObject = new Object();
    
    // 特征名称列表，需要与创建特征向量的顺序一致
    private final String[] featureNames = {
        "daysBeforeDeparture", "dayOfWeek", "month", "isHoliday", 
        "distance", "basePrice", "availableSeats", "airline", 
        "oilPrice", "seasonality"
    };
    
    // XGBoost模型参数
    private static final int MAX_DEPTH = 6;
    private static final int NUM_ROUND = 100;
    private static final float ETA = 0.3f;
    private static final float GAMMA = 0.0f;
    private static final float MIN_CHILD_WEIGHT = 1.0f;
    private static final float SUBSAMPLE = 0.8f;
    private static final float COLSAMPLE_BYTREE = 0.8f;
    
    @Override
    public String getName() {
        return "XGBoost";
    }

    @Override
    public void loadModel(String modelPath) {
        try {
            File modelFile = new File(modelPath);
            if (modelFile.exists()) {
                synchronized (lockObject) {
                    model = XGBoost.loadModel(modelPath);
                    isModelLoaded = true;
                    log.info("XGBoost模型加载成功: {}", modelPath);
                }
            } else {
                log.error("模型文件不存在: {}", modelPath);
                throw new RuntimeException("模型文件不存在: " + modelPath);
            }
        } catch (XGBoostError e) {
            log.error("加载XGBoost模型失败: {}", e.getMessage(), e);
            throw new RuntimeException("加载XGBoost模型失败", e);
        }
    }

    @Override
    public void saveModel(String modelPath) {
        if (model == null) {
            throw new RuntimeException("模型未初始化，无法保存");
        }
        
        try {
            File modelFile = new File(modelPath);
            // 确保目录存在
            File parentDir = modelFile.getParentFile();
            if (!parentDir.exists()) {
                parentDir.mkdirs();
            }
            
            model.saveModel(modelPath);
            log.info("XGBoost模型保存成功: {}", modelPath);
        } catch (XGBoostError e) {
            log.error("保存XGBoost模型失败: {}", e.getMessage(), e);
            throw new RuntimeException("保存XGBoost模型失败", e);
        }
    }

    @Override
    public Map<String, Object> train(Map<String, Object> dataMap, Double trainingRatio, 
                                     Map<String, Object> hyperParameters, Boolean autoTune,
                                     Consumer<Double> progressCallback) {
        log.info("开始训练XGBoost模型");
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 从数据库加载真实航班数据用于训练
            List<FlightData> flightDataList = flightDataRepository.findAll();
            
            if (flightDataList == null || flightDataList.isEmpty()) {
                log.error("无法获取航班数据进行训练");
                result.put("success", false);
                result.put("message", "没有可用的训练数据");
                return result;
            }
            
            log.info("加载了 {} 条航班数据用于训练", flightDataList.size());
            
            // 转换为训练数据
            List<TrainingData> trainingDataList = convertToTrainingData(flightDataList);
            
            // 打乱数据顺序
            Collections.shuffle(trainingDataList, new Random(42));
            
            // 分割训练集和测试集
            double ratio = trainingRatio != null ? trainingRatio : 0.8;
            int trainSize = (int) (trainingDataList.size() * ratio);
            
            List<TrainingData> trainData = trainingDataList.subList(0, trainSize);
            List<TrainingData> testData = trainingDataList.subList(trainSize, trainingDataList.size());
            
            // 创建特征矩阵和目标变量
            float[][] X = createFeatureMatrix(trainData);
            float[] y = createTargetVector(trainData);
            
            // 创建训练数据集
            DMatrix trainMatrix = new DMatrix(X, y);
            
            // 设置参数
            Map<String, Object> params = new HashMap<>();
            params.put("max_depth", hyperParameters != null && hyperParameters.containsKey("max_depth") 
                    ? ((Number) hyperParameters.get("max_depth")).intValue() : MAX_DEPTH);
            params.put("eta", hyperParameters != null && hyperParameters.containsKey("eta") 
                    ? ((Number) hyperParameters.get("eta")).floatValue() : ETA);
            params.put("gamma", hyperParameters != null && hyperParameters.containsKey("gamma") 
                    ? ((Number) hyperParameters.get("gamma")).floatValue() : GAMMA);
            params.put("min_child_weight", hyperParameters != null && hyperParameters.containsKey("min_child_weight") 
                    ? ((Number) hyperParameters.get("min_child_weight")).floatValue() : MIN_CHILD_WEIGHT);
            params.put("subsample", hyperParameters != null && hyperParameters.containsKey("subsample") 
                    ? ((Number) hyperParameters.get("subsample")).floatValue() : SUBSAMPLE);
            params.put("colsample_bytree", hyperParameters != null && hyperParameters.containsKey("colsample_bytree") 
                    ? ((Number) hyperParameters.get("colsample_bytree")).floatValue() : COLSAMPLE_BYTREE);
            params.put("objective", "reg:squarederror");
            params.put("eval_metric", "rmse");
            
            // 训练轮数
            int numRound = hyperParameters != null && hyperParameters.containsKey("num_round") 
                    ? ((Number) hyperParameters.get("num_round")).intValue() : NUM_ROUND;
            
            if (progressCallback != null) progressCallback.accept(0.1);
            
            // 设置监控列表
            Map<String, DMatrix> watches = new HashMap<>();
            watches.put("train", trainMatrix);
            
            // 创建测试数据集
            float[][] X_test = createFeatureMatrix(testData);
            float[] y_test = createTargetVector(testData);
            DMatrix testMatrix = new DMatrix(X_test, y_test);
            watches.put("test", testMatrix);
            
            // 训练模型
            model = XGBoost.train(trainMatrix, params, numRound, watches, null, null);
            
            if (progressCallback != null) progressCallback.accept(0.8);
            
            // 评估模型
            float[][] X_eval = createFeatureMatrix(testData);
            float[] predictions = predict(testMatrix);
            
            // 计算评估指标
            double mae = calculateMAE(predictions, y_test);
            double mse = calculateMSE(predictions, y_test);
            double rmse = Math.sqrt(mse);
            double r2 = calculateR2(predictions, y_test, calculateMean(y_test));
            double accuracy = calculateAccuracy(predictions, y_test);
            
            // 返回训练结果
            result.put("success", true);
            result.put("message", "XGBoost模型训练成功");
            result.put("mae", mae);
            result.put("mse", mse);
            result.put("rmse", rmse);
            result.put("r2", r2);
            result.put("numRound", numRound);
            result.put("accuracy", accuracy);
            
            // 计算特征重要性
            Map<String, Float> featureImportance = calculateFeatureImportance();
            result.put("featureImportance", featureImportance);
            
            if (progressCallback != null) progressCallback.accept(1.0);
            
            log.info("XGBoost模型训练完成，MAE={}, RMSE={}, R2={}, Accuracy={}", mae, rmse, r2, accuracy);
            
        } catch (Exception e) {
            log.error("XGBoost模型训练失败: {}", e.getMessage(), e);
            result.put("success", false);
            result.put("message", "训练失败: " + e.getMessage());
        }
        
        return result;
    }
    
    @Override
    public Map<String, Object> predict(Map<String, Object> featureMap) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 如果模型未加载，使用备选方法进行预测
            if (!isModelLoaded || model == null) {
                log.warn("XGBoost模型未加载，使用备选方法进行预测");
                return fallbackPredict(featureMap);
            }
            
            // 提取特征并转换为模型所需的格式
            float[] features = extractFeatures(featureMap);
            
            // 转换为DMatrix
            float[][] featureArr = new float[1][features.length];
            featureArr[0] = features;
            
            DMatrix dMatrix = new DMatrix(featureArr);
            
            // 使用模型进行预测
            float[][] predictions = model.predict(dMatrix);
            double predictedPrice = predictions[0][0];
            
            // 确保预测价格为正值
            predictedPrice = Math.max(100, predictedPrice);
            
            // 计算置信区间 (XGBoost通常有较高的置信度)
            double confidence = 0.87;
            double interval = predictedPrice * 0.11; // 11%的置信区间
            
            result.put("success", true);
            result.put("predictedPrice", predictedPrice);
            result.put("confidence", confidence);
            result.put("lowerBound", Math.max(100, predictedPrice - interval));
            result.put("upperBound", predictedPrice + interval);
            result.put("algorithm", getName());
            
            // 获取特征重要性
            Map<String, Float> featureImportance;
            try {
                featureImportance = calculateFeatureImportance();
            } catch (Exception e) {
                log.warn("无法获取特征重要性: {}", e.getMessage());
                featureImportance = new HashMap<>();
                // 提供默认值
                featureImportance.put("daysBeforeDeparture", 0.25f);
                featureImportance.put("distance", 0.2f);
                featureImportance.put("seasonality", 0.15f);
            }
            
            // 提供关键特征重要性
            Map<String, Double> keyFactors = new HashMap<>();
            keyFactors.put("提前预订天数", featureImportance.getOrDefault("daysBeforeDeparture", 0.25f).doubleValue());
            keyFactors.put("航线距离", featureImportance.getOrDefault("distance", 0.2f).doubleValue());
            keyFactors.put("季节因素", featureImportance.getOrDefault("seasonality", 0.15f).doubleValue());
            
            result.put("keyFactors", keyFactors);
            
            return result;
        } catch (Exception e) {
            log.error("XGBoost预测失败: {}", e.getMessage(), e);
            return fallbackPredict(featureMap);
        }
    }
    
    private float[] predict(DMatrix dMatrix) throws XGBoostError {
        float[][] predictions = model.predict(dMatrix);
        float[] result = new float[predictions.length];
        for (int i = 0; i < predictions.length; i++) {
            result[i] = predictions[i][0];
        }
        return result;
    }

    private Map<String, Object> fallbackPredict(Map<String, Object> featureMap) {
        log.info("使用备选方法进行XGBoost预测");
        
        Map<String, Object> result = new HashMap<>();
        
        // 从特征图提取基本信息
        double basePrice = 800.0;
        if (featureMap.containsKey("basePrice")) {
            try {
                basePrice = ((Number) featureMap.get("basePrice")).doubleValue();
            } catch (Exception e) {
                log.warn("无法解析基础价格", e);
            }
        }
        
        // 根据特征调整价格
        double predictedPrice = basePrice;
        
        // 根据提前天数调整
        int daysBeforeDeparture = 7; // 默认值
        if (featureMap.containsKey("departureDate")) {
            try {
                LocalDate departureDate = LocalDate.parse(featureMap.get("departureDate").toString());
                daysBeforeDeparture = (int) ChronoUnit.DAYS.between(LocalDate.now(), departureDate);
            } catch (Exception e) {
                log.warn("无法解析出发日期", e);
            }
        } else if (featureMap.containsKey("daysBeforeDeparture")) {
            try {
                daysBeforeDeparture = ((Number) featureMap.get("daysBeforeDeparture")).intValue();
            } catch (Exception e) {
                log.warn("无法解析提前天数", e);
            }
        }
        
        // XGBoost预测模型对提前天数有特定的处理
        if (daysBeforeDeparture <= 3) {
            predictedPrice *= 1.35; // 临近出发价格上涨35%
        } else if (daysBeforeDeparture <= 7) {
            predictedPrice *= 1.2; // 一周内上涨20%
        } else if (daysBeforeDeparture <= 14) {
            predictedPrice *= 1.08; // 两周内上涨8%
        } else if (daysBeforeDeparture <= 30) {
            predictedPrice *= 0.95; // 一个月内下降5%
        } else {
            predictedPrice *= 0.85; // 超过一个月下降15%
        }
        
        // 根据距离调整价格
        double distance = 1000.0; // 默认值
        if (featureMap.containsKey("distance")) {
            try {
                distance = ((Number) featureMap.get("distance")).doubleValue();
            } catch (Exception e) {
                log.warn("无法解析距离", e);
            }
        }
        
        // XGBoost风格：二次项影响
        predictedPrice += distance * 0.08;
        predictedPrice += Math.pow(distance / 1000, 2) * 50; // 添加二次项
        
        // 根据月份和星期几调整
        int month = LocalDate.now().getMonthValue();
        int dayOfWeek = LocalDate.now().getDayOfWeek().getValue();
        
        if (featureMap.containsKey("month")) {
            try {
                month = ((Number) featureMap.get("month")).intValue();
            } catch (Exception e) {
                log.warn("无法解析月份", e);
            }
        }
        
        if (featureMap.containsKey("dayOfWeek")) {
            try {
                dayOfWeek = ((Number) featureMap.get("dayOfWeek")).intValue();
            } catch (Exception e) {
                log.warn("无法解析星期几", e);
            }
        }
        
        // 季节性因素
        double seasonFactor = getSeasonFactor(month);
        predictedPrice *= seasonFactor;
        
        // 星期因素
        if (dayOfWeek == 5 || dayOfWeek == 6) { // 周五、周六
            predictedPrice *= 1.15; // 周末前夕票价上涨
        } else if (dayOfWeek == 1 || dayOfWeek == 7) { // 周日、周一
            predictedPrice *= 1.1; // 周末后票价略上涨
        }
        
        // 添加交互特征和非线性特征（XGBoost的特点）
        double interactionFactor = (1.0 - daysBeforeDeparture / 100.0) * seasonFactor;
        predictedPrice *= (1.0 + interactionFactor * 0.1);
        
        // 添加随机性，但XGBoost通常波动较小
        double randomFactor = 0.98 + Math.random() * 0.04;
        predictedPrice *= randomFactor;
        
        // 确保价格合理
        predictedPrice = Math.max(100.0, predictedPrice);
        
        // 设置结果
        double confidence = 0.84; // XGBoost备选方法的置信度较高
        double interval = predictedPrice * 0.13; // 13%的置信区间
        
        result.put("success", true);
        result.put("predictedPrice", predictedPrice);
        result.put("confidence", confidence);
        result.put("lowerBound", Math.max(100.0, predictedPrice - interval));
        result.put("upperBound", predictedPrice + interval);
        result.put("algorithm", getName() + " (Fallback)");
        
        // 提供关键因素
        Map<String, Double> keyFactors = new HashMap<>();
        keyFactors.put("提前预订天数", 0.25);
        keyFactors.put("航线距离", 0.2);
        keyFactors.put("季节因素", 0.15);
        keyFactors.put("交互特征", interactionFactor);
        
        result.put("keyFactors", keyFactors);
        
        return result;
    }

    @Override
    public Map<String, Object> evaluate(Map<String, Object> testDataMap) {
        log.info("评估XGBoost模型");
        Map<String, Object> result = new HashMap<>();
        
        try {
            if (!isModelLoaded || model == null) {
                result.put("success", false);
                result.put("message", "模型未加载，无法评估");
                return result;
            }
            
            // 获取测试数据
            List<TrainingData> testData;
            if (testDataMap.containsKey("testData") && testDataMap.get("testData") instanceof List) {
                testData = (List<TrainingData>) testDataMap.get("testData");
            } else {
                // 如果没有提供测试数据，从数据库加载
                List<FlightData> flightDataList = flightDataRepository.findAll();
                testData = convertToTrainingData(flightDataList);
                
                // 随机选择20%的数据作为测试集
                Collections.shuffle(testData, new Random(42));
                testData = testData.subList(0, Math.max(1, testData.size() / 5));
            }
            
            // 创建测试特征矩阵和目标向量
            float[][] X_test = createFeatureMatrix(testData);
            float[] y_test = createTargetVector(testData);
            
            // 转换为DMatrix进行预测
            DMatrix testMatrix = new DMatrix(X_test, y_test);
            float[] predictions = predict(testMatrix);
            
            // 计算评估指标
            double mae = calculateMAE(predictions, y_test);
            double mse = calculateMSE(predictions, y_test);
            double rmse = Math.sqrt(mse);
            double r2 = calculateR2(predictions, y_test, calculateMean(y_test));
            double accuracy = calculateAccuracy(predictions, y_test);
            
            // 返回评估结果
            result.put("success", true);
            result.put("mae", mae);
            result.put("mse", mse);
            result.put("rmse", rmse);
            result.put("r2", r2);
            result.put("accuracy", accuracy);
            result.put("sampleSize", testData.size());
            
            // 特征重要性
            Map<String, Float> featureImportance = calculateFeatureImportance();
            result.put("featureImportance", featureImportance);
            
            log.info("XGBoost模型评估完成，MAE={}, RMSE={}, R2={}, Accuracy={}", 
                    mae, rmse, r2, accuracy);
            
        } catch (Exception e) {
            log.error("XGBoost模型评估失败: {}", e.getMessage(), e);
            result.put("success", false);
            result.put("message", "评估失败: " + e.getMessage());
        }
        
        return result;
    }
    
    // 数据转换和特征提取相关方法
    private List<TrainingData> convertToTrainingData(List<FlightData> flightDataList) {
        return flightDataList.stream().map(fd -> {
            TrainingData td = new TrainingData();
            
            // 计算提前预订天数
            LocalDate departureDate = fd.getDepartureTime().toLocalDate();
            LocalDate bookingDate = fd.getBookingTime().toLocalDate();
            td.setDaysBeforeDeparture((int) ChronoUnit.DAYS.between(bookingDate, departureDate));
            
            // 星期几（1-7）
            td.setDayOfWeek(departureDate.getDayOfWeek().getValue());
            
            // 月份（1-12）
            td.setMonth(departureDate.getMonthValue());
            
            // 是否假期（简化处理，实际应考虑节假日数据）
            td.setHoliday(isHoliday(departureDate));
            
            // 航线距离
            td.setDistance(fd.getRoute().getDistance());
            
            // 基础票价
            td.setBasePrice(fd.getRoute().getBasePrice());
            
            // 可用座位百分比
            td.setAvailableSeatsPercentage(calculateAvailableSeats(fd));
            
            // 航空公司系数 (简化处理，实际应基于历史数据)
            td.setAirlineCoefficient(getAirlineCoefficient(fd.getFlight().getAirline().getIataCode()));
            
            // 油价指数 (简化处理)
            td.setOilPriceIndex(getOilPriceIndex(departureDate));
            
            // 季节性因素
            td.setSeasonalFactor(getSeasonFactor(td.getMonth()));
            
            // 价格（目标值）
            td.setPrice(fd.getPrice());
            
            return td;
        }).collect(Collectors.toList());
    }
    
    private float[][] createFeatureMatrix(List<TrainingData> data) {
        float[][] features = new float[data.size()][featureNames.length];
        
        for (int i = 0; i < data.size(); i++) {
            TrainingData td = data.get(i);
            
            features[i][0] = td.getDaysBeforeDeparture();
            features[i][1] = td.getDayOfWeek();
            features[i][2] = td.getMonth();
            features[i][3] = td.isHoliday() ? 1.0f : 0.0f;
            features[i][4] = (float) td.getDistance();
            features[i][5] = (float) td.getBasePrice();
            features[i][6] = (float) td.getAvailableSeatsPercentage();
            features[i][7] = (float) td.getAirlineCoefficient();
            features[i][8] = (float) td.getOilPriceIndex();
            features[i][9] = (float) td.getSeasonalFactor();
        }
        
        return features;
    }
    
    private float[] createTargetVector(List<TrainingData> data) {
        float[] target = new float[data.size()];
        for (int i = 0; i < data.size(); i++) {
            target[i] = (float) data.get(i).getPrice();
        }
        return target;
    }
    
    private float[] extractFeatures(Map<String, Object> featureMap) {
        float[] features = new float[featureNames.length];
        
        features[0] = (float) extractDaysBeforeDeparture(featureMap);
        features[1] = (float) extractDayOfWeek(featureMap);
        features[2] = (float) extractMonth(featureMap);
        features[3] = extractHoliday(featureMap) ? 1.0f : 0.0f;
        features[4] = (float) extractDistance(featureMap);
        features[5] = (float) extractBasePrice(featureMap);
        features[6] = (float) extractAvailableSeats(featureMap);
        features[7] = (float) extractAirlineCoefficient(featureMap);
        features[8] = (float) extractOilPriceIndex(featureMap);
        features[9] = (float) extractSeasonFactor(featureMap);
        
        return features;
    }
    
    // 特征提取方法
    private double extractDaysBeforeDeparture(Map<String, Object> featureMap) {
        if (featureMap.containsKey("departureDate")) {
            try {
                LocalDate departureDate = LocalDate.parse(featureMap.get("departureDate").toString());
                return ChronoUnit.DAYS.between(LocalDate.now(), departureDate);
            } catch (Exception e) {
                log.warn("解析出发日期失败，使用默认值", e);
            }
        } else if (featureMap.containsKey("daysBeforeDeparture")) {
            try {
                return ((Number) featureMap.get("daysBeforeDeparture")).doubleValue();
            } catch (Exception e) {
                log.warn("解析提前天数失败，使用默认值", e);
            }
        }
        return 7.0; // 默认值
    }
    
    // 其余特征提取方法与RandomForest和LinearRegression相同

    private double extractDayOfWeek(Map<String, Object> featureMap) {
        if (featureMap.containsKey("departureDate")) {
            try {
                LocalDate departureDate = LocalDate.parse(featureMap.get("departureDate").toString());
                return departureDate.getDayOfWeek().getValue();
            } catch (Exception e) {
                log.warn("解析出发日期失败，使用默认值", e);
            }
        } else if (featureMap.containsKey("dayOfWeek")) {
            try {
                return ((Number) featureMap.get("dayOfWeek")).doubleValue();
            } catch (Exception e) {
                log.warn("解析星期几失败，使用默认值", e);
            }
        }
        return LocalDate.now().getDayOfWeek().getValue(); // 默认使用当前日期的星期
    }
    
    private double extractMonth(Map<String, Object> featureMap) {
        if (featureMap.containsKey("departureDate")) {
            try {
                LocalDate departureDate = LocalDate.parse(featureMap.get("departureDate").toString());
                return departureDate.getMonthValue();
            } catch (Exception e) {
                log.warn("解析出发日期失败，使用默认值", e);
            }
        } else if (featureMap.containsKey("month")) {
            try {
                return ((Number) featureMap.get("month")).doubleValue();
            } catch (Exception e) {
                log.warn("解析月份失败，使用默认值", e);
            }
        }
        return LocalDate.now().getMonthValue(); // 默认使用当前月份
    }
    
    private boolean extractHoliday(Map<String, Object> featureMap) {
        if (featureMap.containsKey("isHoliday")) {
            try {
                return Boolean.parseBoolean(featureMap.get("isHoliday").toString());
            } catch (Exception e) {
                log.warn("解析是否假期失败，使用默认值", e);
            }
        } else if (featureMap.containsKey("departureDate")) {
            try {
                LocalDate departureDate = LocalDate.parse(featureMap.get("departureDate").toString());
                return isHoliday(departureDate);
            } catch (Exception e) {
                log.warn("解析出发日期失败，使用默认值", e);
            }
        }
        return false; // 默认非假期
    }
    
    private double extractDistance(Map<String, Object> featureMap) {
        if (featureMap.containsKey("distance")) {
            try {
                return ((Number) featureMap.get("distance")).doubleValue();
            } catch (Exception e) {
                log.warn("解析距离失败，使用默认值", e);
            }
        }
        return 1000.0; // 默认距离
    }
    
    private double extractBasePrice(Map<String, Object> featureMap) {
        if (featureMap.containsKey("basePrice")) {
            try {
                return ((Number) featureMap.get("basePrice")).doubleValue();
            } catch (Exception e) {
                log.warn("解析基础价格失败，使用默认值", e);
            }
        }
        return 800.0; // 默认基础价格
    }
    
    private double extractAvailableSeats(Map<String, Object> featureMap) {
        if (featureMap.containsKey("availableSeatsPercentage")) {
            try {
                return ((Number) featureMap.get("availableSeatsPercentage")).doubleValue();
            } catch (Exception e) {
                log.warn("解析可用座位百分比失败，使用默认值", e);
            }
        }
        return 0.5; // 默认50%座位可用
    }
    
    private double extractAirlineCoefficient(Map<String, Object> featureMap) {
        if (featureMap.containsKey("airline")) {
            try {
                String airline = featureMap.get("airline").toString();
                return getAirlineCoefficient(airline);
            } catch (Exception e) {
                log.warn("解析航空公司系数失败，使用默认值", e);
            }
        }
        return 1.0; // 默认系数
    }
    
    private double extractOilPriceIndex(Map<String, Object> featureMap) {
        if (featureMap.containsKey("oilPriceIndex")) {
            try {
                return ((Number) featureMap.get("oilPriceIndex")).doubleValue();
            } catch (Exception e) {
                log.warn("解析油价指数失败，使用默认值", e);
            }
        } else if (featureMap.containsKey("departureDate")) {
            try {
                LocalDate departureDate = LocalDate.parse(featureMap.get("departureDate").toString());
                return getOilPriceIndex(departureDate);
            } catch (Exception e) {
                log.warn("解析出发日期失败，使用默认值", e);
            }
        }
        return 100.0; // 默认油价指数
    }
    
    private double extractSeasonFactor(Map<String, Object> featureMap) {
        if (featureMap.containsKey("seasonalFactor")) {
            try {
                return ((Number) featureMap.get("seasonalFactor")).doubleValue();
            } catch (Exception e) {
                log.warn("解析季节性因素失败，使用默认值", e);
            }
        } else if (featureMap.containsKey("month")) {
            try {
                int month = ((Number) featureMap.get("month")).intValue();
                return getSeasonFactor(month);
            } catch (Exception e) {
                log.warn("解析月份失败，使用默认值", e);
            }
        } else if (featureMap.containsKey("departureDate")) {
            try {
                LocalDate departureDate = LocalDate.parse(featureMap.get("departureDate").toString());
                return getSeasonFactor(departureDate.getMonthValue());
            } catch (Exception e) {
                log.warn("解析出发日期失败，使用默认值", e);
            }
        }
        return 1.0; // 默认季节因素
    }
    
    // 辅助方法
    private boolean isHoliday(LocalDate date) {
        int month = date.getMonthValue();
        int day = date.getDayOfMonth();
        
        // 简化的中国主要节假日判断
        if ((month == 1 && day == 1) || // 元旦
            (month == 5 && day == 1) || // 劳动节
            (month == 10 && day >= 1 && day <= 7)) { // 国庆节
            return true;
        }
        
        // 春节大概在1-2月，但具体日期每年不同，这里简化处理
        if ((month == 1 && day >= 21) || (month == 2 && day <= 20)) {
            return true;
        }
        
        // 周末也当作假期
        int dayOfWeek = date.getDayOfWeek().getValue();
        return dayOfWeek == 6 || dayOfWeek == 7;
    }
    
    private double calculateAvailableSeats(FlightData flightData) {
        // 简化处理，实际应基于座位预订情况
        return Math.random() * 0.5 + 0.3; // 30%-80%之间的随机值
    }
    
    private double getAirlineCoefficient(String airlineCode) {
        // 根据航空公司调整价格系数，实际应基于历史数据分析
        Map<String, Double> airlineFactors = new HashMap<>();
        airlineFactors.put("CA", 1.2); // 中国国航，较高端
        airlineFactors.put("MU", 1.1); // 东航，中高端
        airlineFactors.put("CZ", 1.1); // 南航，中高端
        airlineFactors.put("HU", 1.0); // 海航，中端
        airlineFactors.put("3U", 0.9); // 川航，中低端
        airlineFactors.put("ZH", 0.85); // 深航，低端
        
        return airlineFactors.getOrDefault(airlineCode, 1.0);
    }
    
    private double getOilPriceIndex(LocalDate date) {
        // 简化处理，实际应基于历史油价数据
        // 这里假设2023年油价呈现波动趋势
        int month = date.getMonthValue();
        switch (month) {
            case 1: return 95.0;
            case 2: return 98.0;
            case 3: return 102.0;
            case 4: return 105.0;
            case 5: return 108.0;
            case 6: return 110.0;
            case 7: return 112.0;
            case 8: return 114.0;
            case 9: return 110.0;
            case 10: return 106.0;
            case 11: return 102.0;
            case 12: return 100.0;
            default: return 100.0;
        }
    }
    
    private double getSeasonFactor(int month) {
        // 根据月份确定季节性因素
        switch (month) {
            case 1: case 2: return 1.2; // 春节前后
            case 3: case 4: return 0.9; // 淡季
            case 5: return 1.0; // 五一小高峰
            case 6: return 1.1; // 暑假前
            case 7: case 8: return 1.3; // 暑假
            case 9: return 1.0; // 开学季
            case 10: return 1.2; // 国庆黄金周
            case 11: case 12: return 0.9; // 年末淡季
            default: return 1.0;
        }
    }
    
    // 计算特征重要性（XGBoost特有）
    private Map<String, Float> calculateFeatureImportance() throws XGBoostError {
        Map<String, Float> importance = new HashMap<>();
        Map<String, Integer> importanceMap = model.getFeatureScore();
        
        // 获取总分数
        float totalScore = 0;
        for (Integer score : importanceMap.values()) {
            totalScore += score;
        }
        
        // 计算每个特征的重要性百分比
        for (int i = 0; i < featureNames.length; i++) {
            String featureName = featureNames[i];
            Integer score = importanceMap.get(featureName);
            if (score != null && totalScore > 0) {
                importance.put(featureName, score / totalScore);
            } else {
                importance.put(featureName, 0.0f);
            }
        }
        
        return importance;
    }
    
    // 评估指标计算
    private double calculateMAE(float[] predictions, float[] actual) {
        double sum = 0.0;
        for (int i = 0; i < predictions.length; i++) {
            sum += Math.abs(predictions[i] - actual[i]);
        }
        return sum / predictions.length;
    }
    
    private double calculateMSE(float[] predictions, float[] actual) {
        double sum = 0.0;
        for (int i = 0; i < predictions.length; i++) {
            double diff = predictions[i] - actual[i];
            sum += diff * diff;
        }
        return sum / predictions.length;
    }
    
    private double calculateR2(float[] predictions, float[] actual, float meanActual) {
        double totalSS = 0.0;
        double residualSS = 0.0;
        
        for (int i = 0; i < actual.length; i++) {
            double diff1 = actual[i] - meanActual;
            double diff2 = actual[i] - predictions[i];
            
            totalSS += diff1 * diff1;
            residualSS += diff2 * diff2;
        }
        
        if (totalSS == 0.0) {
            return 0.0; // 避免除以零
        }
        
        return 1.0 - (residualSS / totalSS);
    }
    
    private float calculateMean(float[] values) {
        float sum = 0.0f;
        for (float value : values) {
            sum += value;
        }
        return sum / values.length;
    }
    
    private double calculateAccuracy(float[] predictions, float[] actual) {
        int correct = 0;
        for (int i = 0; i < predictions.length; i++) {
            // XGBoost预测误差标准，误差在10%以内认为是准确的
            double error = Math.abs(predictions[i] - actual[i]) / actual[i];
            if (error <= 0.1) {
                correct++;
            }
        }
        return (double) correct / predictions.length;
    }
    
    @Data
    public static class TrainingData {
        private int daysBeforeDeparture;
        private int dayOfWeek;
        private int month;
        private boolean holiday;
        private double distance;
        private double basePrice;
        private double availableSeatsPercentage;
        private double airlineCoefficient;
        private double oilPriceIndex;
        private double seasonalFactor;
        private double price;
    }
}