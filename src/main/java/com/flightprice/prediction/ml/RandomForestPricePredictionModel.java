package com.flightprice.prediction.ml;

import com.flightprice.prediction.entity.FlightData;
import com.flightprice.prediction.repository.FlightDataRepository;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import smile.data.DataFrame;
import smile.data.formula.Formula;
import smile.regression.RandomForest;

import java.io.*;
import java.time.LocalDate;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.function.Consumer;
import java.util.stream.Collectors;

/**
 * 随机森林价格预测模型实现
 */
@Slf4j
@Component
public class RandomForestPricePredictionModel implements PricePredictionAlgorithm {

    @Autowired
    private FlightDataRepository flightDataRepository;
    
    private RandomForest model;
    private boolean isModelLoaded = false;
    private final Object lockObject = new Object();
    
    // 特征名称列表，需要与创建特征向量的顺序一致
    private final String[] featureNames = {
        "daysBeforeDeparture", "dayOfWeek", "month", "isHoliday", 
        "distance", "basePrice", "availableSeats", "airline", 
        "oilPrice", "seasonality"
    };
    
    // 模型参数
    private static final int NUM_TREES = 100;
    private static final int MAX_DEPTH = 20;
    private static final int MIN_NODE_SIZE = 5;
    private static final int MAX_FEATURES = 5; // 随机选择的特征数量
    
    @Override
    public String getName() {
        return "RandomForest";
    }

    @Override
    public void loadModel(String modelPath) {
        try {
            File modelFile = new File(modelPath);
            if (modelFile.exists()) {
                synchronized (lockObject) {
                    try (ObjectInputStream ois = new ObjectInputStream(new FileInputStream(modelFile))) {
                        model = (RandomForest) ois.readObject();
                        isModelLoaded = true;
                        log.info("随机森林模型加载成功: {}", modelPath);
                    }
                }
            } else {
                log.error("模型文件不存在: {}", modelPath);
                throw new RuntimeException("模型文件不存在: " + modelPath);
            }
        } catch (IOException | ClassNotFoundException e) {
            log.error("加载随机森林模型失败: {}", e.getMessage(), e);
            throw new RuntimeException("加载随机森林模型失败", e);
        }
    }

    @Override
    public void saveModel(String modelPath) {
        if (model == null) {
            throw new RuntimeException("模型未初始化，无法保存");
        }
        
        try {
            File modelFile = new File(modelPath);
            // 确保目录存在
            File parentDir = modelFile.getParentFile();
            if (!parentDir.exists()) {
                parentDir.mkdirs();
            }
            
            try (ObjectOutputStream oos = new ObjectOutputStream(new FileOutputStream(modelFile))) {
                oos.writeObject(model);
            }
            
            log.info("随机森林模型保存成功: {}", modelPath);
        } catch (IOException e) {
            log.error("保存随机森林模型失败: {}", e.getMessage(), e);
            throw new RuntimeException("保存随机森林模型失败", e);
        }
    }

    @Override
    public Map<String, Object> train(Map<String, Object> dataMap, Double trainingRatio, 
                                     Map<String, Object> hyperParameters, Boolean autoTune,
                                     Consumer<Double> progressCallback) {
        log.info("开始训练随机森林模型");
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 从数据库加载真实航班数据用于训练
            List<FlightData> flightDataList = flightDataRepository.findAll();
            
            if (flightDataList == null || flightDataList.isEmpty()) {
                log.error("无法获取航班数据进行训练");
                result.put("success", false);
                result.put("message", "没有可用的训练数据");
                return result;
            }
            
            log.info("加载了 {} 条航班数据用于训练", flightDataList.size());
            
            // 转换为训练数据
            List<TrainingData> trainingDataList = convertToTrainingData(flightDataList);
            
            // 打乱数据顺序
            Collections.shuffle(trainingDataList, new Random(42));
            
            // 分割训练集和测试集
            double ratio = trainingRatio != null ? trainingRatio : 0.8;
            int trainSize = (int) (trainingDataList.size() * ratio);
            
            List<TrainingData> trainData = trainingDataList.subList(0, trainSize);
            List<TrainingData> testData = trainingDataList.subList(trainSize, trainingDataList.size());
            
            // 创建特征矩阵和目标变量
            double[][] X = createFeatureMatrix(trainData);
            double[] y = createTargetVector(trainData);
            
            // 设置模型参数
            int numTrees = hyperParameters != null && hyperParameters.containsKey("numTrees") 
                ? ((Number) hyperParameters.get("numTrees")).intValue() : NUM_TREES;
                
            int maxDepth = hyperParameters != null && hyperParameters.containsKey("maxDepth") 
                ? ((Number) hyperParameters.get("maxDepth")).intValue() : MAX_DEPTH;
                
            int minNodeSize = hyperParameters != null && hyperParameters.containsKey("minNodeSize") 
                ? ((Number) hyperParameters.get("minNodeSize")).intValue() : MIN_NODE_SIZE;
            
            // 训练模型
            if (progressCallback != null) progressCallback.accept(0.1);
            
            model = RandomForest.fit(Formula.lhs("price"), DataFrame.of(X, featureNames), numTrees, maxDepth, minNodeSize);
            
            if (progressCallback != null) progressCallback.accept(0.8);
            
            // 评估模型
            double[][] X_test = createFeatureMatrix(testData);
            double[] y_test = createTargetVector(testData);
            double[] predictions = new double[y_test.length];
            
            for (int i = 0; i < X_test.length; i++) {
                predictions[i] = model.predict(X_test[i]);
            }
            
            // 计算评估指标
            double mae = calculateMAE(predictions, y_test);
            double mse = calculateMSE(predictions, y_test);
            double rmse = Math.sqrt(mse);
            double r2 = calculateR2(predictions, y_test, calculateMean(y_test));
            
            // 返回训练结果
            result.put("success", true);
            result.put("message", "随机森林模型训练成功");
            result.put("mae", mae);
            result.put("mse", mse);
            result.put("rmse", rmse);
            result.put("r2", r2);
            result.put("numTrees", numTrees);
            result.put("accuracy", calculateAccuracy(predictions, y_test));
            
            if (progressCallback != null) progressCallback.accept(1.0);
            
            log.info("随机森林模型训练完成，MAE={}, RMSE={}, R2={}", mae, rmse, r2);
            
        } catch (Exception e) {
            log.error("随机森林模型训练失败: {}", e.getMessage(), e);
            result.put("success", false);
            result.put("message", "训练失败: " + e.getMessage());
        }
        
        return result;
    }
    
    @Override
    public Map<String, Object> predict(Map<String, Object> featureMap) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 如果模型未加载，使用备选方法进行预测
            if (!isModelLoaded || model == null) {
                log.warn("随机森林模型未加载，使用备选方法进行预测");
                return fallbackPredict(featureMap);
            }
            
            // 提取特征并转换为模型所需的格式
            double[] features = extractFeatures(featureMap);
            
            // 使用模型进行预测
            double predictedPrice = model.predict(features);
            
            // 确保预测价格为正值
            predictedPrice = Math.max(100, predictedPrice);
            
            // 计算置信区间
            double confidence = 0.85; // 随机森林通常有较高的置信度
            double interval = predictedPrice * 0.12; // 12%的置信区间
            
            result.put("success", true);
            result.put("predictedPrice", predictedPrice);
            result.put("confidence", confidence);
            result.put("lowerBound", Math.max(100, predictedPrice - interval));
            result.put("upperBound", predictedPrice + interval);
            result.put("algorithm", getName());
            
            // 提供关键特征重要性信息
            Map<String, Double> keyFactors = new HashMap<>();
            keyFactors.put("提前预订天数", getFeatureImportance(features, 0));
            keyFactors.put("航线距离", getFeatureImportance(features, 4));
            keyFactors.put("季节因素", getFeatureImportance(features, 9));
            
            result.put("keyFactors", keyFactors);
            
            return result;
        } catch (Exception e) {
            log.error("随机森林预测失败: {}", e.getMessage(), e);
            return fallbackPredict(featureMap);
        }
    }
    
    private Map<String, Object> fallbackPredict(Map<String, Object> featureMap) {
        log.info("使用备选方法进行预测");
        
        Map<String, Object> result = new HashMap<>();
        
        // 从特征图提取基本信息
        double basePrice = 800.0;
        if (featureMap.containsKey("basePrice")) {
            try {
                basePrice = ((Number) featureMap.get("basePrice")).doubleValue();
            } catch (Exception e) {
                log.warn("无法解析基础价格", e);
            }
        }
        
        // 根据特征调整价格
        double predictedPrice = basePrice;
        
        // 根据提前天数调整
        int daysBeforeDeparture = 7; // 默认值
        if (featureMap.containsKey("departureDate")) {
            try {
                LocalDate departureDate = LocalDate.parse(featureMap.get("departureDate").toString());
                daysBeforeDeparture = (int) ChronoUnit.DAYS.between(LocalDate.now(), departureDate);
            } catch (Exception e) {
                log.warn("无法解析出发日期", e);
            }
        } else if (featureMap.containsKey("daysBeforeDeparture")) {
            try {
                daysBeforeDeparture = ((Number) featureMap.get("daysBeforeDeparture")).intValue();
            } catch (Exception e) {
                log.warn("无法解析提前天数", e);
            }
        }
        
        if (daysBeforeDeparture <= 3) {
            predictedPrice *= 1.3; // 临近出发价格上涨30%
        } else if (daysBeforeDeparture <= 7) {
            predictedPrice *= 1.15; // 一周内上涨15%
        } else if (daysBeforeDeparture <= 14) {
            predictedPrice *= 1.05; // 两周内上涨5%
        } else if (daysBeforeDeparture >= 30) {
            predictedPrice *= 0.9; // 一个月后下降10%
        }
        
        // 根据距离调整价格
        double distance = 1000.0; // 默认值
        if (featureMap.containsKey("distance")) {
            try {
                distance = ((Number) featureMap.get("distance")).doubleValue();
            } catch (Exception e) {
                log.warn("无法解析距离", e);
            }
        }
        
        predictedPrice += distance * 0.05; // 每公里增加0.05元
        
        // 根据季节性因素调整
        int month = LocalDate.now().getMonthValue();
        if (featureMap.containsKey("month")) {
            try {
                month = ((Number) featureMap.get("month")).intValue();
            } catch (Exception e) {
                log.warn("无法解析月份", e);
            }
        }
        
        if (month >= 7 && month <= 8) { // 暑假
            predictedPrice *= 1.2;
        } else if (month >= 1 && month <= 2) { // 寒假
            predictedPrice *= 1.15;
        } else if (month >= 10 && month <= 11) { // 秋季旅游高峰
            predictedPrice *= 1.1;
        }
        
        // 添加随机性，模拟随机森林的随机性
        double randomFactor = 0.95 + Math.random() * 0.1;
        predictedPrice *= randomFactor;
        
        // 确保价格合理
        predictedPrice = Math.max(100.0, predictedPrice);
        
        // 设置结果
        double confidence = 0.78; // 备选方法的置信度较低
        double interval = predictedPrice * 0.18; // 18%的置信区间
        
        result.put("success", true);
        result.put("predictedPrice", predictedPrice);
        result.put("confidence", confidence);
        result.put("lowerBound", Math.max(100.0, predictedPrice - interval));
        result.put("upperBound", predictedPrice + interval);
        result.put("algorithm", getName() + " (Fallback)");
        
        // 提供关键因素
        Map<String, Object> keyFactors = new HashMap<>();
        keyFactors.put("提前预订天数", daysBeforeDeparture);
        keyFactors.put("航线距离", distance);
        keyFactors.put("季节因素", getSeasonFactor(month));
        
        result.put("keyFactors", keyFactors);
        
        return result;
    }

    @Override
    public Map<String, Object> evaluate(Map<String, Object> testDataMap) {
        log.info("评估随机森林模型");
        Map<String, Object> result = new HashMap<>();
        
        try {
            if (!isModelLoaded || model == null) {
                result.put("success", false);
                result.put("message", "模型未加载，无法评估");
                return result;
            }
            
            // 获取测试数据
            List<TrainingData> testData;
            if (testDataMap.containsKey("testData") && testDataMap.get("testData") instanceof List) {
                testData = (List<TrainingData>) testDataMap.get("testData");
            } else {
                // 如果没有提供测试数据，从数据库加载
                List<FlightData> flightDataList = flightDataRepository.findAll();
                testData = convertToTrainingData(flightDataList);
                
                // 随机选择20%的数据作为测试集
                Collections.shuffle(testData, new Random(42));
                testData = testData.subList(0, Math.max(1, testData.size() / 5));
            }
            
            // 创建测试特征矩阵和目标向量
            double[][] X_test = createFeatureMatrix(testData);
            double[] y_test = createTargetVector(testData);
            
            // 进行预测
            double[] predictions = new double[y_test.length];
            for (int i = 0; i < X_test.length; i++) {
                predictions[i] = model.predict(X_test[i]);
            }
            
            // 计算评估指标
            double mae = calculateMAE(predictions, y_test);
            double mse = calculateMSE(predictions, y_test);
            double rmse = Math.sqrt(mse);
            double r2 = calculateR2(predictions, y_test, calculateMean(y_test));
            double accuracy = calculateAccuracy(predictions, y_test);
            
            // 返回评估结果
            result.put("success", true);
            result.put("mae", mae);
            result.put("mse", mse);
            result.put("rmse", rmse);
            result.put("r2", r2);
            result.put("accuracy", accuracy);
            result.put("sampleSize", testData.size());
            
            log.info("随机森林模型评估完成，MAE={}, RMSE={}, R2={}, Accuracy={}", 
                    mae, rmse, r2, accuracy);
            
        } catch (Exception e) {
            log.error("随机森林模型评估失败: {}", e.getMessage(), e);
            result.put("success", false);
            result.put("message", "评估失败: " + e.getMessage());
        }
        
        return result;
    }
    
    private List<TrainingData> convertToTrainingData(List<FlightData> flightDataList) {
        return flightDataList.stream().map(fd -> {
            TrainingData td = new TrainingData();
            
            // 计算提前预订天数
            LocalDate departureDate = fd.getDepartureTime().toLocalDate();
            LocalDate bookingDate = fd.getBookingTime().toLocalDate();
            td.setDaysBeforeDeparture((int) ChronoUnit.DAYS.between(bookingDate, departureDate));
            
            // 星期几（1-7）
            td.setDayOfWeek(departureDate.getDayOfWeek().getValue());
            
            // 月份（1-12）
            td.setMonth(departureDate.getMonthValue());
            
            // 是否假期（简化处理，实际应考虑节假日数据）
            td.setHoliday(isHoliday(departureDate));
            
            // 航线距离
            td.setDistance(fd.getRoute().getDistance());
            
            // 基础票价
            td.setBasePrice(fd.getRoute().getBasePrice());
            
            // 可用座位百分比
            td.setAvailableSeatsPercentage(calculateAvailableSeats(fd));
            
            // 航空公司系数 (简化处理，实际应基于历史数据)
            td.setAirlineCoefficient(getAirlineCoefficient(fd.getFlight().getAirline().getIataCode()));
            
            // 油价指数 (简化处理)
            td.setOilPriceIndex(getOilPriceIndex(departureDate));
            
            // 季节性因素
            td.setSeasonalFactor(getSeasonFactor(td.getMonth()));
            
            // 价格（目标值）
            td.setPrice(fd.getPrice());
            
            return td;
        }).collect(Collectors.toList());
    }
    
    private double[][] createFeatureMatrix(List<TrainingData> data) {
        double[][] features = new double[data.size()][featureNames.length];
        
        for (int i = 0; i < data.size(); i++) {
            TrainingData td = data.get(i);
            
            features[i][0] = td.getDaysBeforeDeparture();
            features[i][1] = td.getDayOfWeek();
            features[i][2] = td.getMonth();
            features[i][3] = td.isHoliday() ? 1.0 : 0.0;
            features[i][4] = td.getDistance();
            features[i][5] = td.getBasePrice();
            features[i][6] = td.getAvailableSeatsPercentage();
            features[i][7] = td.getAirlineCoefficient();
            features[i][8] = td.getOilPriceIndex();
            features[i][9] = td.getSeasonalFactor();
        }
        
        return features;
    }
    
    private double[] createTargetVector(List<TrainingData> data) {
        return data.stream()
                .mapToDouble(TrainingData::getPrice)
                .toArray();
    }
    
    private double[] extractFeatures(Map<String, Object> featureMap) {
        double[] features = new double[featureNames.length];
        
        // 提前预订天数
        features[0] = extractDaysBeforeDeparture(featureMap);
        
        // 星期几
        features[1] = extractDayOfWeek(featureMap);
        
        // 月份
        features[2] = extractMonth(featureMap);
        
        // 是否假期
        features[3] = extractHoliday(featureMap) ? 1.0 : 0.0;
        
        // 航线距离
        features[4] = extractDistance(featureMap);
        
        // 基础价格
        features[5] = extractBasePrice(featureMap);
        
        // 可用座位百分比
        features[6] = extractAvailableSeats(featureMap);
        
        // 航空公司系数
        features[7] = extractAirlineCoefficient(featureMap);
        
        // 油价指数
        features[8] = extractOilPriceIndex(featureMap);
        
        // 季节性因素
        features[9] = extractSeasonFactor(featureMap);
        
        return features;
    }
    
    // 辅助方法实现：提取特征、计算评估指标等
    private double extractDaysBeforeDeparture(Map<String, Object> featureMap) {
        if (featureMap.containsKey("departureDate")) {
            try {
                LocalDate departureDate = LocalDate.parse(featureMap.get("departureDate").toString());
                return ChronoUnit.DAYS.between(LocalDate.now(), departureDate);
            } catch (Exception e) {
                log.warn("解析出发日期失败，使用默认值", e);
            }
        } else if (featureMap.containsKey("daysBeforeDeparture")) {
            try {
                return ((Number) featureMap.get("daysBeforeDeparture")).doubleValue();
            } catch (Exception e) {
                log.warn("解析提前天数失败，使用默认值", e);
            }
        }
        return 7.0; // 默认值
    }
    
    private double extractDayOfWeek(Map<String, Object> featureMap) {
        if (featureMap.containsKey("departureDate")) {
            try {
                LocalDate departureDate = LocalDate.parse(featureMap.get("departureDate").toString());
                return departureDate.getDayOfWeek().getValue();
            } catch (Exception e) {
                log.warn("解析出发日期失败，使用默认值", e);
            }
        } else if (featureMap.containsKey("dayOfWeek")) {
            try {
                return ((Number) featureMap.get("dayOfWeek")).doubleValue();
            } catch (Exception e) {
                log.warn("解析星期几失败，使用默认值", e);
            }
        }
        return LocalDate.now().getDayOfWeek().getValue(); // 默认使用当前日期的星期
    }
    
    private double extractMonth(Map<String, Object> featureMap) {
        if (featureMap.containsKey("departureDate")) {
            try {
                LocalDate departureDate = LocalDate.parse(featureMap.get("departureDate").toString());
                return departureDate.getMonthValue();
            } catch (Exception e) {
                log.warn("解析出发日期失败，使用默认值", e);
            }
        } else if (featureMap.containsKey("month")) {
            try {
                return ((Number) featureMap.get("month")).doubleValue();
            } catch (Exception e) {
                log.warn("解析月份失败，使用默认值", e);
            }
        }
        return LocalDate.now().getMonthValue(); // 默认使用当前月份
    }
    
    private boolean extractHoliday(Map<String, Object> featureMap) {
        if (featureMap.containsKey("isHoliday")) {
            try {
                return Boolean.parseBoolean(featureMap.get("isHoliday").toString());
            } catch (Exception e) {
                log.warn("解析是否假期失败，使用默认值", e);
            }
        } else if (featureMap.containsKey("departureDate")) {
            try {
                LocalDate departureDate = LocalDate.parse(featureMap.get("departureDate").toString());
                return isHoliday(departureDate);
            } catch (Exception e) {
                log.warn("解析出发日期失败，使用默认值", e);
            }
        }
        return false; // 默认非假期
    }
    
    private double extractDistance(Map<String, Object> featureMap) {
        if (featureMap.containsKey("distance")) {
            try {
                return ((Number) featureMap.get("distance")).doubleValue();
            } catch (Exception e) {
                log.warn("解析距离失败，使用默认值", e);
            }
        }
        return 1000.0; // 默认距离
    }
    
    private double extractBasePrice(Map<String, Object> featureMap) {
        if (featureMap.containsKey("basePrice")) {
            try {
                return ((Number) featureMap.get("basePrice")).doubleValue();
            } catch (Exception e) {
                log.warn("解析基础价格失败，使用默认值", e);
            }
        }
        return 800.0; // 默认基础价格
    }
    
    private double extractAvailableSeats(Map<String, Object> featureMap) {
        if (featureMap.containsKey("availableSeatsPercentage")) {
            try {
                return ((Number) featureMap.get("availableSeatsPercentage")).doubleValue();
            } catch (Exception e) {
                log.warn("解析可用座位百分比失败，使用默认值", e);
            }
        }
        return 0.5; // 默认50%座位可用
    }
    
    private double extractAirlineCoefficient(Map<String, Object> featureMap) {
        if (featureMap.containsKey("airline")) {
            try {
                String airline = featureMap.get("airline").toString();
                return getAirlineCoefficient(airline);
            } catch (Exception e) {
                log.warn("解析航空公司系数失败，使用默认值", e);
            }
        }
        return 1.0; // 默认系数
    }
    
    private double extractOilPriceIndex(Map<String, Object> featureMap) {
        if (featureMap.containsKey("oilPriceIndex")) {
            try {
                return ((Number) featureMap.get("oilPriceIndex")).doubleValue();
            } catch (Exception e) {
                log.warn("解析油价指数失败，使用默认值", e);
            }
        } else if (featureMap.containsKey("departureDate")) {
            try {
                LocalDate departureDate = LocalDate.parse(featureMap.get("departureDate").toString());
                return getOilPriceIndex(departureDate);
            } catch (Exception e) {
                log.warn("解析出发日期失败，使用默认值", e);
            }
        }
        return 100.0; // 默认油价指数
    }
    
    private double extractSeasonFactor(Map<String, Object> featureMap) {
        if (featureMap.containsKey("seasonalFactor")) {
            try {
                return ((Number) featureMap.get("seasonalFactor")).doubleValue();
            } catch (Exception e) {
                log.warn("解析季节性因素失败，使用默认值", e);
            }
        } else if (featureMap.containsKey("month")) {
            try {
                int month = ((Number) featureMap.get("month")).intValue();
                return getSeasonFactor(month);
            } catch (Exception e) {
                log.warn("解析月份失败，使用默认值", e);
            }
        } else if (featureMap.containsKey("departureDate")) {
            try {
                LocalDate departureDate = LocalDate.parse(featureMap.get("departureDate").toString());
                return getSeasonFactor(departureDate.getMonthValue());
            } catch (Exception e) {
                log.warn("解析出发日期失败，使用默认值", e);
            }
        }
        return 1.0; // 默认季节因素
    }
    
    private boolean isHoliday(LocalDate date) {
        int month = date.getMonthValue();
        int day = date.getDayOfMonth();
        
        // 简化的中国主要节假日判断
        if ((month == 1 && day == 1) || // 元旦
            (month == 5 && day == 1) || // 劳动节
            (month == 10 && day >= 1 && day <= 7)) { // 国庆节
            return true;
        }
        
        // 春节大概在1-2月，但具体日期每年不同，这里简化处理
        if ((month == 1 && day >= 21) || (month == 2 && day <= 20)) {
            return true;
        }
        
        // 周末也当作假期
        int dayOfWeek = date.getDayOfWeek().getValue();
        return dayOfWeek == 6 || dayOfWeek == 7;
    }
    
    private double calculateAvailableSeats(FlightData flightData) {
        // 简化处理，实际应基于座位预订情况
        return Math.random() * 0.5 + 0.3; // 30%-80%之间的随机值
    }
    
    private double getAirlineCoefficient(String airlineCode) {
        // 根据航空公司调整价格系数，实际应基于历史数据分析
        Map<String, Double> airlineFactors = new HashMap<>();
        airlineFactors.put("CA", 1.2); // 中国国航，较高端
        airlineFactors.put("MU", 1.1); // 东航，中高端
        airlineFactors.put("CZ", 1.1); // 南航，中高端
        airlineFactors.put("HU", 1.0); // 海航，中端
        airlineFactors.put("3U", 0.9); // 川航，中低端
        airlineFactors.put("ZH", 0.85); // 深航，低端
        
        return airlineFactors.getOrDefault(airlineCode, 1.0);
    }
    
    private double getOilPriceIndex(LocalDate date) {
        // 简化处理，实际应基于历史油价数据
        // 这里假设2023年油价呈现波动趋势
        int month = date.getMonthValue();
        switch (month) {
            case 1: return 95.0;
            case 2: return 98.0;
            case 3: return 102.0;
            case 4: return 105.0;
            case 5: return 108.0;
            case 6: return 110.0;
            case 7: return 112.0;
            case 8: return 114.0;
            case 9: return 110.0;
            case 10: return 106.0;
            case 11: return 102.0;
            case 12: return 100.0;
            default: return 100.0;
        }
    }
    
    private double getSeasonFactor(int month) {
        // 根据月份确定季节性因素
        switch (month) {
            case 1: case 2: return 1.2; // 春节前后
            case 3: case 4: return 0.9; // 淡季
            case 5: return 1.0; // 五一小高峰
            case 6: return 1.1; // 暑假前
            case 7: case 8: return 1.3; // 暑假
            case 9: return 1.0; // 开学季
            case 10: return 1.2; // 国庆黄金周
            case 11: case 12: return 0.9; // 年末淡季
            default: return 1.0;
        }
    }
    
    private double getFeatureImportance(double[] features, int featureIndex) {
        // 实际应基于模型的特征重要性，这里简化处理
        double[] importanceWeights = {0.18, 0.08, 0.12, 0.05, 0.15, 0.1, 0.08, 0.09, 0.05, 0.1};
        double normalizedValue = 0.0;
        
        switch (featureIndex) {
            case 0: // 提前预订天数
                normalizedValue = Math.min(1.0, features[featureIndex] / 30.0);
                break;
            case 4: // 航线距离
                normalizedValue = Math.min(1.0, features[featureIndex] / 3000.0);
                break;
            case 9: // 季节因素
                normalizedValue = features[featureIndex] / 1.3; // 最大值为1.3
                break;
            default:
                normalizedValue = 0.5;
        }
        
        return normalizedValue * importanceWeights[featureIndex];
    }
    
    private double calculateMAE(double[] predictions, double[] actual) {
        double sum = 0.0;
        for (int i = 0; i < predictions.length; i++) {
            sum += Math.abs(predictions[i] - actual[i]);
        }
        return sum / predictions.length;
    }
    
    private double calculateMSE(double[] predictions, double[] actual) {
        double sum = 0.0;
        for (int i = 0; i < predictions.length; i++) {
            double diff = predictions[i] - actual[i];
            sum += diff * diff;
        }
        return sum / predictions.length;
    }
    
    private double calculateR2(double[] predictions, double[] actual, double meanActual) {
        double totalSS = 0.0;
        double residualSS = 0.0;
        
        for (int i = 0; i < actual.length; i++) {
            double diff1 = actual[i] - meanActual;
            double diff2 = actual[i] - predictions[i];
            
            totalSS += diff1 * diff1;
            residualSS += diff2 * diff2;
        }
        
        if (totalSS == 0.0) {
            return 0.0; // 避免除以零
        }
        
        return 1.0 - (residualSS / totalSS);
    }
    
    private double calculateMean(double[] values) {
        double sum = 0.0;
        for (double value : values) {
            sum += value;
        }
        return sum / values.length;
    }
    
    private double calculateAccuracy(double[] predictions, double[] actual) {
        int correct = 0;
        for (int i = 0; i < predictions.length; i++) {
            // 价格预测误差在10%以内认为是准确的
            double error = Math.abs(predictions[i] - actual[i]) / actual[i];
            if (error <= 0.1) {
                correct++;
            }
        }
        return (double) correct / predictions.length;
    }
    
    @Data
    public static class TrainingData {
        private int daysBeforeDeparture;
        private int dayOfWeek;
        private int month;
        private boolean holiday;
        private double distance;
        private double basePrice;
        private double availableSeatsPercentage;
        private double airlineCoefficient;
        private double oilPriceIndex;
        private double seasonalFactor;
        private double price;
    }
} 