package com.flightprice.prediction.ml;

import com.flightprice.prediction.entity.PricePredictionData;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.deeplearning4j.nn.conf.ConvolutionMode;
import org.deeplearning4j.nn.conf.MultiLayerConfiguration;
import org.deeplearning4j.nn.conf.NeuralNetConfiguration;
import org.deeplearning4j.nn.conf.inputs.InputType;
import org.deeplearning4j.nn.conf.layers.ConvolutionLayer;
import org.deeplearning4j.nn.conf.layers.DenseLayer;
import org.deeplearning4j.nn.conf.layers.OutputLayer;
import org.deeplearning4j.nn.conf.layers.SubsamplingLayer;
import org.deeplearning4j.nn.multilayer.MultiLayerNetwork;
import org.deeplearning4j.nn.weights.WeightInit;
import org.deeplearning4j.util.ModelSerializer;
import org.nd4j.linalg.activations.Activation;
import org.nd4j.linalg.api.ndarray.INDArray;
import org.nd4j.linalg.dataset.DataSet;
import org.nd4j.linalg.factory.Nd4j;
import org.nd4j.linalg.learning.config.Adam;
import org.nd4j.linalg.lossfunctions.LossFunctions;
import org.nd4j.linalg.ops.transforms.Transforms;
import org.springframework.stereotype.Component;

import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Consumer;
import java.util.Random;

/**
 * CNN价格预测模型实现
 */
@Slf4j
@Component
public class CNNPricePredictionModel implements PricePredictionAlgorithm {

    private MultiLayerNetwork model;
    private boolean isModelLoaded = false;
    private final Object lockObject = new Object();
    private static final int NUM_FEATURES = 10; // 特征维度
    private static final int BATCH_SIZE = 64;
    private static final int EPOCHS = 20;
    private static final double LEARNING_RATE = 0.001;
    private static final double TRAIN_RATIO = 0.8; // 80%数据用于训练，20%用于验证

    @Override
    public String getName() {
        return "CNN";
    }

    @Override
    public void loadModel(String modelPath) {
        try {
            File modelFile = new File(modelPath);
            if (modelFile.exists()) {
        synchronized (lockObject) {
                    model = ModelSerializer.restoreMultiLayerNetwork(modelFile);
                isModelLoaded = true;
                    log.info("CNN模型加载成功: {}", modelPath);
                }
            } else {
                log.error("模型文件不存在: {}", modelPath);
                throw new RuntimeException("模型文件不存在: " + modelPath);
            }
            } catch (IOException e) {
            log.error("加载CNN模型失败: {}", e.getMessage(), e);
            throw new RuntimeException("加载CNN模型失败", e);
        }
    }

    @Override
    public void saveModel(String modelPath) {
        if (model == null) {
            throw new RuntimeException("模型未初始化，无法保存");
        }
        
        try {
            File modelFile = new File(modelPath);
            // 确保目录存在
            File parentDir = modelFile.getParentFile();
            if (!parentDir.exists()) {
                parentDir.mkdirs();
            }
            
            ModelSerializer.writeModel(model, modelPath, true);
            log.info("CNN模型保存成功: {}", modelPath);
        } catch (IOException e) {
            log.error("保存CNN模型失败: {}", e.getMessage(), e);
            throw new RuntimeException("保存CNN模型失败", e);
        }
    }

    /**
     * 创建新的CNN模型
     * @return 初始化的模型
     */
    public MultiLayerNetwork createNewModel() {
        log.info("创建新的CNN模型");
        
        // 创建模型配置
        MultiLayerConfiguration conf = new NeuralNetConfiguration.Builder()
                .seed(123)
                .updater(new Adam(LEARNING_RATE))
                .weightInit(WeightInit.XAVIER)
                .l2(1e-4)
                .list()
                .layer(0, new DenseLayer.Builder()
                        .nIn(NUM_FEATURES)
                        .nOut(64)
                        .activation(Activation.RELU)
                        .build())
                .layer(1, new DenseLayer.Builder()
                        .nOut(32)
                        .activation(Activation.RELU)
                        .build())
                .layer(2, new OutputLayer.Builder(LossFunctions.LossFunction.MSE)
                        .nOut(1)
                        .activation(Activation.IDENTITY)
                        .build())
                .build();

        // 初始化模型
        MultiLayerNetwork model = new MultiLayerNetwork(conf);
        model.init();

        return model;
    }

    /**
     * 训练模型
     * @param trainingData 训练数据列表
     * @param modelPath 模型保存路径
     * @return 模型训练指标
     */
    public ModelMetrics trainModel(List<PricePredictionData> trainingData, String modelPath) {
        log.info("开始训练CNN模型，训练数据大小: {}", trainingData.size());

        if (trainingData == null || trainingData.isEmpty()) {
            log.error("训练数据为空，无法训练模型");
            return null;
        }

        try {
            // 创建或加载模型
            if (model == null) {
                model = createNewModel();
            }

            // 准备训练数据
            // 1. 洗牌数据
            List<PricePredictionData> shuffledData = new ArrayList<>(trainingData);
            java.util.Collections.shuffle(shuffledData, new Random(123));
            
            // 2. 划分训练集和验证集
            int trainSize = (int) (shuffledData.size() * TRAIN_RATIO);
            List<PricePredictionData> trainSet = shuffledData.subList(0, trainSize);
            List<PricePredictionData> valSet = shuffledData.subList(trainSize, shuffledData.size());
            
            // 3. 创建特征矩阵和标签
            INDArray trainFeatures = createFeatureMatrix(trainSet);
            INDArray trainLabels = createLabelMatrix(trainSet);
            
            INDArray valFeatures = createFeatureMatrix(valSet);
            INDArray valLabels = createLabelMatrix(valSet);
            
            // 创建训练数据集
            DataSet trainDataSet = new DataSet(trainFeatures, trainLabels);

            // 训练模型
            for (int i = 0; i < EPOCHS; i++) {
                model.fit(trainDataSet);
                
                // 每个epoch评估一次模型
                if (i % 5 == 0 || i == EPOCHS - 1) {
                    INDArray predictions = model.output(valFeatures);
                    ModelMetrics metrics = calculateMetrics(predictions, valLabels);
                    log.info("Epoch {}/{}: MAE={}, MSE={}, RMSE={}, R2={}", 
                            i+1, EPOCHS, metrics.getMae(), metrics.getMse(), metrics.getRmse(), metrics.getR2());
                }
            }
            
            // 最终评估
            INDArray predictions = model.output(valFeatures);
            ModelMetrics metrics = calculateMetrics(predictions, valLabels);
            log.info("训练完成, 最终指标: MAE={}, MSE={}, RMSE={}, R2={}", 
                    metrics.getMae(), metrics.getMse(), metrics.getRmse(), metrics.getR2());

            // 保存模型
            saveModel(modelPath);

            return metrics;
        } catch (Exception e) {
            log.error("训练CNN模型失败: {}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * 创建特征矩阵
     */
    private INDArray createFeatureMatrix(List<PricePredictionData> data) {
        INDArray features = Nd4j.zeros(data.size(), NUM_FEATURES);
        
        for (int i = 0; i < data.size(); i++) {
            PricePredictionData item = data.get(i);
            // 归一化特征
            features.putScalar(i, 0, normalizeDay(item.getDaysBeforeDeparture()));
            features.putScalar(i, 1, item.getDayOfWeek() / 6.0); // 0-6 -> 0-1
            features.putScalar(i, 2, (item.getMonth() - 1) / 11.0); // 1-12 -> 0-1
            features.putScalar(i, 3, item.isHoliday() ? 1.0 : 0.0);
            features.putScalar(i, 4, normalizeDistance(item.getDistance()));
            features.putScalar(i, 5, normalizePrice(item.getBasePrice()));
            features.putScalar(i, 6, item.getAvailableSeatsPercentage());
            features.putScalar(i, 7, (item.getAirlineCoefficient() - 0.8) / 0.4); // 0.8-1.2 -> 0-1
            features.putScalar(i, 8, (item.getOilPriceIndex() - 80) / 40.0); // 80-120 -> 0-1
            features.putScalar(i, 9, (item.getSeasonalFactor() - 0.9) / 0.4); // 0.9-1.3 -> 0-1
        }
        
        return features;
    }
    
    /**
     * 创建标签矩阵
     */
    private INDArray createLabelMatrix(List<PricePredictionData> data) {
        INDArray labels = Nd4j.zeros(data.size(), 1);
        
        for (int i = 0; i < data.size(); i++) {
            // 价格归一化处理
            labels.putScalar(i, 0, normalizePrice(data.get(i).getPrice()));
        }
        
        return labels;
    }

    /**
     * 计算模型评估指标
     */
    private ModelMetrics calculateMetrics(INDArray predictions, INDArray labels) {
        // 将归一化的预测转回原始值
        INDArray predOriginal = denormalizePrice(predictions);
        INDArray labelsOriginal = denormalizePrice(labels);
        
        INDArray diff = predOriginal.sub(labelsOriginal);
        INDArray absDiff = Transforms.abs(diff);
        INDArray squaredDiff = diff.mul(diff);
        
        double mae = absDiff.meanNumber().doubleValue();
        double mse = squaredDiff.meanNumber().doubleValue();
        double rmse = Math.sqrt(mse);

        // 计算R2
        double yMean = labelsOriginal.meanNumber().doubleValue();
        INDArray totalSS = labelsOriginal.sub(yMean).mul(labelsOriginal.sub(yMean));
        double r2 = 1.0 - (squaredDiff.sumNumber().doubleValue() / totalSS.sumNumber().doubleValue());

        ModelMetrics metrics = new ModelMetrics();
        metrics.setMae(mae);
        metrics.setMse(mse);
        metrics.setRmse(rmse);
        metrics.setR2(r2);
        metrics.setAccuracy(1.0 - (mae / labelsOriginal.meanNumber().doubleValue()));

        return metrics;
    }

    /**
     * 归一化距离出发天数
     */
    private double normalizeDay(int days) {
        return Math.min(days, 365) / 365.0; // 最多考虑一年内的数据
    }
    
    /**
     * 归一化距离
     */
    private double normalizeDistance(double distance) {
        return Math.min(distance, 5000) / 5000.0; // 最远考虑5000公里
    }
    
    /**
     * 归一化价格
     */
    private double normalizePrice(double price) {
        return Math.min(price, 10000) / 10000.0; // 最高考虑10000元
    }
    
    /**
     * 反归一化价格
     */
    private INDArray denormalizePrice(INDArray normalizedPrice) {
        return normalizedPrice.mul(10000);
    }

    @Override
    public Map<String, Object> train(Map<String, Object> dataMap, Double trainingRatio, 
                                     Map<String, Object> hyperParameters, Boolean autoTune,
                                     Consumer<Double> progressCallback) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            log.info("开始使用CNN算法训练模型");
            
            // 从dataMap中提取训练数据
            @SuppressWarnings("unchecked")
            List<PricePredictionData> trainingDataList = (List<PricePredictionData>) dataMap.get("trainingData");
            
            if (trainingDataList == null || trainingDataList.isEmpty()) {
                result.put("success", false);
                result.put("message", "训练数据为空");
                return result;
            }
            
            log.info("训练数据大小: {}", trainingDataList.size());
            
            // 应用超参数
            int epochs = EPOCHS;
            double learningRate = LEARNING_RATE;
            int batchSize = BATCH_SIZE;
            
            if (hyperParameters != null) {
                if (hyperParameters.containsKey("epochs")) {
                    epochs = ((Number) hyperParameters.get("epochs")).intValue();
                }
                if (hyperParameters.containsKey("learningRate")) {
                    learningRate = ((Number) hyperParameters.get("learningRate")).doubleValue();
                }
                if (hyperParameters.containsKey("batchSize")) {
                    batchSize = ((Number) hyperParameters.get("batchSize")).intValue();
                }
            }
            
            log.info("使用参数: epochs={}, learningRate={}, batchSize={}", epochs, learningRate, batchSize);
            
            // 如果没有指定训练比例，使用默认值
            if (trainingRatio == null) {
                trainingRatio = TRAIN_RATIO;
            }
            
            // 创建或使用现有模型
            if (model == null) {
                model = createNewModel();
                isModelLoaded = true;
            }
            
            // 准备数据
            // 1. 洗牌数据
            List<PricePredictionData> shuffledData = new ArrayList<>(trainingDataList);
            java.util.Collections.shuffle(shuffledData, new Random(123));
            
            // 2. 划分训练集和验证集
            int trainSize = (int) (shuffledData.size() * trainingRatio);
            List<PricePredictionData> trainSet = shuffledData.subList(0, trainSize);
            List<PricePredictionData> valSet = shuffledData.subList(trainSize, shuffledData.size());
            
            // 3. 创建特征矩阵和标签
            INDArray trainFeatures = createFeatureMatrix(trainSet);
            INDArray trainLabels = createLabelMatrix(trainSet);
            
            INDArray valFeatures = createFeatureMatrix(valSet);
            INDArray valLabels = createLabelMatrix(valSet);
            
            // 创建训练数据集
            DataSet trainDataSet = new DataSet(trainFeatures, trainLabels);
            
            // 训练进度
            int totalSteps = epochs;
            int currentStep = 0;
            
            // 训练模型
            for (int i = 0; i < epochs; i++) {
                model.fit(trainDataSet);
                currentStep++;
                
                // 报告进度
                if (progressCallback != null) {
                    progressCallback.accept((double) currentStep / totalSteps);
                }
                
                // 每个epoch评估一次模型
                if (i % 5 == 0 || i == epochs - 1) {
                    INDArray predictions = model.output(valFeatures);
                    ModelMetrics metrics = calculateMetrics(predictions, valLabels);
                    log.info("Epoch {}/{}: MAE={}, MSE={}, RMSE={}, R2={}, Accuracy={}", 
                            i+1, epochs, metrics.getMae(), metrics.getMse(), 
                            metrics.getRmse(), metrics.getR2(), metrics.getAccuracy());
                }
            }
            
            // 最终评估
            INDArray predictions = model.output(valFeatures);
            ModelMetrics metrics = calculateMetrics(predictions, valLabels);
            
            // 返回训练结果
            result.put("success", true);
            result.put("mae", metrics.getMae());
            result.put("mse", metrics.getMse());
            result.put("rmse", metrics.getRmse());
            result.put("r2", metrics.getR2());
            result.put("accuracy", metrics.getAccuracy());
            result.put("message", "模型训练成功");
            
            log.info("CNN模型训练完成: MAE={}, 准确率={}%", 
                     metrics.getMae(), metrics.getAccuracy() * 100);
            
            // 如果提供了模型路径，保存模型
            if (dataMap.containsKey("modelPath")) {
                String modelPath = (String) dataMap.get("modelPath");
                saveModel(modelPath);
                result.put("modelPath", modelPath);
            }
            
        } catch (Exception e) {
            log.error("训练CNN模型失败: {}", e.getMessage(), e);
            result.put("success", false);
            result.put("message", "训练失败: " + e.getMessage());
        }
        
        return result;
    }

    @Override
    public Map<String, Object> predict(Map<String, Object> featureMap) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 检查模型是否已加载
            synchronized (lockObject) {
                if (model == null) {
                    log.error("模型未加载，无法进行预测，使用后备预测方法");
                    return fallbackPredict(featureMap);
                }
                
                // 准备特征数据
                INDArray features = Nd4j.zeros(1, NUM_FEATURES);
                
                try {
                    // 从featureMap中提取特征并进行归一化处理
                    int daysBeforeDeparture = ((Number) featureMap.getOrDefault("daysBeforeDeparture", 0)).intValue();
                    int dayOfWeek = ((Number) featureMap.getOrDefault("dayOfWeek", 0)).intValue();
                    int month = ((Number) featureMap.getOrDefault("month", 1)).intValue();
                    boolean isHoliday = (Boolean) featureMap.getOrDefault("isHoliday", false);
                    double distance = ((Number) featureMap.getOrDefault("distance", 0.0)).doubleValue();
                    double basePrice = ((Number) featureMap.getOrDefault("basePrice", 0.0)).doubleValue();
                    double availableSeatsPercentage = ((Number) featureMap.getOrDefault("availableSeatsPercentage", 0.8)).doubleValue();
                    double airlineCoefficient = ((Number) featureMap.getOrDefault("airlineCoefficient", 1.0)).doubleValue();
                    double oilPriceIndex = ((Number) featureMap.getOrDefault("oilPriceIndex", 100.0)).doubleValue();
                    double seasonalFactor = ((Number) featureMap.getOrDefault("seasonalFactor", 1.0)).doubleValue();
                    
                    // 填充特征矩阵
                    features.putScalar(0, 0, normalizeDay(daysBeforeDeparture));
                    features.putScalar(0, 1, dayOfWeek / 6.0);
                    features.putScalar(0, 2, (month - 1) / 11.0);
                    features.putScalar(0, 3, isHoliday ? 1.0 : 0.0);
                    features.putScalar(0, 4, normalizeDistance(distance));
                    features.putScalar(0, 5, normalizePrice(basePrice));
                    features.putScalar(0, 6, availableSeatsPercentage);
                    features.putScalar(0, 7, (airlineCoefficient - 0.8) / 0.4);
                    features.putScalar(0, 8, (oilPriceIndex - 80) / 40.0);
                    features.putScalar(0, 9, (seasonalFactor - 0.9) / 0.4);
                } catch (Exception e) {
                    log.error("特征处理失败: {}", e.getMessage(), e);
                    return fallbackPredict(featureMap);
                }
                
                // 使用模型进行预测
                INDArray outputArray = model.output(features);
                
                // 反归一化预测结果获取真实价格
                double predictedPrice = denormalizePrice(outputArray).getDouble(0, 0);
                
                // 确保价格不是负数或不合理的值
                if (predictedPrice < 0 || Double.isNaN(predictedPrice) || Double.isInfinite(predictedPrice)) {
                    log.warn("模型预测出负价格或无效价格: {}，使用后备预测", predictedPrice);
                    double basePrice = ((Number) featureMap.getOrDefault("basePrice", 0.0)).doubleValue();
                    // 如果基础价格可用，使用它作为预测基础
                    if (basePrice > 0) {
                        predictedPrice = basePrice;
                        // 根据距离出发天数进行简单调整
                        int daysBeforeDeparture = ((Number) featureMap.getOrDefault("daysBeforeDeparture", 0)).intValue();
                        if (daysBeforeDeparture < 3) {
                            predictedPrice *= 1.3; // 临近出发，价格上涨50%
                        } else if (daysBeforeDeparture < 7) {
                            predictedPrice *= 1.1; // 较近，小幅上涨
                        } else if (daysBeforeDeparture > 30) {
                            predictedPrice *= 0.9; // 较远，小幅下降
                        }
                        
                        // 根据季节性因素调整
                        double seasonalFactor = ((Number) featureMap.getOrDefault("seasonalFactor", 1.0)).doubleValue();
                        predictedPrice *= seasonalFactor;
                    } else {
                        // 如果无法获取基础价格，设置一个合理的默认值
                        predictedPrice = 800.0;
                    }
                }
                
                // 确保最低价格
                predictedPrice = Math.max(100.0, predictedPrice);
                
                // 计算置信度 (模型置信度可以通过多种方式估计，这里使用一个简单的方法)
                // 在实际项目中，您可能需要使用更复杂的方法来估计置信度
                double confidence = calculateConfidence(features);
                
                // 如果是通过后备方法计算的价格，降低置信度
                if (predictedPrice < 0) {
                    confidence = Math.max(0.6, confidence - 0.2);
                }
                
                // 设置预测结果
                result.put("success", true);
                result.put("predictedPrice", predictedPrice);
                result.put("confidence", confidence);
                
                // 计算价格区间
                double interval = predictedPrice * (1 - confidence);
                result.put("lowerBound", Math.max(100.0, predictedPrice - interval));
                result.put("upperBound", predictedPrice + interval);
                
                log.info("价格预测完成: 预测价格={}, 置信度={}", predictedPrice, confidence);
            }
        } catch (Exception e) {
            log.error("价格预测失败: {}", e.getMessage(), e);
            return fallbackPredict(featureMap);
        }
        
        return result;
    }
    
    /**
     * 后备预测方法，当模型预测失败时使用
     * 基于简单规则进行价格估算
     */
    private Map<String, Object> fallbackPredict(Map<String, Object> featureMap) {
        Map<String, Object> result = new HashMap<>();
        try {
            log.info("使用后备方法进行价格预测");
            
            // 提取基本特征
            double basePrice = ((Number) featureMap.getOrDefault("basePrice", 500.0)).doubleValue();
            int daysBeforeDeparture = ((Number) featureMap.getOrDefault("daysBeforeDeparture", 7)).intValue();
            double seasonalFactor = ((Number) featureMap.getOrDefault("seasonalFactor", 1.0)).doubleValue();
            double airlineCoefficient = ((Number) featureMap.getOrDefault("airlineCoefficient", 1.0)).doubleValue();
            
            // 基于规则计算预测价格
            double predictedPrice = basePrice;
            
            // 1. 根据距离出发天数调整价格
            if (daysBeforeDeparture <= 1) {
                predictedPrice *= 1.5; // 出发前1天，价格上涨50%
            } else if (daysBeforeDeparture <= 3) {
                predictedPrice *= 1.3; // 出发前2-3天，价格上涨30%
            } else if (daysBeforeDeparture <= 7) {
                predictedPrice *= 1.15; // 出发前4-7天，价格上涨15%
            } else if (daysBeforeDeparture <= 14) {
                predictedPrice *= 1.05; // 出发前8-14天，价格上涨5%
            } else if (daysBeforeDeparture <= 30) {
                predictedPrice *= 1.0; // 出发前15-30天，价格不变
            } else if (daysBeforeDeparture <= 60) {
                predictedPrice *= 0.95; // 出发前31-60天，价格下降5%
            } else {
                predictedPrice *= 0.9; // 出发前60天以上，价格下降10%
            }
            
            // 2. 根据季节性因素调整
            predictedPrice *= seasonalFactor;
            
            // 3. 根据航空公司系数调整
            predictedPrice *= airlineCoefficient;
            
            // 4. 添加一些随机波动（±5%）模拟真实情况
            double randomFactor = 0.95 + Math.random() * 0.1; // 0.95-1.05之间的随机数
            predictedPrice *= randomFactor;
            
            // 确保最低价格
            predictedPrice = Math.max(100.0, predictedPrice);
            
            // 设置预测结果
            result.put("success", true);
            result.put("predictedPrice", predictedPrice);
            result.put("confidence", 0.7); // 后备方法置信度较低
            
            // 计算价格区间
            double interval = predictedPrice * 0.3; // 较宽的价格区间
            result.put("lowerBound", Math.max(100.0, predictedPrice - interval));
            result.put("upperBound", predictedPrice + interval);
            
            result.put("isBackupPrediction", true); // 标记为后备预测结果
            
            log.info("后备方法价格预测完成: 预测价格={}, 置信度=0.7", predictedPrice);
        } catch (Exception e) {
            log.error("后备预测失败: {}", e.getMessage(), e);
            result.put("success", false);
            result.put("message", "预测失败: " + e.getMessage());
        }
        
        return result;
    }

    /**
     * 计算模型对当前预测的置信度
     * 
     * @param features 输入特征
     * @return 置信度 (0-1之间)
     */
    private double calculateConfidence(INDArray features) {
        // 这里实现一种简单的置信度计算方法
        // 实际应用中，您可能需要使用贝叶斯方法或其他技术来估计模型的不确定性
        
        try {
            // 这里我们假设模型对某些特征范围的数据预测更准确
            // 例如，对于临近出发的航班和正常价格范围内的预测更有信心
            
            double dayConfidence = 1.0 - Math.abs(features.getDouble(0, 0) - 0.3) * 0.5; // 0.3大约是110天
            double priceConfidence = 1.0 - Math.abs(features.getDouble(0, 5) - 0.5) * 0.6; // 0.5是标准化价格的中间值
            double seatConfidence = features.getDouble(0, 6) > 0.3 ? 0.9 : 0.7; // 座位充足时更有信心
            
            // 综合各因素的置信度
            double confidence = (dayConfidence * 0.4 + priceConfidence * 0.4 + seatConfidence * 0.2);
            
            // 限制在0.7-0.95之间
            return Math.max(0.7, Math.min(0.95, confidence));
        } catch (Exception e) {
            log.error("计算置信度失败，使用默认值: {}", e.getMessage());
            return 0.8; // 默认置信度
        }
    }

    @Override
    public Map<String, Object> evaluate(Map<String, Object> testDataMap) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 检查模型是否已加载
            if (model == null) {
                result.put("success", false);
                result.put("message", "模型未加载，无法进行评估");
                return result;
            }
            
            // 从testDataMap中提取测试数据
            @SuppressWarnings("unchecked")
            List<PricePredictionData> testDataList = (List<PricePredictionData>) testDataMap.get("testData");
            
            if (testDataList == null || testDataList.isEmpty()) {
                result.put("success", false);
                result.put("message", "测试数据为空");
                return result;
            }
            
            // 准备特征矩阵和标签
            INDArray testFeatures = createFeatureMatrix(testDataList);
            INDArray testLabels = createLabelMatrix(testDataList);
            
            // 使用模型进行预测
            INDArray predictions = model.output(testFeatures);
            
            // 计算评估指标
            ModelMetrics metrics = calculateMetrics(predictions, testLabels);
            
            // 反归一化获取真实价格和预测价格
            INDArray predOriginal = denormalizePrice(predictions);
            INDArray labelsOriginal = denormalizePrice(testLabels);
            
            // 计算每个样本的相对误差
            INDArray absDiff = Transforms.abs(predOriginal.sub(labelsOriginal));
            INDArray relativeError = absDiff.div(labelsOriginal);
            double avgRelativeError = relativeError.meanNumber().doubleValue();
            
            // 计算不同误差范围内的预测准确率
            int total = testDataList.size();
            int within5Percent = 0;
            int within10Percent = 0;
            int within20Percent = 0;
            
            for (int i = 0; i < total; i++) {
                double error = relativeError.getDouble(i);
                if (error <= 0.05) within5Percent++;
                if (error <= 0.10) within10Percent++;
                if (error <= 0.20) within20Percent++;
            }
            
            // 返回评估结果
            result.put("success", true);
            result.put("mae", metrics.getMae());
            result.put("mse", metrics.getMse());
            result.put("rmse", metrics.getRmse());
            result.put("r2", metrics.getR2());
            result.put("accuracy", metrics.getAccuracy());
            result.put("averageRelativeError", avgRelativeError);
            result.put("within5Percent", (double) within5Percent / total);
            result.put("within10Percent", (double) within10Percent / total);
            result.put("within20Percent", (double) within20Percent / total);
            
            log.info("模型评估完成: MAE={}, 准确率={}%, 5%误差范围内={}%, 10%误差范围内={}%, 20%误差范围内={}%", 
                    metrics.getMae(), 
                    metrics.getAccuracy() * 100, 
                    (double) within5Percent / total * 100, 
                    (double) within10Percent / total * 100, 
                    (double) within20Percent / total * 100);
            
        } catch (Exception e) {
            log.error("评估CNN模型失败: {}", e.getMessage(), e);
            result.put("success", false);
            result.put("message", "评估失败: " + e.getMessage());
        }
        
        return result;
    }

    @Data
    public static class ModelMetrics {
        private double mae;     // 平均绝对误差
        private double mse;     // 均方误差
        private double rmse;    // 均方根误差
        private double r2;      // 决定系数
        private double accuracy; // 准确率
    }
    
    /**
     * 价格预测数据模型，用于训练和预测
     */
    @Data
    public static class PricePredictionData {
        private int daysBeforeDeparture;  // 距离出发天数
        private int dayOfWeek;            // 星期几
        private int month;                // 月份
        private boolean holiday;          // 是否假期
        private double distance;          // 航线距离
        private double basePrice;         // 基础票价
        private double availableSeatsPercentage; // 可用座位百分比
        private double airlineCoefficient;       // 航空公司系数
        private double oilPriceIndex;            // 油价指数
        private double seasonalFactor;           // 季节性因素
        private double price;                    // 价格 (训练目标值)
    }
}