package com.flightprice.prediction.ml;

import java.util.Map;
import java.util.function.Consumer;

/**
 * 价格预测算法接口
 */
public interface PricePredictionAlgorithm {
    
    /**
     * 获取算法名称
     */
    String getName();
    
    /**
     * 训练模型
     * 
     * @param dataMap 训练数据
     * @param trainingRatio 训练集比例
     * @param hyperParameters 超参数
     * @param autoTune 是否自动调优
     * @param progressCallback 进度回调
     * @return 训练结果信息
     */
    Map<String, Object> train(Map<String, Object> dataMap, Double trainingRatio, 
                              Map<String, Object> hyperParameters, Boolean autoTune,
                              Consumer<Double> progressCallback);
    
    /**
     * 保存模型
     * 
     * @param modelPath 模型保存路径
     */
    void saveModel(String modelPath);
    
    /**
     * 加载模型
     * 
     * @param modelPath 模型路径
     */
    void loadModel(String modelPath);
    
    /**
     * 预测价格
     * 
     * @param featureMap 特征数据
     * @return 预测结果
     */
    Map<String, Object> predict(Map<String, Object> featureMap);
    
    /**
     * 评估模型
     * 
     * @param testDataMap 测试数据
     * @return 评估结果
     */
    Map<String, Object> evaluate(Map<String, Object> testDataMap);
} 