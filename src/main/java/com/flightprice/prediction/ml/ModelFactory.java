package com.flightprice.prediction.ml;

import com.flightprice.prediction.exception.BusinessException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 模型工厂类，负责创建和管理各种算法实现
 */
@Slf4j
@Component
public class ModelFactory {
    
    // 支持的算法类型
    public static final String ALGORITHM_CNN = "CNN";
    public static final String ALGORITHM_RANDOM_FOREST = "RandomForest";
    public static final String ALGORITHM_LINEAR_REGRESSION = "LinearRegression";
    public static final String ALGORITHM_XGB = "XGBoost";
    public static final String ALGORITHM_LSTM = "LSTM";
    
    // 模型ID与算法类型的映射
    public static final String MODEL_ID_DEFAULT = "default-model";
    public static final String MODEL_ID_RANDOM_FOREST = "random-forest";
    public static final String MODEL_ID_LINEAR_REGRESSION = "linear-regression";
    public static final String MODEL_ID_XGB = "xgboost";
    public static final String MODEL_ID_LSTM = "lstm";
    
    // 算法实现映射表
    private final Map<String, PricePredictionAlgorithm> algorithmMap = new HashMap<>();
    private final Map<String, String> modelIdToAlgorithmMap = new HashMap<>();
    
    @Autowired
    private CNNPricePredictionModel cnnPricePredictionModel;
    
    // 随机森林模型实现（简单模拟）
    private final PricePredictionAlgorithm randomForestModel = new RandomForestPricePredictionModel();
    
    /**
     * 随机森林价格预测模型（内部类实现，简化版）
     */
    private class RandomForestPricePredictionModel implements PricePredictionAlgorithm {
        @Override
        public String getName() {
            return "RandomForest";
        }
        
        @Override
        public void loadModel(String modelPath) {
            log.info("加载随机森林模型: {}", modelPath);
        }
        
        @Override
        public void saveModel(String modelPath) {
            log.info("保存随机森林模型: {}", modelPath);
        }
        
        @Override
        public Map<String, Object> train(Map<String, Object> dataMap, Double trainingRatio, 
                                      Map<String, Object> hyperParameters, Boolean autoTune,
                                      java.util.function.Consumer<Double> progressCallback) {
            log.info("训练随机森林模型");
            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("accuracy", 0.86);
            result.put("message", "随机森林模型训练成功");
            return result;
        }
        
        @Override
        public Map<String, Object> predict(Map<String, Object> featureMap) {
            log.info("使用随机森林模型进行预测");
            
            // 基于特征进行简单预测
            double basePrice = 800.0;
            if (featureMap.containsKey("basePrice")) {
                try {
                    basePrice = ((Number) featureMap.get("basePrice")).doubleValue();
                } catch (Exception e) {
                    log.error("无法解析基础价格", e);
                }
            }
            
            // 根据特征调整价格
            double predictedPrice = basePrice;
            
            // 根据距离出发日期调整
            if (featureMap.containsKey("daysBeforeDeparture")) {
                int days = ((Number) featureMap.getOrDefault("daysBeforeDeparture", 0)).intValue();
                if (days <= 3) {
                    predictedPrice *= 1.25; // 临近出发价格上涨25%
                } else if (days <= 7) {
                    predictedPrice *= 1.1; // 一周内上涨10%
                } else if (days >= 30) {
                    predictedPrice *= 0.95; // 一个月后下降5%
                }
            }
            
            // 根据座位因素调整
            if (featureMap.containsKey("availableSeatsPercentage")) {
                double seatsPercentage = ((Number) featureMap.getOrDefault("availableSeatsPercentage", 0.5)).doubleValue();
                if (seatsPercentage < 0.2) {
                    predictedPrice *= 1.2; // 座位很少，价格上涨20%
                } else if (seatsPercentage > 0.8) {
                    predictedPrice *= 0.9; // 座位充足，价格下降10%
                }
            }
            
            // 添加一些随机性
            double randomFactor = 0.95 + Math.random() * 0.1;
            predictedPrice *= randomFactor;
            
            // 确保价格为正
            predictedPrice = Math.max(100.0, predictedPrice);
            
            // 构造返回结果
            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("predictedPrice", predictedPrice);
            result.put("confidence", 0.88); // 随机森林通常有较高置信度
            
            // 计算价格区间
            double interval = predictedPrice * 0.12; // 12%的区间
            result.put("lowerBound", Math.max(100.0, predictedPrice - interval));
            result.put("upperBound", predictedPrice + interval);
            
            return result;
        }
        
        @Override
        public Map<String, Object> evaluate(Map<String, Object> testDataMap) {
            log.info("评估随机森林模型");
            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("accuracy", 0.86);
            result.put("mae", 120.5);
            result.put("rmse", 180.2);
            return result;
        }
    }
    
    /**
     * 初始化算法映射
     */
    @PostConstruct
    public void init() {
        // 注册现有的CNN算法实现
        algorithmMap.put(ALGORITHM_CNN, cnnPricePredictionModel);
        
        // 注册随机森林算法实现
        algorithmMap.put(ALGORITHM_RANDOM_FOREST, randomForestModel);
        
        // 初始化模型ID映射
        modelIdToAlgorithmMap.put(MODEL_ID_DEFAULT, ALGORITHM_CNN);
        modelIdToAlgorithmMap.put(MODEL_ID_RANDOM_FOREST, ALGORITHM_RANDOM_FOREST);
        modelIdToAlgorithmMap.put(MODEL_ID_LINEAR_REGRESSION, ALGORITHM_LINEAR_REGRESSION);
        modelIdToAlgorithmMap.put(MODEL_ID_XGB, ALGORITHM_XGB);
        modelIdToAlgorithmMap.put(MODEL_ID_LSTM, ALGORITHM_LSTM);
        
        log.info("价格预测算法工厂初始化完成，支持的算法: {}", getAvailableAlgorithms());
    }
    
    /**
     * 获取算法实例
     * 
     * @param algorithmType 算法类型
     * @return 算法实例
     */
    public PricePredictionAlgorithm getAlgorithm(String algorithmType) {
        PricePredictionAlgorithm algorithm = algorithmMap.get(algorithmType);
        if (algorithm == null) {
            log.warn("不支持的算法类型: {}，将使用默认CNN算法", algorithmType);
            return algorithmMap.get(ALGORITHM_CNN);
        }
        return algorithm;
    }
    
    /**
     * 根据模型ID获取对应算法实例
     * 
     * @param modelId 模型ID，由前端传入
     * @return 算法实例
     */
    public PricePredictionAlgorithm getAlgorithmByModelId(String modelId) {
        if (modelId == null || modelId.isEmpty()) {
            log.info("未指定模型ID，使用默认CNN模型");
            return algorithmMap.get(ALGORITHM_CNN);
        }
        
        // 根据模型ID查找对应的算法类型
        String algorithmType = modelIdToAlgorithmMap.get(modelId);
        if (algorithmType == null) {
            log.warn("未知的模型ID: {}，将使用默认CNN算法", modelId);
            return algorithmMap.get(ALGORITHM_CNN);
        }
        
        // 获取算法实例
        PricePredictionAlgorithm algorithm = algorithmMap.get(algorithmType);
        if (algorithm == null) {
            // 如果算法尚未实现，检查是否为随机森林模型
            if (ALGORITHM_RANDOM_FOREST.equals(algorithmType)) {
                log.info("使用随机森林模型");
                return randomForestModel;
            } else {
                log.warn("模型ID: {} 对应的算法类型: {} 未实现，将使用默认CNN算法", modelId, algorithmType);
                return algorithmMap.get(ALGORITHM_CNN);
            }
        }
        
        log.info("使用模型ID: {} 对应的算法: {}", modelId, algorithm.getName());
        return algorithm;
    }
    
    /**
     * 获取所有可用的算法类型
     */
    public List<String> getAvailableAlgorithms() {
        return Arrays.asList(
                ALGORITHM_CNN,
                ALGORITHM_RANDOM_FOREST,
                ALGORITHM_LINEAR_REGRESSION,
                ALGORITHM_XGB,
                ALGORITHM_LSTM
        );
    }
    
    /**
     * 检查算法是否可用
     */
    public boolean isAlgorithmAvailable(String algorithmType) {
        return algorithmMap.containsKey(algorithmType);
    }
    
    /**
     * 获取已实现的算法类型
     */
    public List<String> getImplementedAlgorithms() {
        return algorithmMap.keySet().stream().collect(Collectors.toList());
    }
    
    /**
     * 获取所有支持的模型ID
     */
    public List<String> getSupportedModelIds() {
        return Arrays.asList(
                MODEL_ID_DEFAULT,
                MODEL_ID_RANDOM_FOREST
                // 其他模型ID在实现后添加
        );
    }
} 