package com.flightprice.prediction.task;

import com.flightprice.prediction.config.ModelConfig;
import com.flightprice.prediction.service.PricePredictionService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * 模型训练定时任务
 */
@Slf4j
@Component
@EnableScheduling
public class ModelTrainingTask {
    
    @Autowired
    private PricePredictionService pricePredictionService;
    
    @Autowired
    private ModelConfig modelConfig;
    
    /**
     * 每天凌晨2点执行模型训练
     */
    @Scheduled(cron = "0 0 2 * * ?")
    public void trainModel() {
        log.info("定时训练模型任务开始执行...");
        
        try {
            // 根据配置决定是否训练通用模型
            Long routeId = Boolean.TRUE.equals(modelConfig.getUseGeneralModel()) ? null : null;
            
            // 执行训练
            Long modelId = pricePredictionService.trainModel(routeId);
            
            if (modelId != null) {
                log.info("定时训练模型成功，新模型ID: {}", modelId);
            } else {
                log.warn("定时训练模型未产生新模型");
            }
        } catch (Exception e) {
            log.error("定时训练模型失败", e);
        }
    }
    
    /**
     * 每12小时收集一次价格数据
     */
    @Scheduled(cron = "0 0 */12 * * ?")
    public void collectPriceData() {
        log.info("定时收集价格数据任务开始执行...");
        
        try {
            pricePredictionService.collectPriceData();
            log.info("定时收集价格数据成功");
        } catch (Exception e) {
            log.error("定时收集价格数据失败", e);
        }
    }
} 