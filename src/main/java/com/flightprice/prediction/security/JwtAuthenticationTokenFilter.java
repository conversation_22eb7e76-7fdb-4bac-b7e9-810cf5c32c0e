package com.flightprice.prediction.security;

import com.flightprice.prediction.common.Constants;
import com.flightprice.prediction.util.JwtUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.web.authentication.WebAuthenticationDetailsSource;
import org.springframework.stereotype.Component;
import org.springframework.web.filter.OncePerRequestFilter;

import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * JWT认证过滤器
 */
@Slf4j
@Component
public class JwtAuthenticationTokenFilter extends OncePerRequestFilter {

    private final UserDetailsServiceImpl userDetailsService;
    private final JwtUtil jwtUtil;

    public JwtAuthenticationTokenFilter(UserDetailsServiceImpl userDetailsService, JwtUtil jwtUtil) {
        this.userDetailsService = userDetailsService;
        this.jwtUtil = jwtUtil;
    }

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain chain) throws ServletException, IOException {
        try {
            // 获取请求头中的token
            String token = request.getHeader(Constants.Security.TOKEN_HEADER);
            if (StringUtils.isNotEmpty(token) && token.startsWith(Constants.Security.TOKEN_PREFIX)) {
                // 提取token值
                token = token.substring(Constants.Security.TOKEN_PREFIX.length());

                try {
                    // 从token中获取用户名
                    String username = jwtUtil.getUsernameFromToken(token);

                    // 如果能获取到用户名且当前SecurityContext中没有认证信息
                    if (StringUtils.isNotEmpty(username) && SecurityContextHolder.getContext().getAuthentication() == null) {
                        // 加载用户信息
                        UserDetails userDetails = userDetailsService.loadUserByUsername(username);

                        // 验证token
                        if (jwtUtil.validateToken(token, userDetails)) {
                            // 将用户信息存入SecurityContext
                            UsernamePasswordAuthenticationToken authentication = new UsernamePasswordAuthenticationToken(
                                    userDetails, null, userDetails.getAuthorities());
                            authentication.setDetails(new WebAuthenticationDetailsSource().buildDetails(request));
                            SecurityContextHolder.getContext().setAuthentication(authentication);
                            log.debug("认证成功，用户：{}", username);
                        }
                    }
                } catch (Exception e) {
                    log.error("解析JWT令牌失败", e);
                    // 令牌解析失败，但不中断请求
                }
            }
        } catch (Exception e) {
            log.error("处理JWT认证过滤器时发生异常", e);
            // 发生异常时不中断过滤器链，继续处理请求
        }

        chain.doFilter(request, response);
    }
}