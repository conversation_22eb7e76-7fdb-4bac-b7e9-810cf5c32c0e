package com.flightprice.prediction.service;

import com.flightprice.prediction.entity.PriceComparison;
import java.math.BigDecimal;
import java.util.List;

public interface PriceComparisonService {
    /**
     * 保存价格比较数据
     */
    PriceComparison saveComparison(Long flightId, String cabinClass, 
                                 BigDecimal originalPrice, BigDecimal predictedPrice,
                                 BigDecimal actualPrice);

    /**
     * 根据航班ID获取价格比较数据
     */
    List<PriceComparison> getComparisonsByFlight(Long flightId);

    /**
     * 根据舱位等级获取价格比较数据
     */
    List<PriceComparison> getComparisonsByCabinClass(String cabinClass);

    /**
     * 根据航班ID和舱位等级获取价格比较数据
     */
    List<PriceComparison> getComparisonsByFlightAndCabin(Long flightId, String cabinClass);
} 