package com.flightprice.prediction.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.flightprice.prediction.common.PageResult;
import com.flightprice.prediction.dto.CreateOrderRequest;
import com.flightprice.prediction.entity.Order;
import com.flightprice.prediction.vo.OrderVO;

import java.util.List;

/**
 * 订单服务接口
 */
public interface OrderService extends IService<Order> {

    /**
     * 获取当前用户最近的订单
     *
     * @param limit 最大数量
     * @return 订单列表
     */
    List<OrderVO> getRecentOrders(Integer limit);

    /**
     * 创建订单
     *
     * @param request 创建订单请求
     * @return 订单号
     */
    String createOrder(CreateOrderRequest request);

    /**
     * 支付订单
     *
     * @param orderNo 订单号
     * @return 是否成功
     */
    boolean payOrder(String orderNo);

    /**
     * 取消订单
     *
     * @param orderNo 订单号
     */
    void cancelOrder(String orderNo);

    /**
     * 获取订单详情
     *
     * @param orderNo 订单号
     * @return 订单详情
     */
    OrderVO getOrderDetail(String orderNo);

    /**
     * 获取当前用户订单列表
     *
     * @param pageNum  页码
     * @param pageSize 每页数量
     * @param status   订单状态
     * @return 分页结果
     */
    PageResult<OrderVO> getUserOrders(Integer pageNum, Integer pageSize, String status);

    /**
     * 获取所有订单列表（管理员）
     *
     * @param pageNum  页码
     * @param pageSize 每页数量
     * @param status   订单状态
     * @return 分页结果
     */
    PageResult<OrderVO> getAllOrders(Integer pageNum, Integer pageSize, String status);
} 