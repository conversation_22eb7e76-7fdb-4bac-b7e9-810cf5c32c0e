package com.flightprice.prediction.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.flightprice.prediction.common.PageResult;
import com.flightprice.prediction.dto.FlightSearchRequest;
import com.flightprice.prediction.entity.Flight;
import com.flightprice.prediction.vo.FlightVO;

/**
 * 航班服务接口
 */
public interface FlightService extends IService<Flight> {

    /**
     * 搜索航班
     *
     * @param request 搜索请求
     * @return 分页结果
     */
    PageResult<FlightVO> searchFlights(FlightSearchRequest request);

    /**
     * 获取航班详情
     *
     * @param id 航班ID
     * @return 航班详情
     */
    FlightVO getFlightDetail(Long id);

    /**
     * 新增航班
     *
     * @param flight 航班
     * @return 航班ID
     */
    Long addFlight(Flight flight);

    /**
     * 修改航班
     *
     * @param flight 航班
     */
    void updateFlight(Flight flight);

    /**
     * 删除航班
     *
     * @param id 航班ID
     */
    void deleteFlight(Long id);

    /**
     * 获取航班模板列表
     *
     * @param pageNum 页码
     * @param pageSize 每页大小
     * @param airlineId 航空公司ID（可选）
     * @param departureCityId 出发城市ID（可选）
     * @param arrivalCityId 到达城市ID（可选）
     * @return 航班模板分页结果
     */
    PageResult<FlightVO> getFlightTemplates(Integer pageNum, Integer pageSize, Long airlineId, Long departureCityId, Long arrivalCityId);

    /**
     * 获取航班模板详情
     *
     * @param id 航班模板ID
     * @return 航班模板详情
     */
    FlightVO getFlightTemplateDetail(Long id);

    /**
     * 新增航班模板
     *
     * @param flightTemplate 航班模板信息
     * @return 航班模板ID
     */
    Long addFlightTemplate(Flight flightTemplate);

    /**
     * 修改航班模板
     *
     * @param flightTemplate 航班模板信息
     */
    void updateFlightTemplate(Flight flightTemplate);

    /**
     * 删除航班模板
     *
     * @param id 航班模板ID
     */
    void deleteFlightTemplate(Long id);
} 