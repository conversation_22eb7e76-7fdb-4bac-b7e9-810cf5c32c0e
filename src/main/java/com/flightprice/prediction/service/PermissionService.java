package com.flightprice.prediction.service;

import com.flightprice.prediction.dto.PermissionDTO;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.List;

/**
 * 权限服务接口
 */
public interface PermissionService {
    
    /**
     * 分页查询权限列表
     *
     * @param keyword 关键字
     * @param type 权限类型
     * @param pageable 分页参数
     * @return 权限分页列表
     */
    Page<PermissionDTO> getPermissionPage(String keyword, String type, Pageable pageable);
    
    /**
     * 获取所有权限列表
     *
     * @return 权限列表
     */
    List<PermissionDTO> getAllPermissions();
    
    /**
     * 获取权限树
     *
     * @return 权限树
     */
    List<PermissionDTO> getPermissionTree();
    
    /**
     * 根据类型获取权限列表
     *
     * @param type 权限类型
     * @return 权限列表
     */
    List<PermissionDTO> getPermissionsByType(String type);
    
    /**
     * 根据ID获取权限详情
     *
     * @param id 权限ID
     * @return 权限详情
     */
    PermissionDTO getPermissionById(Long id);
    
    /**
     * 创建权限
     *
     * @param permissionDTO 权限DTO
     * @return 权限ID
     */
    Long createPermission(PermissionDTO permissionDTO);
    
    /**
     * 更新权限
     *
     * @param permissionDTO 权限DTO
     * @return 是否成功
     */
    boolean updatePermission(PermissionDTO permissionDTO);
    
    /**
     * 删除权限
     *
     * @param id 权限ID
     * @return 是否成功
     */
    boolean deletePermission(Long id);
    
    /**
     * 批量删除权限
     *
     * @param ids 权限ID列表
     * @return 是否成功
     */
    boolean batchDeletePermission(List<Long> ids);
} 