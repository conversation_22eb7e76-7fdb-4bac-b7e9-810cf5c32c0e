package com.flightprice.prediction.service;

import com.flightprice.prediction.vo.dashboard.*;

import java.util.Map;

/**
 * 数据大屏服务接口
 */
public interface DataDashboardService {

    /**
     * 获取顶部概览数据
     */
    DashboardOverviewVO getOverview();

    /**
     * 获取价格趋势数据
     */
    PriceTrendsVO getPriceTrends(String departureCity, String arrivalCity, String airline, String timeRange);

    /**
     * 获取航线热度数据
     */
    RouteHeatVO getRouteHeat();

    /**
     * 获取航空公司市场份额
     */
    AirlineMarketShareVO getAirlineMarketShare();

    /**
     * 获取热门城市排行
     */
    HotCitiesVO getHotCities(Integer limit);

    /**
     * 获取航班准点率统计
     */
    PunctualityVO getPunctuality(String airline);

    /**
     * 获取客流量统计
     */
    PassengerFlowVO getPassengerFlow(String timeRange);

    /**
     * 获取地图数据
     */
    MapDataVO getMapData();
    
    /**
     * 获取价格区间分布
     */
    PriceDistributionVO getPriceDistribution();
    
    /**
     * 获取各城市平均价格
     */
    CityAvgPricesVO getCityAvgPrices(String type);
    
    /**
     * 获取预测准确率统计
     */
    PredictionAccuracyVO getPredictionAccuracy();
    
    /**
     * 获取实时数据监控
     */
    RealtimeMonitorVO getRealtimeMonitor();
    
    /**
     * 获取日期因素对价格的影响
     */
    DatePriceImpactVO getDatePriceImpact();
    
    /**
     * 综合大屏数据（所有数据）
     */
    Map<String, Object> getAllDashboardData();
} 