package com.flightprice.prediction.service;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.stream.Collectors;
import java.util.Calendar;

/**
 * 航班数据缓存服务
 * 用于缓存航班数据中的城市和航空公司信息
 */
@Service
@Slf4j
public class FlightDataCacheService {

    @Autowired
    private JdbcTemplate jdbcTemplate;

    // 缓存容器
    private final Map<String, List<String>> dataCache = new ConcurrentHashMap<>();
    
    // 热门航线数据缓存
    private final Map<Integer, List<Map<String, Object>>> hotRoutesCache = new ConcurrentHashMap<>();
    
    // 特价机票数据缓存
    private final Map<Integer, List<Map<String, Object>>> promotionTicketsCache = new ConcurrentHashMap<>();

    // 缓存键
    private static final String DEPARTURE_CITIES_KEY = "departure_cities";
    private static final String ARRIVAL_CITIES_KEY = "arrival_cities";
    private static final String AIRLINES_KEY = "airlines";
    private static final String ARRIVAL_CITIES_BY_DEPARTURE_KEY = "arrival_cities_by_departure_";
    private static final String HOT_ROUTES_KEY = "hot_routes_";
    
    // 缓存初始化状态标志
    private final AtomicBoolean departureCitiesLoaded = new AtomicBoolean(false);
    private final AtomicBoolean arrivalCitiesLoaded = new AtomicBoolean(false);
    private final AtomicBoolean airlinesLoaded = new AtomicBoolean(false);
    private final AtomicBoolean hotRoutesLoaded = new AtomicBoolean(false);
    private final AtomicBoolean promotionTicketsLoaded = new AtomicBoolean(false);

    /**
     * 启动异步初始化缓存
     */
    @PostConstruct
    public void initCacheAsync() {
        log.info("启动异步初始化航班数据缓存...");
        CompletableFuture.runAsync(this::asyncLoadBasicData);
    }
    
    /**
     * 异步加载基础数据
     */
    @Async("cacheTaskExecutor")
    public void asyncLoadBasicData() {
        try {
            log.info("异步加载航班数据缓存 - 开始...");
            // 使用重试机制加载基础数据
            retryOperation(() -> loadDepartureCities(), "加载出发城市数据", 3);
            retryOperation(() -> loadArrivalCities(), "加载到达城市数据", 3);
            retryOperation(() -> loadAirlines(), "加载航空公司数据", 3);
            
            log.info("异步加载航班数据缓存 - 基础数据加载完成");
            
            // 再加载复杂数据
            CompletableFuture.runAsync(this::asyncLoadAdvancedData);
        } catch (Exception e) {
            log.error("异步加载基础数据时发生错误", e);
        }
    }
    
    /**
     * 异步加载高级数据
     */
    @Async("cacheTaskExecutor")
    public void asyncLoadAdvancedData() {
        try {
            log.info("异步加载高级缓存数据 - 开始...");
            // 使用重试机制预加载热门航线数据
            retryOperation(() -> loadHotRoutes(20), "加载热门航线数据(20)", 3);
            retryOperation(() -> loadHotRoutes(10), "加载热门航线数据(10)", 3);
            log.info("异步加载航班数据缓存 - 热门航线数据加载完成");
            
            // 使用重试机制预加载特价机票数据
            retryOperation(() -> loadPromotionTickets(50), "加载特价机票数据(50)", 3);
            log.info("异步加载航班数据缓存 - 特价机票数据加载完成");
            log.info("异步加载航班数据缓存 - 全部完成");
        } catch (Exception e) {
            log.error("异步加载高级数据时发生错误", e);
        }
    }

    /**
     * 每天凌晨2点自动刷新缓存
     */
    @Scheduled(cron = "0 0 2 * * ?")
    public void scheduledRefreshCache() {
        log.info("定时任务：刷新航班数据缓存");
        refreshCacheAsync();
    }
    
    /**
     * 异步刷新缓存
     */
    public void refreshCacheAsync() {
        log.info("启动异步刷新航班数据缓存...");
        CompletableFuture.runAsync(() -> {
            try {
                log.info("异步刷新航班数据缓存 - 开始...");
                loadDepartureCities();
                loadArrivalCities();
                loadAirlines();
                
                // 刷新热门航线缓存
                hotRoutesCache.clear();
                hotRoutesLoaded.set(false);
                
                // 刷新特价机票缓存
                promotionTicketsCache.clear();
                promotionTicketsLoaded.set(false);
                
                // 预加载热门航线和特价机票数据
                loadHotRoutes(20);
                loadHotRoutes(10);
                loadPromotionTickets(20);
                
                log.info("异步刷新航班数据缓存 - 完成");
            } catch (Exception e) {
                log.error("异步刷新缓存时发生错误", e);
            }
        });
    }

    /**
     * 获取出发城市列表
     */
    public List<String> getDepartureCities() {
        if (!departureCitiesLoaded.get()) {
            loadDepartureCities();
        }
        return dataCache.getOrDefault(DEPARTURE_CITIES_KEY, Collections.emptyList());
    }

    /**
     * 获取到达城市列表
     */
    public List<String> getArrivalCities() {
        if (!arrivalCitiesLoaded.get()) {
            loadArrivalCities();
        }
        return dataCache.getOrDefault(ARRIVAL_CITIES_KEY, Collections.emptyList());
    }

    /**
     * 获取航空公司列表
     */
    public List<String> getAirlines() {
        if (!airlinesLoaded.get()) {
            loadAirlines();
        }
        return dataCache.getOrDefault(AIRLINES_KEY, Collections.emptyList());
    }

    /**
     * 设置航空公司列表缓存
     * @param airlines 航空公司列表
     */
    public void setAirlines(List<String> airlines) {
        if (airlines != null && !airlines.isEmpty()) {
            dataCache.put(AIRLINES_KEY, airlines);
            airlinesLoaded.set(true);
            log.info("航空公司缓存已更新，共 {} 条", airlines.size());
        }
    }

    /**
     * 搜索出发城市
     */
    public List<String> searchDepartureCities(String keyword) {
        List<String> allCities = getDepartureCities();
        return allCities.stream()
                .filter(city -> city.contains(keyword))
                .limit(10)
                .collect(Collectors.toList());
    }

    /**
     * 搜索到达城市
     */
    public List<String> searchArrivalCities(String keyword) {
        List<String> allCities = getArrivalCities();
        return allCities.stream()
                .filter(city -> city.contains(keyword))
                .limit(10)
                .collect(Collectors.toList());
    }

    /**
     * 搜索航空公司
     */
    public List<String> searchAirlines(String keyword) {
        List<String> allAirlines = getAirlines();
        return allAirlines.stream()
                .filter(airline -> airline.contains(keyword))
                .limit(10)
                .collect(Collectors.toList());
    }

    /**
     * 加载出发城市数据
     */
    private synchronized void loadDepartureCities() {
        if (departureCitiesLoaded.get()) {
            return;
        }
        
        log.info("从数据库加载出发城市数据...");
        try {
        String sql = "SELECT DISTINCT departure_city FROM flight_data WHERE departure_city IS NOT NULL ORDER BY departure_city";
        List<String> cities = jdbcTemplate.queryForList(sql, String.class);
        dataCache.put(DEPARTURE_CITIES_KEY, cities);
            departureCitiesLoaded.set(true);
        log.info("出发城市数据加载完成，共 {} 条", cities.size());
        } catch (Exception e) {
            log.error("加载出发城市数据时发生错误", e);
            dataCache.put(DEPARTURE_CITIES_KEY, new ArrayList<>());
        }
    }

    /**
     * 加载到达城市数据
     */
    private synchronized void loadArrivalCities() {
        if (arrivalCitiesLoaded.get()) {
            return;
        }
        
        log.info("从数据库加载到达城市数据...");
        try {
        String sql = "SELECT DISTINCT arrival_city FROM flight_data WHERE arrival_city IS NOT NULL ORDER BY arrival_city";
        List<String> cities = jdbcTemplate.queryForList(sql, String.class);
        dataCache.put(ARRIVAL_CITIES_KEY, cities);
            arrivalCitiesLoaded.set(true);
        log.info("到达城市数据加载完成，共 {} 条", cities.size());
        } catch (Exception e) {
            log.error("加载到达城市数据时发生错误", e);
            dataCache.put(ARRIVAL_CITIES_KEY, new ArrayList<>());
        }
    }

    /**
     * 加载航空公司数据
     */
    private synchronized void loadAirlines() {
        if (airlinesLoaded.get()) {
            return;
        }
        
        log.info("从数据库加载航空公司数据...");
        try {
        String sql = "SELECT DISTINCT airline FROM flight_data WHERE airline IS NOT NULL ORDER BY airline";
        List<String> airlines = jdbcTemplate.queryForList(sql, String.class);
        dataCache.put(AIRLINES_KEY, airlines);
            airlinesLoaded.set(true);
        log.info("航空公司数据加载完成，共 {} 条", airlines.size());
        } catch (Exception e) {
            log.error("加载航空公司数据时发生错误", e);
            dataCache.put(AIRLINES_KEY, new ArrayList<>());
        }
    }

    /**
     * 刷新缓存
     */
    public void refreshCache() {
        refreshCacheAsync();
    }

    /**
     * 根据出发城市获取可到达的城市
     */
    public List<String> getArrivalCitiesByDepartureCity(String departureCity) {
        String cacheKey = ARRIVAL_CITIES_BY_DEPARTURE_KEY + departureCity;
        if (!dataCache.containsKey(cacheKey)) {
            log.info("从数据库加载出发城市 {} 的可到达城市列表", departureCity);
            try {
            String sql = "SELECT DISTINCT arrival_city FROM flight_data WHERE departure_city = ? AND is_deleted = 0 ORDER BY arrival_city";
            List<String> cities = jdbcTemplate.queryForList(sql, String.class, departureCity);
            dataCache.put(cacheKey, cities);
            log.info("出发城市 {} 的可到达城市列表加载完成，共 {} 条", departureCity, cities.size());
            } catch (Exception e) {
                log.error("加载可到达城市列表时发生错误", e);
                dataCache.put(cacheKey, new ArrayList<>());
            }
        }
        return dataCache.getOrDefault(cacheKey, Collections.emptyList());
    }
    
    /**
     * 加载热门航线数据
     * @param limit 加载的航线数量
     */
    private synchronized void loadHotRoutes(Integer limit) {
        // 检查是否已经加载过
        if (hotRoutesCache.containsKey(limit)) {
            return;
        }
        
        log.info("从数据库加载热门航线数据，数量: {}...", limit);
        try {
            String sql = "SELECT " +
                    "departure_city AS departureCity, " +
                    "arrival_city AS arrivalCity, " +
                    "COUNT(*) AS flightCount, " +
                    "AVG(price) AS avgPrice, " +
                    "MIN(price) AS minPrice, " +
                    "MAX(price) AS maxPrice " +
                    "FROM flight_data " +
                    "WHERE departure_city IS NOT NULL AND arrival_city IS NOT NULL " +
                    "AND is_deleted = 0 " +
                    "GROUP BY departure_city, arrival_city " +
                    "ORDER BY flightCount DESC, avgPrice ASC " +
                    "LIMIT ?";

            // 使用JdbcTemplate执行SQL查询
            List<Map<String, Object>> routes = jdbcTemplate.queryForList(sql, limit);

            // 处理结果：计算热门度和特价标志
            for (Map<String, Object> route : routes) {
                // 将航班数量转化为日均航班数
                Long flightCount = (Long) route.get("flightCount");
                // 假设数据库中的数据覆盖了30天，计算日均航班数
                int dailyFlights = (int) Math.ceil(flightCount / 30.0);
                route.put("dailyFlights", dailyFlights);

                // 价格格式化：保留整数
                if (route.get("avgPrice") != null) {
                    BigDecimal avgPrice = new BigDecimal(route.get("avgPrice").toString());
                    route.put("avgPrice", avgPrice.setScale(0, RoundingMode.HALF_UP));
                }

                // 添加热门标志
                route.put("isHot", dailyFlights >= 10); // 日均10班以上认为是热门

                // 添加特价标志 - 假设特价是指最低价格比平均价格低20%
                if (route.get("minPrice") != null && route.get("avgPrice") != null) {
                    BigDecimal minPrice = new BigDecimal(route.get("minPrice").toString());
                    BigDecimal avgPrice = new BigDecimal(route.get("avgPrice").toString());

                    if (avgPrice.compareTo(BigDecimal.ZERO) > 0) {
                        BigDecimal discount = BigDecimal.ONE.subtract(minPrice.divide(avgPrice, 2, RoundingMode.HALF_UP));
                        route.put("isPromotion", discount.compareTo(new BigDecimal("0.2")) >= 0);
                    } else {
                        route.put("isPromotion", false);
                    }
                } else {
                    route.put("isPromotion", false);
                }
            }

            // 保存到缓存
            hotRoutesCache.put(limit, routes);
            hotRoutesLoaded.set(true);
            log.info("热门航线数据加载完成，共 {} 条", routes.size());
        } catch (Exception e) {
            log.error("加载热门航线数据时发生错误", e);
            hotRoutesCache.put(limit, new ArrayList<>());
        }
    }
    
    /**
     * 加载特价机票数据
     * @param limit 加载的特价机票数量
     */
    private synchronized void loadPromotionTickets(Integer limit) {
        // 检查是否已经加载过
        if (promotionTicketsCache.containsKey(limit)) {
            return;
        }
        
        log.info("从数据库加载特价机票数据，数量: {}...", limit);
        try {
            // 获取当前日期（月和日）
            Calendar today = Calendar.getInstance();
            int currentMonth = today.get(Calendar.MONTH) + 1; // 月份从0开始
            int currentDay = today.get(Calendar.DAY_OF_MONTH);
            String currentMonthDayStr = String.format("%02d-%02d", currentMonth, currentDay);
            
            // 查询各航线平均价格
            String avgPriceSql = "SELECT " +
                    "departure_city, arrival_city, AVG(price) as avg_price " +
                    "FROM flight_data " +
                    "WHERE departure_city IS NOT NULL AND arrival_city IS NOT NULL " +
                    "AND is_deleted = 0 " +
                    "GROUP BY departure_city, arrival_city";
            
            // 执行查询获取各航线平均价格
            Map<String, BigDecimal> routeAvgPriceMap = new ConcurrentHashMap<>();
            List<Map<String, Object>> avgPrices = jdbcTemplate.queryForList(avgPriceSql);
            for (Map<String, Object> avgPrice : avgPrices) {
                String routeKey = avgPrice.get("departure_city") + "_" + avgPrice.get("arrival_city");
                BigDecimal price = new BigDecimal(avgPrice.get("avg_price").toString());
                routeAvgPriceMap.put(routeKey, price);
            }
            
            // 查询低于平均价格20%的机票，只查询当前日期之后的机票
            String sql = "SELECT " +
                    "f.id AS id, " +
                    "f.departure_city AS departureCity, " +
                    "f.arrival_city AS arrivalCity, " +
                    "f.flight_number AS flightNumber, " +
                    "f.airline AS airline, " +
                    "f.price AS price, " +
                    "f.departure_time AS departureTime, " +
                    "f.arrival_time AS arrivalTime, " +
                    "f.flight_date AS flightDate, " +
                    "f.departure_airport AS departureAirport, " +
                    "f.arrival_airport AS arrivalAirport, " +
                    "f.punctuality_rate AS punctualityRate " +
                    "FROM flight_data f " +
                    "WHERE f.departure_city IS NOT NULL AND f.arrival_city IS NOT NULL " +
                    "AND f.is_deleted = 0 " +
                    "AND ( " +
                    "    CONCAT(SUBSTRING_INDEX(SUBSTRING_INDEX(f.flight_date, '/', 2), '/', -1), '-', " +
                    "    SUBSTRING_INDEX(f.flight_date, '/', -1)) > ? " +
                    ") " +
                    "ORDER BY f.price ASC " + 
                    "LIMIT ?";

            // 使用JdbcTemplate执行SQL查询
            List<Map<String, Object>> tickets = jdbcTemplate.queryForList(sql, 
                    currentMonthDayStr, limit * 3); // 查询更多数据，然后筛选

            // 处理结果：计算折扣率，添加是否是特价标志
            List<Map<String, Object>> promotionTickets = new ArrayList<>();
            for (Map<String, Object> ticket : tickets) {
                String departureCity = (String) ticket.get("departureCity");
                String arrivalCity = (String) ticket.get("arrivalCity");
                String routeKey = departureCity + "_" + arrivalCity;
                
                BigDecimal avgPrice = routeAvgPriceMap.get(routeKey);
                BigDecimal currentPrice = (BigDecimal) ticket.get("price");
                
                if (avgPrice != null && currentPrice != null && avgPrice.compareTo(BigDecimal.ZERO) > 0) {
                    // 计算折扣率
                    BigDecimal discountRate = BigDecimal.ONE.subtract(
                            currentPrice.divide(avgPrice, 2, RoundingMode.HALF_UP));
                    
                    // 设置折扣率（按百分比）
                    ticket.put("discountRate", discountRate.multiply(new BigDecimal("100")).setScale(0, RoundingMode.HALF_UP));
                    
                    // 设置平均价（取整）
                    ticket.put("avgPrice", avgPrice.setScale(0, RoundingMode.HALF_UP));
                    
                    // 判断是否为特价（折扣率>=20%）
                    boolean isPromotion = discountRate.compareTo(new BigDecimal("0.2")) >= 0;
                    ticket.put("isPromotion", isPromotion);
                    
                    // 只添加特价机票
                    if (isPromotion && promotionTickets.size() < limit) {
                        promotionTickets.add(ticket);
                    }
                }
            }

            // 按折扣率降序排序
            promotionTickets.sort((a, b) -> {
                BigDecimal discountA = (BigDecimal) a.get("discountRate");
                BigDecimal discountB = (BigDecimal) b.get("discountRate");
                return discountB.compareTo(discountA); // 降序
            });

            // 保存到缓存
            promotionTicketsCache.put(limit, promotionTickets);
            promotionTicketsLoaded.set(true);
            log.info("特价机票数据加载完成，共 {} 条", promotionTickets.size());
        } catch (Exception e) {
            log.error("加载特价机票数据时发生错误", e);
            promotionTicketsCache.put(limit, new ArrayList<>());
        }
    }
    
    /**
     * 获取热门航线数据
     * @param limit 获取的航线数量
     * @return 热门航线数据列表
     */
    public List<Map<String, Object>> getHotRoutes(Integer limit) {
        if (!hotRoutesCache.containsKey(limit)) {
            if (!hotRoutesLoaded.get()) {
                // 异步加载，先返回空列表
                CompletableFuture.runAsync(() -> loadHotRoutes(limit));
                return Collections.emptyList();
            }
            loadHotRoutes(limit);
        }
        return hotRoutesCache.getOrDefault(limit, Collections.emptyList());
    }
    
    /**
     * 获取特价机票数据
     * @param limit 获取的特价机票数量
     * @return 特价机票数据列表
     */
    public List<Map<String, Object>> getPromotionTickets(Integer limit) {
        if (!promotionTicketsCache.containsKey(limit)) {
            if (!promotionTicketsLoaded.get()) {
                // 异步加载，先返回空列表
                CompletableFuture.runAsync(() -> loadPromotionTickets(limit));
                return Collections.emptyList();
            }
            loadPromotionTickets(limit);
        }
        return promotionTicketsCache.getOrDefault(limit, Collections.emptyList());
    }

    /**
     * 使用重试机制执行操作
     * @param operation 要执行的操作
     * @param operationName 操作名称（用于日志）
     * @param maxRetries 最大重试次数
     */
    private void retryOperation(Runnable operation, String operationName, int maxRetries) {
        int attempts = 0;
        boolean success = false;
        Exception lastException = null;
        
        while (!success && attempts < maxRetries) {
            attempts++;
            try {
                operation.run();
                success = true;
                if (attempts > 1) {
                    log.info("{} 在第 {} 次尝试后成功", operationName, attempts);
                }
            } catch (Exception e) {
                lastException = e;
                if (attempts < maxRetries) {
                    // 指数退避策略，增加随机抖动避免多服务同时重试
                    long baseWaitTime = (long) Math.pow(2, attempts) * 1000;
                    long jitter = (long)(baseWaitTime * 0.2 * Math.random());
                    long waitTime = baseWaitTime + jitter;
                    
                    log.warn("{} 第 {} 次尝试失败，将在 {} 毫秒后重试. 错误: {}", 
                             operationName, attempts, waitTime, e.getMessage());
                    try {
                        Thread.sleep(waitTime);
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        log.error("重试等待被中断", ie);
                        break;
                    }
                } else {
                    log.error("{} 在 {} 次尝试后仍然失败: {}", operationName, attempts, e.getMessage());
                    // 不抛出异常，而是捕获并记录，避免整个加载过程中断
                    log.error("最后一次错误详情", e);
                }
            }
        }
        
        // 如果所有尝试都失败，但没有抛出异常，记录详细日志
        if (!success && lastException != null) {
            log.error("所有重试操作都失败: {}", operationName, lastException);
        }
    }
}
