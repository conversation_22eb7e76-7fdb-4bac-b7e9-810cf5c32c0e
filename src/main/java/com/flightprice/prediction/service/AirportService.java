package com.flightprice.prediction.service;

import com.flightprice.prediction.dto.AirportDTO;
import com.flightprice.prediction.dto.AirportStatsDTO;
import com.flightprice.prediction.dto.MonthlyStatsDTO;
import com.flightprice.prediction.entity.Airport;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import com.flightprice.prediction.common.api.CommonPage;

import java.util.List;
import java.util.Map;

/**
 * 机场服务接口
 */
public interface AirportService {

    /**
     * 根据ID获取机场信息
     */
    Airport getById(Long id);

    /**
     * 获取所有机场列表
     */
    List<Airport> getAllAirports();

    /**
     * 分页查询机场列表
     */
    CommonPage<Airport> list(Integer pageNum, Integer pageSize, String keyword);

    /**
     * 创建机场
     */
    Airport create(AirportDTO airportDTO);

    /**
     * 更新机场信息
     */
    Airport update(Long id, AirportDTO airportDTO);

    /**
     * 删除机场
     */
    void delete(Long id);

    /**
     * 批量删除机场
     */
    void deleteBatch(List<Long> ids);

    /**
     * 分页查询机场列表
     *
     * @param keyword 关键字
     * @param pageable 分页参数
     * @return 机场分页列表
     */
    Page<Airport> findAll(String keyword, Pageable pageable);

    /**
     * 根据ID查询机场
     *
     * @param id 机场ID
     * @return 机场
     */
    Airport findById(Long id);

    /**
     * 根据IATA代码查询机场
     *
     * @param iataCode IATA代码
     * @return 机场
     */
    Airport findByIataCode(String iataCode);

    /**
     * 根据ICAO代码查询机场
     *
     * @param icaoCode ICAO代码
     * @return 机场
     */
    Airport findByIcaoCode(String icaoCode);

    /**
     * 根据城市查询机场列表
     *
     * @param city 城市
     * @return 机场列表
     */
    List<Airport> findByCity(String city);

    /**
     * 创建机场
     *
     * @param airportDTO 机场DTO
     * @return 创建的机场
     */
    Airport createAirport(AirportDTO airportDTO);

    /**
     * 更新机场
     *
     * @param id 机场ID
     * @param airportDTO 机场DTO
     * @return 更新后的机场
     */
    Airport updateAirport(Long id, AirportDTO airportDTO);

    /**
     * 删除机场
     *
     * @param id 机场ID
     */
    void deleteAirport(Long id);

    /**
     * 启用/禁用机场
     *
     * @param id 机场ID
     * @param enabled 是否启用
     * @return 更新后的机场
     */
    Airport updateStatus(Long id, Boolean enabled);

    /**
     * 获取机场统计数据
     *
     * @param id 机场ID
     * @return 统计数据
     */
    Map<String, Object> getStatistics(Long id);

    /**
     * 获取机场统计数据
     *
     * @param id 机场ID
     * @return 机场统计数据
     */
    AirportStatsDTO getAirportStats(Long id);

    /**
     * 获取机场月度统计数据
     *
     * @param id 机场ID
     * @param year 年份
     * @return 月度统计数据
     */
    MonthlyStatsDTO getAirportMonthlyStats(Long id, Integer year);
}