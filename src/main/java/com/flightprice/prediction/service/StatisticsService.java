package com.flightprice.prediction.service;

import com.flightprice.prediction.vo.StatisticsVO;

import java.util.Map;

/**
 * 统计服务接口
 */
public interface StatisticsService {

    /**
     * 获取仪表盘统计数据
     *
     * @return 统计数据
     */
    StatisticsVO getDashboardStatistics();

    /**
     * 获取按城市统计的订单数据
     *
     * @return 统计数据
     */
    StatisticsVO getCityOrderStatistics();

    /**
     * 获取按航线统计的订单数据
     *
     * @return 统计数据
     */
    StatisticsVO getRouteOrderStatistics();

    /**
     * 获取按舱位统计的订单数据
     *
     * @return 统计数据
     */
    StatisticsVO getCabinOrderStatistics();

    /**
     * 获取订单趋势统计数据
     *
     * @param days 天数
     * @return 统计数据
     */
    StatisticsVO getOrderTrendStatistics(Integer days);

    /**
     * 获取价格因素影响分析数据
     *
     * @return 统计数据
     */
    StatisticsVO getPriceFactorStatistics();

    /**
     * 获取模型准确率统计数据
     *
     * @return 统计数据
     */
    StatisticsVO getModelAccuracyStatistics();
    
    /**
     * 获取价格趋势数据
     *
     * @return 价格趋势数据
     */
    Map<String, Object> getPriceTrendData();
} 