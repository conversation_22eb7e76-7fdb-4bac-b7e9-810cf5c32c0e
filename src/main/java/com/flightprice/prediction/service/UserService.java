package com.flightprice.prediction.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.flightprice.prediction.dto.BindEmailRequest;
import com.flightprice.prediction.dto.BindPhoneRequest;
import com.flightprice.prediction.dto.ChangePasswordRequest;
import com.flightprice.prediction.dto.LoginDTO;
import com.flightprice.prediction.dto.LoginRequest;
import com.flightprice.prediction.dto.LoginResponse;
import com.flightprice.prediction.dto.RegisterDTO;
import com.flightprice.prediction.dto.RegisterRequest;
import com.flightprice.prediction.dto.UpdateUserInfoRequest;
import com.flightprice.prediction.dto.UserDTO;
import com.flightprice.prediction.entity.User;
import com.flightprice.prediction.vo.LoginVO;
import com.flightprice.prediction.vo.UserVO;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.flightprice.prediction.dto.AssignUserRolesRequest;
import com.flightprice.prediction.dto.UserPermissionsDTO;

import java.util.List;
import java.util.Optional;
import java.util.Set;

/**
 * 用户服务接口
 */
public interface UserService extends IService<User> {

    /**
     * 用户登录
     *
     * @param request 登录请求
     * @return 登录响应
     */
    LoginResponse login(LoginRequest request);

    /**
     * 用户注册
     *
     * @param request 注册请求
     * @return 用户ID
     */
    Long register(RegisterRequest request);

    /**
     * 获取当前登录用户
     *
     * @return 用户
     */
    User getCurrentUser();

    /**
     * 获取当前用户信息
     *
     * @return 用户信息
     */
    UserVO getCurrentUserInfo();

    /**
     * 更新用户信息
     *
     * @param request 更新用户信息请求
     * @return 是否成功
     */
    boolean updateUserInfo(UpdateUserInfoRequest request);

    /**
     * 修改密码
     *
     * @param request 修改密码请求
     * @return 是否成功
     */
    boolean changePassword(ChangePasswordRequest request);

    /**
     * 登录
     */
    LoginVO login(LoginDTO loginDTO);

    /**
     * 注册
     */
    boolean register(RegisterDTO registerDTO);

    /**
     * 使用JPA保存用户 - 用于简单操作
     */
    User saveUserWithJpa(User user);

    /**
     * 使用MyBatis-Plus保存用户 - 可与其他MyBatis操作组合
     */
    boolean saveUserWithMybatis(User user);

    /**
     * 使用JPA查询 - 简单查询，利用JPA自动生成查询方法
     */
    Optional<User> findUserByUsername(String username);

    /**
     * 使用MyBatis-Plus进行复杂查询
     */
    List<User> findUsersByCondition(String role, String keyword);

    /**
     * 分页查询用户列表
     *
     * @param keyword 关键字
     * @param pageable 分页参数
     * @return 用户分页列表
     */
    Page<UserVO> getUserPage(String keyword, Pageable pageable);

    /**
     * 根据ID获取用户详情
     *
     * @param id 用户ID
     * @return 用户详情
     */
    UserDTO getUserById(Long id);

    /**
     * 创建用户
     *
     * @param userDTO 用户DTO
     * @return 用户ID
     */
    Long createUser(UserDTO userDTO);

    /**
     * 更新用户
     *
     * @param userDTO 用户DTO
     * @return 是否成功
     */
    boolean updateUser(UserDTO userDTO);

    /**
     * 删除用户
     *
     * @param id 用户ID
     * @return 是否成功
     */
    boolean deleteUser(Long id);

    /**
     * 批量删除用户
     *
     * @param ids 用户ID列表
     * @return 是否成功
     */
    boolean batchDeleteUser(List<Long> ids);

    /**
     * 启用/禁用用户
     *
     * @param id 用户ID
     * @param enabled 是否启用
     * @return 是否成功
     */
    boolean enableUser(Long id, Boolean enabled);

    /**
     * 重置密码
     *
     * @param id 用户ID
     * @return 新密码
     */
    String resetPassword(Long id);

    /**
     * 获取用户角色列表
     *
     * @param userId 用户ID
     * @return 角色ID列表
     */
    List<Long> getUserRoleIds(Long userId);

    /**
     * 分配用户角色
     *
     * @param userId 用户ID
     * @param roleIds 角色ID列表
     * @return 是否成功
     */
    boolean assignUserRoles(Long userId, List<Long> roleIds);

    /**
     * 分页查询用户列表
     *
     * @param page    页码
     * @param limit   每页条数
     * @param keyword 搜索关键字
     * @param role    角色
     * @return 用户分页列表
     */
    IPage<UserDTO> getUserPage(Integer page, Integer limit, String keyword, String role);

    /**
     * 获取用户详情
     *
     * @param id 用户ID
     * @return 用户详情
     */
    UserDTO getUserDetail(Long id);

    /**
     * 获取当前登录用户信息
     *
     * @return 当前用户信息
     */
    UserDTO getUserInfo();

    /**
     * 创建用户
     *
     * @param userDTO 用户信息
     * @return 创建的用户
     */
    UserDTO createUserWithDetails(UserDTO userDTO);

    /**
     * 更新用户
     *
     * @param id      用户ID
     * @param userDTO 用户信息
     * @return 更新后的用户
     */
    UserDTO updateUser(Long id, UserDTO userDTO);

    /**
     * 删除用户
     *
     * @param id 用户ID
     */
    void removeUser(Long id);

    /**
     * 修改用户密码
     *
     * @param id      用户ID
     * @param request 密码请求
     */
    void changePassword(Long id, ChangePasswordRequest request);

    /**
     * 获取用户权限
     *
     * @param id 用户ID
     * @return 用户权限
     */
    UserPermissionsDTO getUserPermissions(Long id);

    /**
     * 分配用户角色和权限
     *
     * @param request 角色权限请求
     */
    void assignUserRolesAndPermissions(AssignUserRolesRequest request);

    User findById(Long id);

    User findByUsername(String username);

    Page<User> findAll(String keyword, Pageable pageable);

    User createUserEntity(UserDTO userDTO);

    User updateUserEntity(Long id, UserDTO userDTO);

    void updateUserRoles(Long userId, Set<Long> roleIds);

    boolean existsByUsername(String username);

    boolean existsByEmail(String email);

    void updatePassword(Long id, String oldPassword, String newPassword);

    void updateLastLogin(String username);

    /**
     * 绑定手机号
     *
     * @param request 绑定手机号请求
     * @return 是否成功
     */
    boolean bindPhone(BindPhoneRequest request);

    /**
     * 绑定邮箱
     *
     * @param request 绑定邮箱请求
     * @return 是否成功
     */
    boolean bindEmail(BindEmailRequest request);
}