package com.flightprice.prediction.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.flightprice.prediction.dto.TerminalDTO;
import com.flightprice.prediction.entity.Terminal;

/**
 * 航站楼服务接口
 */
public interface TerminalService extends IService<Terminal> {
    
    /**
     * 分页查询航站楼信息
     *
     * @param page 分页参数
     * @param terminalDTO 查询条件
     * @return 分页结果
     */
    IPage<Terminal> queryTerminalPage(Page<Terminal> page, TerminalDTO terminalDTO);
    
    /**
     * 根据ID查询航站楼信息
     *
     * @param id 航站楼ID
     * @return 航站楼信息
     */
    TerminalDTO getTerminalById(Long id);
    
    /**
     * 保存或更新航站楼信息
     *
     * @param terminalDTO 航站楼信息
     * @return 操作结果
     */
    boolean saveOrUpdateTerminal(TerminalDTO terminalDTO);
    
    /**
     * 更新航站楼状态
     *
     * @param id 航站楼ID
     * @param status 状态
     * @return 操作结果
     */
    boolean updateStatus(Long id, Integer status);
    
    /**
     * 删除航站楼
     *
     * @param id 航站楼ID
     * @return 操作结果
     */
    boolean deleteTerminal(Long id);
} 