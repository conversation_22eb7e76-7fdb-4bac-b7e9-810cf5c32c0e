package com.flightprice.prediction.service;

import com.flightprice.prediction.dto.ModelDTO;
import com.flightprice.prediction.dto.ModelTrainingHistoryDTO;
import com.flightprice.prediction.entity.Model;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.List;
import java.util.Map;

/**
 * 预测模型服务接口
 */
public interface ModelService {
    
    /**
     * 分页查询模型列表
     *
     * @param keyword 关键字
     * @param pageable 分页参数
     * @return 模型分页列表
     */
    Page<Model> findAll(String keyword, Pageable pageable);
    
    /**
     * 根据ID查询模型
     *
     * @param id 模型ID
     * @return 模型
     */
    Model findById(Long id);
    
    /**
     * 创建模型
     *
     * @param modelDTO 模型DTO
     * @return 创建的模型
     */
    Model createModel(ModelDTO modelDTO);
    
    /**
     * 更新模型
     *
     * @param id 模型ID
     * @param modelDTO 模型DTO
     * @return 更新后的模型
     */
    Model updateModel(Long id, ModelDTO modelDTO);
    
    /**
     * 删除模型
     *
     * @param id 模型ID
     */
    void deleteModel(Long id);
    
    /**
     * 部署/激活模型
     *
     * @param id 模型ID
     * @return 更新后的模型
     */
    Model deployModel(Long id);
    
    /**
     * 停用模型
     *
     * @param id 模型ID
     * @return 更新后的模型
     */
    Model deactivateModel(Long id);
    
    /**
     * 训练模型
     *
     * @param id 模型ID
     * @param parameters 训练参数
     * @return 训练历史ID
     */
    Long trainModel(Long id, Map<String, Object> parameters);
    
    /**
     * 根据ID获取模型训练历史
     *
     * @param id 训练历史ID
     * @return 训练历史
     */
    ModelTrainingHistoryDTO getTrainingHistory(Long id);
    
    /**
     * 根据模型ID获取训练历史列表
     *
     * @param modelId 模型ID
     * @param pageable 分页参数
     * @return 训练历史分页列表
     */
    Page<ModelTrainingHistoryDTO> getTrainingHistories(Long modelId, Pageable pageable);
    
    /**
     * 获取模型的评估指标
     *
     * @param id 模型ID
     * @return 评估指标
     */
    Map<String, Object> getModelEvaluation(Long id);
    
    /**
     * 获取模型统计数据
     *
     * @param id 模型ID
     * @return 统计数据
     */
    Map<String, Object> getModelStatistics(Long id);
    
    /**
     * 获取当前激活的模型
     *
     * @return 激活的模型
     */
    Model getActiveModel();
    
    /**
     * 根据状态获取模型列表
     *
     * @param status 状态
     * @return 模型列表
     */
    List<Model> getModelsByStatus(String status);
    
    /**
     * 获取所有模型类型
     *
     * @return 模型类型列表
     */
    List<String> getModelTypes();
    
    /**
     * 将模型实体转换为DTO
     *
     * @param model 模型实体
     * @return 模型DTO
     */
    ModelDTO convertToDTO(Model model);
} 