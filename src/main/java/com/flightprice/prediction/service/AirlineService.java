package com.flightprice.prediction.service;

import com.flightprice.prediction.dto.AirlineDTO;
import com.flightprice.prediction.dto.AirlineStatDTO;
import com.flightprice.prediction.entity.Airline;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.List;
import java.util.Map;

/**
 * 航空公司服务接口
 */
public interface AirlineService {
    
    /**
     * 分页查询航空公司列表
     *
     * @param keyword 关键字
     * @param pageable 分页参数
     * @return 航空公司分页列表
     */
    Page<Airline> findAll(String keyword, Pageable pageable);
    
    /**
     * 根据ID查询航空公司
     *
     * @param id 航空公司ID
     * @return 航空公司
     */
    Airline findById(Long id);
    
    /**
     * 根据代码查询航空公司
     *
     * @param code 航空公司代码
     * @return 航空公司
     */
    Airline findByCode(String code);
    
    /**
     * 创建航空公司
     *
     * @param airlineDTO 航空公司DTO
     * @return 创建的航空公司
     */
    Airline createAirline(AirlineDTO airlineDTO);
    
    /**
     * 更新航空公司
     *
     * @param id 航空公司ID
     * @param airlineDTO 航空公司DTO
     * @return 更新后的航空公司
     */
    Airline updateAirline(Long id, AirlineDTO airlineDTO);
    
    /**
     * 删除航空公司
     *
     * @param id 航空公司ID
     */
    void deleteAirline(Long id);
    
    /**
     * 启用/禁用航空公司
     *
     * @param id 航空公司ID
     * @param enabled 是否启用
     * @return 更新后的航空公司
     */
    Airline updateStatus(Long id, Boolean enabled);
    
    /**
     * 获取航空公司统计数据
     *
     * @param id 航空公司ID
     * @return 统计数据
     */
    Map<String, Object> getStatistics(Long id);
    
    /**
     * 获取所有航空公司列表
     *
     * @return 航空公司列表
     */
    List<AirlineDTO> getAllAirlines();
    
    /**
     * 获取所有启用的航空公司列表
     *
     * @return 航空公司列表
     */
    List<AirlineDTO> getAllEnabledAirlines();
    
    /**
     * 根据ID获取航空公司详情
     *
     * @param id 航空公司ID
     * @return 航空公司详情
     */
    AirlineDTO getAirlineById(Long id);
    
    /**
     * 根据代码获取航空公司详情
     *
     * @param code 航空公司代码
     * @return 航空公司详情
     */
    AirlineDTO getAirlineByCode(String code);
    
    /**
     * 批量删除航空公司
     *
     * @param ids 航空公司ID列表
     * @return 是否成功
     */
    boolean batchDeleteAirline(List<Long> ids);
    
    /**
     * 获取航空公司每月航班数量统计
     *
     * @param id 航空公司ID
     * @param year 年份
     * @return 每月航班数量统计
     */
    AirlineStatDTO getAirlineMonthlyStats(Long id, Integer year);
    
    /**
     * 将实体转换为DTO
     *
     * @param airline 航空公司实体
     * @return 航空公司DTO
     */
    AirlineDTO convertToDTO(Airline airline);
    
    /**
     * 将DTO转换为实体
     *
     * @param airlineDTO 航空公司DTO
     * @return 航空公司实体
     */
    Airline convertToEntity(AirlineDTO airlineDTO);
} 