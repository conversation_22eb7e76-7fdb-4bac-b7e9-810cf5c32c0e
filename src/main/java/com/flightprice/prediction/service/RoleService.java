package com.flightprice.prediction.service;

import com.flightprice.prediction.dto.RoleDTO;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.List;

/**
 * 角色服务接口
 */
public interface RoleService {
    
    /**
     * 分页查询角色列表
     *
     * @param keyword 关键字
     * @param pageable 分页参数
     * @return 角色分页列表
     */
    Page<RoleDTO> getRolePage(String keyword, Pageable pageable);
    
    /**
     * 获取所有角色列表
     *
     * @return 角色列表
     */
    List<RoleDTO> getAllRoles();
    
    /**
     * 根据ID获取角色详情
     *
     * @param id 角色ID
     * @return 角色详情
     */
    RoleDTO getRoleById(Long id);
    
    /**
     * 创建角色
     *
     * @param roleDTO 角色DTO
     * @return 角色ID
     */
    Long createRole(RoleDTO roleDTO);
    
    /**
     * 更新角色
     *
     * @param roleDTO 角色DTO
     * @return 是否成功
     */
    boolean updateRole(RoleDTO roleDTO);
    
    /**
     * 删除角色
     *
     * @param id 角色ID
     * @return 是否成功
     */
    boolean deleteRole(Long id);
    
    /**
     * 批量删除角色
     *
     * @param ids 角色ID列表
     * @return 是否成功
     */
    boolean batchDeleteRole(List<Long> ids);
    
    /**
     * 获取角色权限列表
     *
     * @param roleId 角色ID
     * @return 权限ID列表
     */
    List<Long> getRolePermissionIds(Long roleId);
    
    /**
     * 分配角色权限
     *
     * @param roleId 角色ID
     * @param permissionIds 权限ID列表
     * @return 是否成功
     */
    boolean assignRolePermissions(Long roleId, List<Long> permissionIds);
} 