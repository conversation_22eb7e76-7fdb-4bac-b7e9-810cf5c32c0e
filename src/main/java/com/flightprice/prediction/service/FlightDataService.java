package com.flightprice.prediction.service;

import com.flightprice.prediction.entity.FlightData;
import com.baomidou.mybatisplus.extension.service.IService;
import com.flightprice.prediction.common.PageResult;
import com.flightprice.prediction.vo.FlightDataVO;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 航班数据服务接口
 */
public interface FlightDataService extends IService<FlightData> {

    /**
     * 从Excel导入航班数据
     * @param filePath Excel文件路径
     * @return 导入的数据条数
     */
    int importFromExcel(String filePath);

    /**
     * 批量保存航班数据
     * @param dataList 航班数据列表
     * @return 保存的数据条数
     */
    int batchSave(List<FlightData> dataList);
    
    /**
     * 使用Spark进行数据分析，获取航线价格统计
     *
     * @param departureCity 出发城市
     * @param arrivalCity 到达城市
     * @return 分析结果
     */
    List<FlightData> analyzeRoutePrices(String departureCity, String arrivalCity);
    
    /**
     * 使用Spark处理Excel数据并提取特征
     *
     * @param excelFilePath Excel文件路径
     * @return 处理后的数据集
     */
    List<FlightData> processAndExtractFeatures(String excelFilePath);

    /**
     * 获取所有出发城市
     * @return 出发城市列表
     */
    List<String> getAllDepartureCities();
    
    /**
     * 获取所有到达城市
     * @return 到达城市列表
     */
    List<String> getAllArrivalCities();
    
    /**
     * 获取所有航空公司
     * @return 航空公司列表
     */
    List<String> getAllAirlines();
    
    /**
     * 根据出发城市获取可到达的城市
     * @param departureCity 出发城市
     * @return 可到达的城市列表
     */
    List<String> getArrivalCitiesByDepartureCity(String departureCity);
    
    /**
     * 根据条件搜索航班数据
     * @param departureCity 出发城市
     * @param arrivalCity 到达城市
     * @param departureDate 出发日期
     * @param airline 航空公司（可选）
     * @param pageNum 页码
     * @param pageSize 每页大小
     * @return 航班数据分页结果
     */
    PageResult<FlightDataVO> searchFlightData(String departureCity, String arrivalCity, 
                                        Date departureDate, String airline, 
                                        Integer pageNum, Integer pageSize);

    /**
     * 获取指定月份有航班的日期
     * @param departureCity 出发城市
     * @param arrivalCity 到达城市
     * @param year 年份
     * @param month 月份（1-12）
     * @return 有航班的日期集合（日期格式：dd）
     */
    Set<String> getAvailableDatesForMonth(String departureCity, String arrivalCity, int year, int month);
    
    /**
     * 根据ID获取航班详情
     * @param id 航班ID
     * @return 航班详情VO
     */
    FlightDataVO getFlightById(Long id);
    
    /**
     * 获取热门航线列表
     * 根据航班数量、平均价格等计算出热门航线
     * @param limit 返回的热门航线数量
     * @return 热门航线列表，每条包含出发城市、到达城市、航班数量和平均价格等信息
     */
    List<Map<String, Object>> getHotRoutes(Integer limit);
    
    /**
     * 获取特价机票列表
     * 返回低于平均价格20%以上的特价机票
     * @param limit 返回的特价机票数量
     * @return 特价机票列表，每条包含出发城市、到达城市、航班号、价格、折扣率等信息
     */
    List<Map<String, Object>> getPromotionTickets(Integer limit);

    /**
     * 获取航班时刻表数据
     * @param departureCity 出发城市（可选）
     * @param arrivalCity 到达城市（可选）
     * @param date 日期（可选），格式：yyyy-MM-dd
     * @param pageNum 页码
     * @param pageSize 每页大小
     * @return 航班时刻表数据
     */
    PageResult<Map<String, Object>> getFlightSchedules(String departureCity, String arrivalCity, 
                                               String date, Integer pageNum, Integer pageSize);
    
    /**
     * 获取季节性航班数据
     * 季节性航班是指只在特定季节运行的航班，通常包括假期、节日或旅游季节专门增开的航班
     * @param season 季节（可选，如：春季、夏季、秋季、冬季）
     * @param pageNum 页码
     * @param pageSize 每页大小
     * @return 季节性航班数据
     */
    PageResult<Map<String, Object>> getSeasonalFlights(String season, Integer pageNum, Integer pageSize);
    
    /**
     * 获取热门城市航班数据
     * @param departureCity 出发城市
     * @param arrivalCity 到达城市
     * @param pageNum 页码
     * @param pageSize 每页大小
     * @return 热门城市航班数据
     */
    PageResult<Map<String, Object>> getInternationalFlights(String departureCity, String arrivalCity,
                                                   Integer pageNum, Integer pageSize);
    
    /**
     * 执行自定义SQL查询
     * @param sql SQL语句
     * @param params SQL参数
     * @return 查询结果
     */
    List<Map<String, Object>> executeQuery(String sql, Object[] params);
} 