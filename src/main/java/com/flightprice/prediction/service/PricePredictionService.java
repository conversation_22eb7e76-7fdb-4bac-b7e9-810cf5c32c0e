package com.flightprice.prediction.service;

import com.flightprice.prediction.dto.PricePredictionRequest;
import com.flightprice.prediction.vo.PricePredictionVO;

import java.util.Map;
import java.util.function.Consumer;

/**
 * 价格预测服务接口
 */
public interface PricePredictionService {
    
    /**
     * 预测价格
     */
    PricePredictionVO predictPrice(PricePredictionRequest request);

    /**
     * 训练模型
     */
    Long trainModel(Long routeId);

    /**
     * 收集价格数据
     */
    void collectPriceData();

    /**
     * 更新价格因素
     */
    void updatePriceFactors(Long routeId);

    /**
     * 训练模型
     * @param dataMap 训练数据
     * @param trainingRatio 训练集比例
     * @param hyperParameters 超参数
     * @param autoTune 是否自动调优
     * @param progressCallback 进度回调
     * @return 训练结果
     */
    Map<String, Object> train(Map<String, Object> dataMap, Double trainingRatio, 
                            Map<String, Object> hyperParameters, Boolean autoTune,
                            Consumer<Double> progressCallback);

    /**
     * 预测价格
     * @param featureMap 特征数据
     * @return 预测结果
     */
    Map<String, Object> predict(Map<String, Object> featureMap);

    /**
     * 评估模型
     * @param testDataMap 测试数据
     * @return 评估结果
     */
    Map<String, Object> evaluate(Map<String, Object> testDataMap);
} 