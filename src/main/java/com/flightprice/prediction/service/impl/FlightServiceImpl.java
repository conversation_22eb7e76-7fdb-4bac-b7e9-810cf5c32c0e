package com.flightprice.prediction.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.flightprice.prediction.common.PageResult;
import com.flightprice.prediction.dto.FlightSearchRequest;
import com.flightprice.prediction.entity.Airline;
import com.flightprice.prediction.entity.Airport;
import com.flightprice.prediction.entity.City;
import com.flightprice.prediction.entity.Flight;
import com.flightprice.prediction.entity.Route;
import com.flightprice.prediction.entity.Ticket;
import com.flightprice.prediction.entity.FlightPriceHistory;
import com.flightprice.prediction.exception.BusinessException;
import com.flightprice.prediction.mapper.AirlineMapper;
import com.flightprice.prediction.mapper.AirportMapper;
import com.flightprice.prediction.mapper.CityMapper;
import com.flightprice.prediction.mapper.FlightMapper;
import com.flightprice.prediction.mapper.RouteMapper;
import com.flightprice.prediction.mapper.TicketMapper;
import com.flightprice.prediction.mapper.FlightPriceHistoryMapper;
import com.flightprice.prediction.service.FlightService;
import com.flightprice.prediction.vo.FlightVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.Random;

/**
 * 航班服务实现类
 */
@Slf4j
@Service
public class FlightServiceImpl extends ServiceImpl<FlightMapper, Flight> implements FlightService {

    private final AirlineMapper airlineMapper;
    private final AirportMapper airportMapper;
    private final CityMapper cityMapper;
    private final RouteMapper routeMapper;
    private final TicketMapper ticketMapper;
    private final FlightPriceHistoryMapper flightPriceHistoryMapper;

    public FlightServiceImpl(AirlineMapper airlineMapper, AirportMapper airportMapper, CityMapper cityMapper,
                             RouteMapper routeMapper, TicketMapper ticketMapper,
                             FlightPriceHistoryMapper flightPriceHistoryMapper) {
        this.airlineMapper = airlineMapper;
        this.airportMapper = airportMapper;
        this.cityMapper = cityMapper;
        this.routeMapper = routeMapper;
        this.ticketMapper = ticketMapper;
        this.flightPriceHistoryMapper = flightPriceHistoryMapper;
    }

    @Override
    @Transactional
    public PageResult<FlightVO> searchFlights(FlightSearchRequest request) {
        // 构建查询条件
        LambdaQueryWrapper<Flight> queryWrapper = new LambdaQueryWrapper<>();

        // 处理城市名称
        if (request.getDepartureCityId() == null && request.getDepartureCity() != null) {
            // 根据城市名称查询城市ID
            LambdaQueryWrapper<City> cityQuery = new LambdaQueryWrapper<>();
            cityQuery.eq(City::getName, request.getDepartureCity());
            City departureCity = cityMapper.selectOne(cityQuery);
            if (departureCity != null) {
                request.setDepartureCityId(departureCity.getId());
                log.info("根据城市名称 {} 找到城市ID: {}", request.getDepartureCity(), departureCity.getId());
            } else {
                // 如果找不到城市，返回空结果
                log.warn("找不到出发城市: {}", request.getDepartureCity());
                return PageResult.empty(request.getPageNum(), request.getPageSize());
            }
        }

        if (request.getArrivalCityId() == null && request.getArrivalCity() != null) {
            // 根据城市名称查询城市ID
            LambdaQueryWrapper<City> cityQuery = new LambdaQueryWrapper<>();
            cityQuery.eq(City::getName, request.getArrivalCity());
            City arrivalCity = cityMapper.selectOne(cityQuery);
            if (arrivalCity != null) {
                request.setArrivalCityId(arrivalCity.getId());
                log.info("根据城市名称 {} 找到城市ID: {}", request.getArrivalCity(), arrivalCity.getId());
            } else {
                // 如果找不到城市，返回空结果
                log.warn("找不到到达城市: {}", request.getArrivalCity());
                return PageResult.empty(request.getPageNum(), request.getPageSize());
            }
        }

        // 根据出发城市和到达城市查询
        if (request.getDepartureCityId() != null && request.getArrivalCityId() != null) {
            // 查询出发城市的所有机场
            List<Airport> departureAirports = airportMapper.selectList(
                    new LambdaQueryWrapper<Airport>()
                            .eq(Airport::getCityId, request.getDepartureCityId())
            );
            List<Long> departureAirportIds = departureAirports.stream()
                    .map(Airport::getId)
                    .collect(Collectors.toList());

            // 查询到达城市的所有机场
            List<Airport> arrivalAirports = airportMapper.selectList(
                    new LambdaQueryWrapper<Airport>()
                            .eq(Airport::getCityId, request.getArrivalCityId())
            );
            List<Long> arrivalAirportIds = arrivalAirports.stream()
                    .map(Airport::getId)
                    .collect(Collectors.toList());

            // 添加机场条件
            if (!departureAirportIds.isEmpty() && !arrivalAirportIds.isEmpty()) {
                queryWrapper.in(Flight::getDepartureAirportId, departureAirportIds)
                        .in(Flight::getArrivalAirportId, arrivalAirportIds);
            } else {
                // 如果没有找到机场，返回空结果
                log.warn("出发城市或到达城市没有关联的机场");
                return PageResult.empty(request.getPageNum(), request.getPageSize());
            }
        }

        // 根据出发日期查询
        if (request.getDepartureDate() != null) {
            queryWrapper.ge(Flight::getDepartureTime, request.getDepartureDate())
                    .lt(Flight::getDepartureTime, getNextDay(request.getDepartureDate()));
        }

        // 分页查询
        Page<Flight> page = new Page<>(request.getPageNum(), request.getPageSize());
        page(page, queryWrapper);

        // 转换结果
        List<FlightVO> flightVOList = new ArrayList<>();
        for (Flight flight : page.getRecords()) {
            FlightVO flightVO = convertToFlightVO(flight, request.getCabinClass());
            
            // 获取今天的价格历史数据
            List<FlightPriceHistory> priceHistories = flightPriceHistoryMapper.selectList(
                new LambdaQueryWrapper<FlightPriceHistory>()
                    .eq(FlightPriceHistory::getFlightId, flight.getId())
                    .eq(FlightPriceHistory::getCabinClass, request.getCabinClass())
                    .eq(FlightPriceHistory::getCollectDate, new Date())
            );
            
            // 计算平均价格
            if (!priceHistories.isEmpty()) {
                BigDecimal avgPrice = priceHistories.stream()
                    .map(FlightPriceHistory::getPrice)
                    .reduce(BigDecimal.ZERO, BigDecimal::add)
                    .divide(new BigDecimal(priceHistories.size()), 2, RoundingMode.HALF_UP);
                
                // 根据舱位等级设置价格
                switch (request.getCabinClass()) {
                    case "ECONOMY":
                        flightVO.setEconomyPrice(avgPrice);
                        break;
                    case "BUSINESS":
                        flightVO.setBusinessPrice(avgPrice);
                        break;
                    case "FIRST":
                        flightVO.setFirstPrice(avgPrice);
                        break;
                }
                flightVO.setPrice(avgPrice);
            }
            
            flightVOList.add(flightVO);
        }

        return PageResult.build(flightVOList, page.getTotal(), request.getPageNum(), request.getPageSize());
    }

    @Override
    public FlightVO getFlightDetail(Long id) {
        // 检查ID是否为空
        if (id == null) {
            throw new BusinessException("航班 ID 不能为空");
        }

        // 查询航班
        Flight flight = getById(id);
        if (flight == null) {
            throw new BusinessException("航班不存在");
        }

        // 转换为VO
        return convertToFlightVO(flight, null);
    }

    @Override
    @Transactional
    public Long addFlight(Flight flight) {
        // 检查航班号是否已存在
        long count = count(new LambdaQueryWrapper<Flight>()
                .eq(Flight::getFlightNumber, flight.getFlightNumber())
                .eq(Flight::getDepartureTime, flight.getDepartureTime()));
        if (count > 0) {
            throw new BusinessException("航班号已存在");
        }

        // 设置创建时间和更新时间
        Date now = new Date();
        flight.setCreateTime(now);
        flight.setUpdateTime(now);

        // 保存航班
        save(flight);

        return flight.getId();
    }

    @Override
    @Transactional
    public void updateFlight(Flight flight) {
        // 检查航班是否存在
        Flight existingFlight = getById(flight.getId());
        if (existingFlight == null) {
            throw new BusinessException("航班不存在");
        }

        // 检查航班号是否已存在（排除自身）
        long count = count(new LambdaQueryWrapper<Flight>()
                .eq(Flight::getFlightNumber, flight.getFlightNumber())
                .eq(Flight::getDepartureTime, flight.getDepartureTime())
                .ne(Flight::getId, flight.getId()));
        if (count > 0) {
            throw new BusinessException("航班号已存在");
        }

        // 设置更新时间
        flight.setUpdateTime(new Date());

        // 更新航班
        updateById(flight);
    }

    @Override
    @Transactional
    public void deleteFlight(Long id) {
        // 检查航班是否存在
        Flight flight = getById(id);
        if (flight == null) {
            throw new BusinessException("航班不存在");
        }

        // 检查是否有关联的机票
        long ticketCount = ticketMapper.selectCount(new LambdaQueryWrapper<Ticket>()
                .eq(Ticket::getFlightId, id));
        if (ticketCount > 0) {
            throw new BusinessException("该航班已有机票销售，无法删除");
        }

        // 删除航班
        removeById(id);
    }

    @Override
    public PageResult<FlightVO> getFlightTemplates(Integer pageNum, Integer pageSize, Long airlineId, Long departureCityId, Long arrivalCityId) {
        // 构建查询条件
        LambdaQueryWrapper<Flight> queryWrapper = new LambdaQueryWrapper<>();
        
        // 添加航空公司条件
        if (airlineId != null) {
            queryWrapper.eq(Flight::getAirlineId, airlineId);
        }
        
        // 根据出发城市和到达城市查询
        if (departureCityId != null || arrivalCityId != null) {
            // 如果指定了出发城市，查询该城市的所有机场
            if (departureCityId != null) {
                List<Airport> departureAirports = airportMapper.selectList(
                        new LambdaQueryWrapper<Airport>()
                                .eq(Airport::getCityId, departureCityId)
                );
                if (!departureAirports.isEmpty()) {
                    List<Long> departureAirportIds = departureAirports.stream()
                            .map(Airport::getId)
                            .collect(Collectors.toList());
                    queryWrapper.in(Flight::getDepartureAirportId, departureAirportIds);
                }
            }
            
            // 如果指定了到达城市，查询该城市的所有机场
            if (arrivalCityId != null) {
                List<Airport> arrivalAirports = airportMapper.selectList(
                        new LambdaQueryWrapper<Airport>()
                                .eq(Airport::getCityId, arrivalCityId)
                );
                if (!arrivalAirports.isEmpty()) {
                    List<Long> arrivalAirportIds = arrivalAirports.stream()
                            .map(Airport::getId)
                            .collect(Collectors.toList());
                    queryWrapper.in(Flight::getArrivalAirportId, arrivalAirportIds);
                }
            }
        }
        
        // 分页查询
        Page<Flight> page = new Page<>(pageNum, pageSize);
        page(page, queryWrapper);
        
        // 转换结果
        List<FlightVO> flightTemplateList = new ArrayList<>();
        for (Flight flight : page.getRecords()) {
            FlightVO flightVO = convertToFlightVO(flight, null);
            flightTemplateList.add(flightVO);
        }
        
        return PageResult.build(flightTemplateList, page.getTotal(), pageNum, pageSize);
    }
    
    @Override
    public FlightVO getFlightTemplateDetail(Long id) {
        // 检查ID是否为空
        if (id == null) {
            throw new BusinessException("航班模板 ID 不能为空");
        }
        
        // 查询航班模板
        Flight flightTemplate = getById(id);
        if (flightTemplate == null) {
            throw new BusinessException("航班模板不存在");
        }
        
        // 转换为VO并返回
        return convertToFlightVO(flightTemplate, null);
    }
    
    @Override
    @Transactional
    public Long addFlightTemplate(Flight flightTemplate) {
        // 检查航班号是否已存在
        long count = count(new LambdaQueryWrapper<Flight>()
                .eq(Flight::getFlightNumber, flightTemplate.getFlightNumber())
                .eq(Flight::getDepartureTime, flightTemplate.getDepartureTime()));
        if (count > 0) {
            throw new BusinessException("相同航班号和出发时间的航班模板已存在");
        }
        
        // 验证航线信息
        if (flightTemplate.getRouteId() != null) {
            Route route = routeMapper.selectById(flightTemplate.getRouteId());
            if (route == null) {
                throw new BusinessException("指定的航线不存在");
            }
        }
        
        // 验证机场信息
        if (flightTemplate.getDepartureAirportId() != null) {
            Airport departureAirport = airportMapper.selectById(flightTemplate.getDepartureAirportId());
            if (departureAirport == null) {
                throw new BusinessException("指定的出发机场不存在");
            }
        }
        
        if (flightTemplate.getArrivalAirportId() != null) {
            Airport arrivalAirport = airportMapper.selectById(flightTemplate.getArrivalAirportId());
            if (arrivalAirport == null) {
                throw new BusinessException("指定的到达机场不存在");
            }
        }
        
        // 验证航空公司信息
        if (flightTemplate.getAirlineId() != null) {
            Airline airline = airlineMapper.selectById(flightTemplate.getAirlineId());
            if (airline == null) {
                throw new BusinessException("指定的航空公司不存在");
            }
        }
        
        // 设置创建时间和更新时间
        Date now = new Date();
        flightTemplate.setCreateTime(now);
        flightTemplate.setUpdateTime(now);
        
        // 保存航班模板
        save(flightTemplate);
        
        // 预先生成一些机票信息，以便测试
        generateTicketsForFlight(flightTemplate);
        
        return flightTemplate.getId();
    }
    
    @Override
    @Transactional
    public void updateFlightTemplate(Flight flightTemplate) {
        // 检查航班模板是否存在
        Flight existingTemplate = getById(flightTemplate.getId());
        if (existingTemplate == null) {
            throw new BusinessException("航班模板不存在");
        }
        
        // 检查航班号是否已存在（排除自身）
        long count = count(new LambdaQueryWrapper<Flight>()
                .eq(Flight::getFlightNumber, flightTemplate.getFlightNumber())
                .eq(Flight::getDepartureTime, flightTemplate.getDepartureTime())
                .ne(Flight::getId, flightTemplate.getId()));
        if (count > 0) {
            throw new BusinessException("相同航班号和出发时间的航班模板已存在");
        }
        
        // 验证航线信息
        if (flightTemplate.getRouteId() != null) {
            Route route = routeMapper.selectById(flightTemplate.getRouteId());
            if (route == null) {
                throw new BusinessException("指定的航线不存在");
            }
        }
        
        // 验证机场信息
        if (flightTemplate.getDepartureAirportId() != null) {
            Airport departureAirport = airportMapper.selectById(flightTemplate.getDepartureAirportId());
            if (departureAirport == null) {
                throw new BusinessException("指定的出发机场不存在");
            }
        }
        
        if (flightTemplate.getArrivalAirportId() != null) {
            Airport arrivalAirport = airportMapper.selectById(flightTemplate.getArrivalAirportId());
            if (arrivalAirport == null) {
                throw new BusinessException("指定的到达机场不存在");
            }
        }
        
        // 验证航空公司信息
        if (flightTemplate.getAirlineId() != null) {
            Airline airline = airlineMapper.selectById(flightTemplate.getAirlineId());
            if (airline == null) {
                throw new BusinessException("指定的航空公司不存在");
            }
        }
        
        // 设置更新时间
        flightTemplate.setUpdateTime(new Date());
        
        // 更新航班模板
        updateById(flightTemplate);
        
        // 如果航线或舱位信息发生变化，重新生成机票
        if (!existingTemplate.getRouteId().equals(flightTemplate.getRouteId()) || 
            !existingTemplate.getDepartureAirportId().equals(flightTemplate.getDepartureAirportId()) ||
            !existingTemplate.getArrivalAirportId().equals(flightTemplate.getArrivalAirportId())) {
            
            // 先删除已有的机票
            ticketMapper.delete(new LambdaQueryWrapper<Ticket>()
                    .eq(Ticket::getFlightId, flightTemplate.getId()));
            
            // 重新生成机票
            generateTicketsForFlight(flightTemplate);
        }
    }
    
    @Override
    @Transactional
    public void deleteFlightTemplate(Long id) {
        // 检查航班模板是否存在
        Flight flightTemplate = getById(id);
        if (flightTemplate == null) {
            throw new BusinessException("航班模板不存在");
        }
        
        // 查询是否有基于此模板生成的航班
        Calendar templateCal = Calendar.getInstance();
        templateCal.setTime(flightTemplate.getDepartureTime());
        int templateHour = templateCal.get(Calendar.HOUR_OF_DAY);
        int templateMinute = templateCal.get(Calendar.MINUTE);
        
        LambdaQueryWrapper<Flight> queryWrapper = new LambdaQueryWrapper<Flight>()
                .eq(Flight::getFlightNumber, flightTemplate.getFlightNumber())
                .ne(Flight::getId, id);
                
        // 检查是否有机票关联
        long ticketCount = ticketMapper.selectCount(new LambdaQueryWrapper<Ticket>()
                .eq(Ticket::getFlightId, id));
        if (ticketCount > 0) {
            // 删除关联的机票
            ticketMapper.delete(new LambdaQueryWrapper<Ticket>()
                    .eq(Ticket::getFlightId, id));
        }
        
        // 删除航班模板
        removeById(id);
    }

    /**
     * 将航班实体转换为VO
     */
    private FlightVO convertToFlightVO(Flight flight, String cabinClass) {
        FlightVO flightVO = new FlightVO();
        BeanUtils.copyProperties(flight, flightVO);

        // 查询航空公司信息
        Airline airline = airlineMapper.selectById(flight.getAirlineId());
        if (airline != null) {
            flightVO.setAirlineName(airline.getName());
            flightVO.setAirlineCode(airline.getCode());
            flightVO.setAirlineLogo(airline.getLogoUrl());
        }

        // 查询航线信息
        if (flight.getRouteId() != null) {
            Route route = routeMapper.selectById(flight.getRouteId());
            if (route != null) {
                // 设置航线相关城市信息
                City departureCity = cityMapper.selectById(route.getDepartureCityId());
                if (departureCity != null) {
                    flightVO.setDepartureCityId(departureCity.getId());
                    flightVO.setDepartureCityName(departureCity.getName());
                }
                
                City arrivalCity = cityMapper.selectById(route.getArrivalCityId());
                if (arrivalCity != null) {
                    flightVO.setArrivalCityId(arrivalCity.getId());
                    flightVO.setArrivalCityName(arrivalCity.getName());
                }
            }
        }

        // 查询出发机场信息
        Airport departureAirport = airportMapper.selectById(flight.getDepartureAirportId());
        if (departureAirport != null) {
            flightVO.setDepartureAirportName(departureAirport.getName());
            flightVO.setDepartureAirportCode(departureAirport.getCode());

            // 如果航线中没有设置出发城市，则从机场获取
            if (flightVO.getDepartureCityId() == null) {
                City departureCity = cityMapper.selectById(departureAirport.getCityId());
                if (departureCity != null) {
                    flightVO.setDepartureCityId(departureCity.getId());
                    flightVO.setDepartureCityName(departureCity.getName());
                }
            }
        }

        // 查询到达机场信息
        Airport arrivalAirport = airportMapper.selectById(flight.getArrivalAirportId());
        if (arrivalAirport != null) {
            flightVO.setArrivalAirportName(arrivalAirport.getName());
            flightVO.setArrivalAirportCode(arrivalAirport.getCode());

            // 如果航线中没有设置到达城市，则从机场获取
            if (flightVO.getArrivalCityId() == null) {
                City arrivalCity = cityMapper.selectById(arrivalAirport.getCityId());
                if (arrivalCity != null) {
                    flightVO.setArrivalCityId(arrivalCity.getId());
                    flightVO.setArrivalCityName(arrivalCity.getName());
                }
            }
        }

        // 计算飞行时长（分钟）
        if (flight.getDepartureTime() != null && flight.getArrivalTime() != null) {
            long durationMillis = flight.getArrivalTime().getTime() - flight.getDepartureTime().getTime();
            flightVO.setDuration((int) TimeUnit.MILLISECONDS.toMinutes(durationMillis));
        }

        // 查询机票信息
        List<Ticket> tickets = ticketMapper.selectList(new LambdaQueryWrapper<Ticket>()
                .eq(Ticket::getFlightId, flight.getId()));

        // 按舱位等级分组
        Map<String, Ticket> ticketMap = tickets.stream()
                .collect(Collectors.toMap(Ticket::getCabinClass, Function.identity(), (t1, t2) -> t1));

        // 获取航线基础价格
        BigDecimal basePrice = getBasePrice(flight);

        // 设置各舱位价格和座位数
        Ticket economyTicket = ticketMap.get("ECONOMY");
        if (economyTicket != null) {
            flightVO.setEconomyPrice(economyTicket.getPrice());
            flightVO.setEconomySeats(economyTicket.getAvailableSeats());
            flightVO.setEconomyTicketId(economyTicket.getId());
        } else {
            // 生成默认经济舱价格和座位数
            flightVO.setEconomyPrice(basePrice);
            flightVO.setEconomySeats(150);
        }

        Ticket businessTicket = ticketMap.get("BUSINESS");
        if (businessTicket != null) {
            flightVO.setBusinessPrice(businessTicket.getPrice());
            flightVO.setBusinessSeats(businessTicket.getAvailableSeats());
            flightVO.setBusinessTicketId(businessTicket.getId());
        } else {
            // 生成默认商务舱价格和座位数
            flightVO.setBusinessPrice(basePrice.multiply(new BigDecimal("2.5")));
            flightVO.setBusinessSeats(30);
        }

        Ticket firstTicket = ticketMap.get("FIRST");
        if (firstTicket != null) {
            flightVO.setFirstPrice(firstTicket.getPrice());
            flightVO.setFirstSeats(firstTicket.getAvailableSeats());
            flightVO.setFirstTicketId(firstTicket.getId());
        } else {
            // 生成默认头等舱价格和座位数
            flightVO.setFirstPrice(basePrice.multiply(new BigDecimal("4.0")));
            flightVO.setFirstSeats(10);
        }

        // 根据舱位等级设置当前价格和座位数
        if (StringUtils.hasText(cabinClass)) {
            Ticket ticket = ticketMap.get(cabinClass);
            if (ticket != null) {
                flightVO.setPrice(ticket.getPrice());
                flightVO.setAvailableSeats(ticket.getAvailableSeats());
            } else {
                // 根据舱位等级设置默认价格和座位数
                switch (cabinClass) {
                    case "ECONOMY":
                        flightVO.setPrice(flightVO.getEconomyPrice());
                        flightVO.setAvailableSeats(flightVO.getEconomySeats());
                        break;
                    case "BUSINESS":
                        flightVO.setPrice(flightVO.getBusinessPrice());
                        flightVO.setAvailableSeats(flightVO.getBusinessSeats());
                        break;
                    case "FIRST":
                        flightVO.setPrice(flightVO.getFirstPrice());
                        flightVO.setAvailableSeats(flightVO.getFirstSeats());
                        break;
                    default:
                        flightVO.setPrice(flightVO.getEconomyPrice());
                        flightVO.setAvailableSeats(flightVO.getEconomySeats());
                }
            }
        }

        return flightVO;
    }

    /**
     * 获取下一天的日期
     */
    private Date getNextDay(Date date) {
        return new Date(date.getTime() + TimeUnit.DAYS.toMillis(1));
    }

    /**
     * 为航班生成机票
     */
    private void generateTicketsForFlight(Flight flight) {
        // 使用航班ID作为锁的key，确保同一航班的机票生成是互斥的
        String lockKey = "ticket-" + flight.getId();
        synchronized (lockKey.intern()) {
            // 首先检查是否已经存在机票
            List<Ticket> existingTickets = ticketMapper.selectList(
                    new LambdaQueryWrapper<Ticket>()
                            .eq(Ticket::getFlightId, flight.getId())
            );

            // 如果已经存在机票，则不需要重新生成
            if (!existingTickets.isEmpty()) {
                return;
            }

            // 获取航班的基础价格
            BigDecimal basePrice = getBasePrice(flight);

            // 应用出发时间段调整因子
            basePrice = adjustPriceByTimeSlot(basePrice, flight.getDepartureTime());
            
            Date now = new Date();

            try {
                // 生成经济舱机票
                Ticket economyTicket = new Ticket();
                economyTicket.setFlightId(flight.getId());
                economyTicket.setCabinClass("ECONOMY");
                economyTicket.setPrice(basePrice);
                economyTicket.setAvailableSeats(150);
                economyTicket.setDiscount(new BigDecimal("1.0"));
                economyTicket.setCreateTime(now);
                economyTicket.setUpdateTime(now);
                ticketMapper.insert(economyTicket);

                // 生成商务舱机票
                Ticket businessTicket = new Ticket();
                businessTicket.setFlightId(flight.getId());
                businessTicket.setCabinClass("BUSINESS");
                businessTicket.setPrice(basePrice.multiply(new BigDecimal("2.5")));
                businessTicket.setAvailableSeats(30);
                businessTicket.setDiscount(new BigDecimal("1.0"));
                businessTicket.setCreateTime(now);
                businessTicket.setUpdateTime(now);
                ticketMapper.insert(businessTicket);

                // 生成头等舱机票
                Ticket firstTicket = new Ticket();
                firstTicket.setFlightId(flight.getId());
                firstTicket.setCabinClass("FIRST");
                firstTicket.setPrice(basePrice.multiply(new BigDecimal("4.0")));
                firstTicket.setAvailableSeats(10);
                firstTicket.setDiscount(new BigDecimal("1.0"));
                firstTicket.setCreateTime(now);
                firstTicket.setUpdateTime(now);
                ticketMapper.insert(firstTicket);
                
                // 记录价格历史数据
                savePriceHistory(flight, economyTicket);
                savePriceHistory(flight, businessTicket);
                savePriceHistory(flight, firstTicket);
                
            } catch (Exception e) {
                // 如果插入失败，可能是并发问题，忽略异常
                log.warn("为航班生成机票时发生异常，可能是并发问题：{}", e.getMessage());

                // 再次检查是否已经存在机票
                existingTickets = ticketMapper.selectList(
                        new LambdaQueryWrapper<Ticket>()
                                .eq(Ticket::getFlightId, flight.getId())
                );

                // 如果已经存在机票，则不需要重新生成
                if (existingTickets.isEmpty()) {
                    log.error("为航班生成机票失败，且数据库中不存在机票：{}", flight.getId());
                }
            }
        }
    }

    /**
     * 获取航班的基础价格
     */
    private BigDecimal getBasePrice(Flight flight) {
        // 如果有航线信息，使用航线的基础价格
        if (flight.getRouteId() != null) {
            Route route = routeMapper.selectById(flight.getRouteId());
            if (route != null && route.getBasePrice() != null) {
                // 获取基础价格，根据航线距离进行调整
                BigDecimal basePrice = route.getBasePrice();
                
                // 根据航线距离计算价格调整因子
                Integer distance = route.getDistance();
                if (distance != null) {
                    // 距离越长，每公里价格越低（规模经济效应）
                    double distanceFactor = 1.0;
                    if (distance < 500) {
                        distanceFactor = 1.2; // 短途航线，价格较高
                    } else if (distance < 1000) {
                        distanceFactor = 1.0; // 中程航线，标准价格
                    } else if (distance < 2000) {
                        distanceFactor = 0.9; // 中长程航线
                    } else {
                        distanceFactor = 0.85; // 长程航线，单位距离价格较低
                    }
                    
                    basePrice = basePrice.multiply(new BigDecimal(distanceFactor));
                }
                
                return basePrice;
            }
        }

        // 如果没有航线信息，根据出发地和目的地生成价格
        try {
            // 查询出发机场和到达机场
            Airport departureAirport = airportMapper.selectById(flight.getDepartureAirportId());
            Airport arrivalAirport = airportMapper.selectById(flight.getArrivalAirportId());
            
            if (departureAirport != null && arrivalAirport != null) {
                // 获取城市
                City departureCity = cityMapper.selectById(departureAirport.getCityId());
                City arrivalCity = cityMapper.selectById(arrivalAirport.getCityId());
                
                if (departureCity != null && arrivalCity != null) {
                    // 根据城市等级生成价格基数 (一二线城市价格通常较高)
                    int baseAmount = 800; // 默认基础价格
                    
                    // 一线城市列表（示例）
                    List<String> tierOneCities = Arrays.asList("北京", "上海", "广州", "深圳");
                    // 二线城市列表（示例）
                    List<String> tierTwoCities = Arrays.asList("成都", "重庆", "杭州", "南京", "武汉", "西安", "天津");
                    
                    // 调整基础价格
                    if (tierOneCities.contains(departureCity.getName()) && tierOneCities.contains(arrivalCity.getName())) {
                        // 一线城市之间
                        baseAmount = 1200;
                    } else if (tierOneCities.contains(departureCity.getName()) || tierOneCities.contains(arrivalCity.getName())) {
                        // 一线城市和其他城市之间
                        baseAmount = 1000;
                    } else if (tierTwoCities.contains(departureCity.getName()) && tierTwoCities.contains(arrivalCity.getName())) {
                        // 二线城市之间
                        baseAmount = 900;
                    }
                    
                    return new BigDecimal(baseAmount);
                }
            }
        } catch (Exception e) {
            log.warn("根据城市信息计算价格失败: {}", e.getMessage());
        }

        // 兜底方案：生成一个默认价格
        return new BigDecimal("1000.00");
    }
    
    /**
     * 根据出发时间段调整价格
     */
    private BigDecimal adjustPriceByTimeSlot(BigDecimal basePrice, Date departureTime) {
        if (departureTime == null) {
            return basePrice;
        }
        
        Calendar cal = Calendar.getInstance();
        cal.setTime(departureTime);
        int hour = cal.get(Calendar.HOUR_OF_DAY);
        
        double timeFactor = 1.0;
        
        // 根据出发时间段调整价格
        if (hour >= 6 && hour < 9) {
            // 早高峰 6:00-9:00
            timeFactor = 1.15;
        } else if (hour >= 9 && hour < 12) {
            // 上午 9:00-12:00
            timeFactor = 1.0;
        } else if (hour >= 12 && hour < 14) {
            // 中午 12:00-14:00
            timeFactor = 0.95;
        } else if (hour >= 14 && hour < 17) {
            // 下午 14:00-17:00
            timeFactor = 1.0;
        } else if (hour >= 17 && hour < 20) {
            // 晚高峰 17:00-20:00
            timeFactor = 1.2;
        } else if (hour >= 20 && hour < 24) {
            // 夜间 20:00-24:00
            timeFactor = 0.9;
        } else {
            // 凌晨 0:00-6:00
            timeFactor = 0.85;
        }
        
        return basePrice.multiply(new BigDecimal(timeFactor)).setScale(2, RoundingMode.HALF_UP);
    }
    
    /**
     * 保存价格历史数据
     */
    private void savePriceHistory(Flight flight, Ticket ticket) {
        try {
            // 检查是否有FlightPriceHistoryMapper
            Object flightPriceHistoryMapper = getApplicationContext().getBean("flightPriceHistoryMapper");
            if (flightPriceHistoryMapper == null) {
                return;
            }
            
            // 这里假设有一个FlightPriceHistory实体类和对应的Mapper
            // 在实际项目中，需要根据具体的实现进行调整
            Class<?> entityClass = Class.forName("com.flightprice.prediction.entity.FlightPriceHistory");
            Object priceHistory = entityClass.newInstance();
            
            // 使用反射设置属性
            Method setFlightId = entityClass.getMethod("setFlightId", Long.class);
            Method setCabinClass = entityClass.getMethod("setCabinClass", String.class);
            Method setPrice = entityClass.getMethod("setPrice", BigDecimal.class);
            Method setCollectDate = entityClass.getMethod("setCollectDate", Date.class);
            Method setCreateTime = entityClass.getMethod("setCreateTime", Date.class);
            
            Date now = new Date();
            setFlightId.invoke(priceHistory, flight.getId());
            setCabinClass.invoke(priceHistory, ticket.getCabinClass());
            setPrice.invoke(priceHistory, ticket.getPrice());
            setCollectDate.invoke(priceHistory, now);
            setCreateTime.invoke(priceHistory, now);
            
            // 计算距离出发的天数
            int daysBeforeDeparture = (int) TimeUnit.MILLISECONDS.toDays(
                    flight.getDepartureTime().getTime() - now.getTime());
            Method setDaysBeforeDeparture = entityClass.getMethod("setDaysBeforeDeparture", Integer.class);
            setDaysBeforeDeparture.invoke(priceHistory, Math.max(0, daysBeforeDeparture));
            
            // 保存到数据库
            Method insert = flightPriceHistoryMapper.getClass().getMethod("insert", Object.class);
            insert.invoke(flightPriceHistoryMapper, priceHistory);
            
            log.info("已保存航班 {} 的 {} 舱位价格历史记录，价格：{}", 
                     flight.getFlightNumber(), ticket.getCabinClass(), ticket.getPrice());
        } catch (Exception e) {
            // 如果出现异常，记录日志但不影响主流程
            log.warn("保存价格历史记录失败: {}", e.getMessage());
        }
    }
    
    /**
     * 获取ApplicationContext
     */
    private ApplicationContext getApplicationContext() {
        try {
            // 通常ServiceImpl类都会实现ApplicationContextAware接口
            // 如果当前类没有实现该接口，可以通过其他方式获取ApplicationContext
            Field field = this.getClass().getSuperclass().getDeclaredField("applicationContext");
            field.setAccessible(true);
            return (ApplicationContext) field.get(this);
        } catch (Exception e) {
            log.warn("获取ApplicationContext失败: {}", e.getMessage());
            return null;
        }
    }
    
    /**
     * 根据日期调整价格
     */
    private void adjustPriceByDate(FlightVO flightVO, Date requestDate) {
        // 获取当前日期和请求日期的LocalDate
        LocalDate currentDate = LocalDate.now();
        LocalDate targetDate = requestDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();

        // 计算距离出发日期的天数
        long daysBeforeDeparture = ChronoUnit.DAYS.between(currentDate, targetDate);
        log.info("当前日期: {}, 目标日期: {}, 相隔天数: {}", currentDate, targetDate, daysBeforeDeparture);

        // 根据距离出发日期的天数计算系数 - 使用与价格预测相同的系数
        double daysFactor;
        if (daysBeforeDeparture <= 0) {
            daysFactor = 1.0; // 当天
        } else if (daysBeforeDeparture <= 3) {
            daysFactor = 1.0 + (0.2 * (3 - daysBeforeDeparture) / 3); // 临近出发日期，最高上浮20%
        } else if (daysBeforeDeparture <= 7) {
            daysFactor = 1.0 + (0.15 * (7 - daysBeforeDeparture) / 4); // 一周内，最高上浮15%
        } else if (daysBeforeDeparture <= 14) {
            daysFactor = 1.0 + (0.1 * (14 - daysBeforeDeparture) / 7); // 两周内，最高上浮10%
        } else if (daysBeforeDeparture <= 30) {
            daysFactor = 1.0 + (0.05 * (30 - daysBeforeDeparture) / 16); // 一个月内，最高上浮5%
        } else if (daysBeforeDeparture <= 60) {
            daysFactor = 0.98; // 提前2个月，价格略低
        } else {
            daysFactor = 0.95; // 提前太久，价格更低
        }

        // 星期几因子 - 使用与价格预测相同的系数
        int dayOfWeek = targetDate.getDayOfWeek().getValue(); // 1-7，周一到周日
        double weekdayFactor = 1.0;
        if (dayOfWeek == 5 || dayOfWeek == 6) {  // 周五和周六
            weekdayFactor = 1.08;  // 周末价格上浮8%
        } else if (dayOfWeek == 7 || dayOfWeek == 1) {  // 周日和周一
            weekdayFactor = 1.05;   // 周日和周一价格上浮5%
        }

        // 判断是否是节假日 - 使用与价格预测相同的系数
        boolean isHoliday = isHoliday(targetDate);
        double holidayFactor = isHoliday ? 1.15 : 1.0; // 节假日上浮15%

        // 季节性因素 - 使用与价格预测相同的系数
        int month = targetDate.getMonthValue();
        double seasonalFactor = getSeasonalFactor(month);
        // 使季节性因素更靠近1.0
        seasonalFactor = 1.0 + (seasonalFactor - 1.0) * 0.7;

        // 计算随机因子 - 保持小范围的随机浮动
        double randomFactor = 0.98 + new Random().nextDouble() * 0.04; // 0.98-1.02之间的随机数

        // 计算价格调整乘数
        double priceMultiplier = daysFactor * weekdayFactor * holidayFactor * seasonalFactor * randomFactor;
        
        log.info("价格调整因子: 天数={}, 星期={}, 节假日={}, 季节={}, 随机={}, 总乘数={}",
                daysFactor, weekdayFactor, holidayFactor, seasonalFactor, randomFactor, priceMultiplier);

        // 调整各舱位价格
        if (flightVO.getEconomyPrice() != null) {
            BigDecimal originalPrice = flightVO.getEconomyPrice();
            BigDecimal newPrice = originalPrice.multiply(new BigDecimal(priceMultiplier)).setScale(2, RoundingMode.HALF_UP);
            
            // 应用最大价格波动限制
            BigDecimal maxPrice = originalPrice.multiply(new BigDecimal("1.3")); // 最多涨30%
            BigDecimal minPrice = originalPrice.multiply(new BigDecimal("0.8")); // 最多降20%
            
            if (newPrice.compareTo(maxPrice) > 0) {
                newPrice = maxPrice;
            } else if (newPrice.compareTo(minPrice) < 0) {
                newPrice = minPrice;
            }
            
            flightVO.setEconomyPrice(newPrice);
        }

        if (flightVO.getBusinessPrice() != null) {
            BigDecimal originalPrice = flightVO.getBusinessPrice();
            BigDecimal newPrice = originalPrice.multiply(new BigDecimal(priceMultiplier)).setScale(2, RoundingMode.HALF_UP);
            
            // 应用最大价格波动限制
            BigDecimal maxPrice = originalPrice.multiply(new BigDecimal("1.3")); // 最多涨30%
            BigDecimal minPrice = originalPrice.multiply(new BigDecimal("0.8")); // 最多降20%
            
            if (newPrice.compareTo(maxPrice) > 0) {
                newPrice = maxPrice;
            } else if (newPrice.compareTo(minPrice) < 0) {
                newPrice = minPrice;
            }
            
            flightVO.setBusinessPrice(newPrice);
        }

        if (flightVO.getFirstPrice() != null) {
            BigDecimal originalPrice = flightVO.getFirstPrice();
            BigDecimal newPrice = originalPrice.multiply(new BigDecimal(priceMultiplier)).setScale(2, RoundingMode.HALF_UP);
            
            // 应用最大价格波动限制
            BigDecimal maxPrice = originalPrice.multiply(new BigDecimal("1.3")); // 最多涨30%
            BigDecimal minPrice = originalPrice.multiply(new BigDecimal("0.8")); // 最多降20%
            
            if (newPrice.compareTo(maxPrice) > 0) {
                newPrice = maxPrice;
            } else if (newPrice.compareTo(minPrice) < 0) {
                newPrice = minPrice;
            }
            
            flightVO.setFirstPrice(newPrice);
        }

        // 调整当前舱位价格
        if (flightVO.getPrice() != null) {
            BigDecimal originalPrice = flightVO.getPrice();
            BigDecimal newPrice = originalPrice.multiply(new BigDecimal(priceMultiplier)).setScale(2, RoundingMode.HALF_UP);
            
            // 应用最大价格波动限制
            BigDecimal maxPrice = originalPrice.multiply(new BigDecimal("1.3")); // 最多涨30%
            BigDecimal minPrice = originalPrice.multiply(new BigDecimal("0.8")); // 最多降20%
            
            if (newPrice.compareTo(maxPrice) > 0) {
                newPrice = maxPrice;
            } else if (newPrice.compareTo(minPrice) < 0) {
                newPrice = minPrice;
            }
            
            flightVO.setPrice(newPrice);
        }
        
        log.info("调整前价格: 经济舱={}, 商务舱={}, 头等舱={}",
                flightVO.getEconomyPrice(), flightVO.getBusinessPrice(), flightVO.getFirstPrice());
    }
    
    /**
     * 判断是否是假期（LocalDate版本）
     */
    private boolean isHoliday(LocalDate date) {
        int dayOfWeek = date.getDayOfWeek().getValue(); // 1-7，周一到周日
        // 简单判断周末（周六是6，周日是7）
        return dayOfWeek == 6 || dayOfWeek == 7;
    }
    
    /**
     * 根据月份获取季节因子
     */
    private double getSeasonalFactor(int month) {
        // 旺季
        if (month >= 6 && month <= 8) { // 暑假
            return 1.15;
        } else if (month == 1 || month == 2) { // 春节
            return 1.2;
        } else if (month == 10) { // 国庆
            return 1.18;
        } else if (month == 4 || month == 5) { // 五一、清明
            return 1.12;
        }
        // 淡季
        else if (month == 3 || month == 11) {
            return 0.95;
        }
        // 正常季节
        return 1.0;
    }
}
