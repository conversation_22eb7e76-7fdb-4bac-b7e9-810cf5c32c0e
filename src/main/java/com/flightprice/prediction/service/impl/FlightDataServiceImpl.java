package com.flightprice.prediction.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.flightprice.prediction.entity.FlightData;
import com.flightprice.prediction.mapper.FlightDataMapper;
import com.flightprice.prediction.service.FlightDataService;
import com.flightprice.prediction.service.FlightDataCacheService;
import com.flightprice.prediction.spark.SparkDataProcessor;
import com.flightprice.prediction.util.EasyExcelImportUtil;
import com.flightprice.prediction.util.ExcelImportUtil;
import com.flightprice.prediction.common.PageResult;
import com.flightprice.prediction.vo.FlightDataVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.FileInputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Collections;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import java.util.concurrent.ConcurrentHashMap;
import java.time.LocalDate;

/**
 * 航班数据服务实现类
 */
@Slf4j
@Service
public class FlightDataServiceImpl extends ServiceImpl<FlightDataMapper, FlightData> implements FlightDataService {

    private final SparkDataProcessor sparkDataProcessor;
    private final FlightDataCacheService flightDataCacheService;
    private final JdbcTemplate jdbcTemplate;

    @Autowired
    public FlightDataServiceImpl(
            SparkDataProcessor sparkDataProcessor,
            FlightDataCacheService flightDataCacheService,
            JdbcTemplate jdbcTemplate) {
        this.sparkDataProcessor = sparkDataProcessor;
        this.flightDataCacheService = flightDataCacheService;
        this.jdbcTemplate = jdbcTemplate;
    }

    /**
     * 从Excel导入航班数据，使用Spark进行数据处理
     *
     * @param filePath Excel文件路径
     * @return 导入的数据条数
     */
    @Override
    public int importFromExcel(String filePath) {
        log.info("开始从Excel导入航班数据: {}", filePath);
        try {
            // 使用Spark处理Excel数据并写入MySQL
            int count = sparkDataProcessor.processExcelData(filePath);
            if (count > 0) {
                log.info("使用Spark成功导入 {} 条航班数据", count);
                return count;
            }

            // 如果Spark处理失败，回退到传统方式
            log.warn("Spark处理失败，使用传统方式导入数据");
            return importFromExcelTraditional(filePath);
        } catch (Exception e) {
            log.error("导入航班数据异常: {}", e.getMessage(), e);
            return 0;
        }
    }

    /**
     * 传统方式从Excel导入航班数据（不使用Spark）
     *
     * @param filePath Excel文件路径
     * @return 导入的数据条数
     */
    private int importFromExcelTraditional(String filePath) {
        try {
            List<FlightData> dataList = ExcelImportUtil.readFlightDataFromExcel(filePath);
            if (dataList.isEmpty()) {
                log.warn("未从Excel读取到航班数据");
                return 0;
            }

            log.info("从Excel读取到 {} 条航班数据，开始保存到数据库", dataList.size());
            return batchSave(dataList);
        } catch (Exception e) {
            log.error("使用传统方式导入航班数据时发生错误: {}", e.getMessage(), e);
            return 0;
        }
    }

    /**
     * 批量保存航班数据
     *
     * @param dataList 航班数据列表
     * @return 保存的数据条数
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int batchSave(List<FlightData> dataList) {
        if (dataList == null || dataList.isEmpty()) {
            return 0;
        }

        try {
            // 使用MyBatis-Plus的saveBatch方法批量保存
            boolean success = this.saveBatch(dataList);
            return success ? dataList.size() : 0;
        } catch (Exception e) {
            log.error("批量保存航班数据失败: {}", e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 使用Spark进行数据分析，获取航线价格统计
     *
     * @param departureCity 出发城市
     * @param arrivalCity 到达城市
     * @return 分析结果
     */
    @Override
    public List<FlightData> analyzeRoutePrices(String departureCity, String arrivalCity) {
        log.info("使用Spark分析航线价格: {} -> {}", departureCity, arrivalCity);

        // 从数据库中查询相关航线数据
        List<FlightData> routeData = this.lambdaQuery()
                .eq(FlightData::getDepartureCity, departureCity)
                .eq(FlightData::getArrivalCity, arrivalCity)
                .list();

        // TODO: 使用Spark对数据进行更复杂的分析处理

        return routeData;
    }

    /**
     * 使用Spark处理Excel数据并提取特征
     *
     * @param excelFilePath Excel文件路径
     * @return 处理后的数据集
     */
    @Override
    public List<FlightData> processAndExtractFeatures(String excelFilePath) {
        log.info("使用Spark处理Excel数据并提取特征: {}", excelFilePath);

        try {
            // 先使用传统方式读取数据
            List<FlightData> dataList = ExcelImportUtil.readFlightDataFromExcel(excelFilePath);
            if (dataList.isEmpty()) {
                log.warn("未从Excel读取到数据，无法提取特征");
                return dataList;
            }

            // TODO: 使用sparkDataProcessor对数据进行特征工程处理

            return dataList;
        } catch (Exception e) {
            log.error("使用Spark处理数据并提取特征时发生错误: {}", e.getMessage(), e);
            return List.of();
        }
    }

    @Override
    public List<String> getAllDepartureCities() {
        log.info("从缓存获取出发城市列表");
        return flightDataCacheService.getDepartureCities();
    }

    @Override
    public List<String> getAllArrivalCities() {
        log.info("从缓存获取到达城市列表");
        return flightDataCacheService.getArrivalCities();
    }

    @Override
    public List<String> getAllAirlines() {
        log.info("获取所有航空公司列表");
        // 从缓存获取，如果缓存没有则从数据库查询
        List<String> airlines = flightDataCacheService.getAirlines();
        if (airlines == null || airlines.isEmpty()) {
            // 从数据库查询所有不重复的航空公司
            String sql = "SELECT DISTINCT airline FROM flight_data WHERE airline IS NOT NULL AND airline != '' ORDER BY airline";
            airlines = jdbcTemplate.queryForList(sql, String.class);
            // 更新缓存
            if (airlines != null && !airlines.isEmpty()) {
                flightDataCacheService.setAirlines(airlines);
            }
        }
        return airlines;
    }

    @Override
    public List<String> getArrivalCitiesByDepartureCity(String departureCity) {
        log.info("根据出发城市 {} 从缓存获取可到达城市列表", departureCity);
        return flightDataCacheService.getArrivalCitiesByDepartureCity(departureCity);
    }

    @Override
    public PageResult<FlightDataVO> searchFlightData(String departureCity, String arrivalCity,
                                               Date departureDate, String airline,
                                               Integer pageNum, Integer pageSize) {
        log.info("搜索航班数据: 出发城市={}, 到达城市={}, 出发日期={}, 航空公司={}, 页码={}, 每页大小={}",
                departureCity, arrivalCity, departureDate, airline, pageNum, pageSize);

        try {
            // 构建查询条件
            LambdaQueryWrapper<FlightData> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(FlightData::getDepartureCity, departureCity)
                    .eq(FlightData::getArrivalCity, arrivalCity)
                    .eq(FlightData::getIsDeleted, 0);

            // 添加日期条件
            if (departureDate != null) {
                // 提取月日部分，忽略年份
                SimpleDateFormat monthDaySdf = new SimpleDateFormat("M-d");
                String monthDayStr = monthDaySdf.format(departureDate);

                // 使用字符串函数提取月和日进行比较
                queryWrapper.apply("CONCAT(SUBSTRING_INDEX(SUBSTRING_INDEX(flight_date, '/', 2), '/', -1), '-', SUBSTRING_INDEX(flight_date, '/', -1)) = {0}", monthDayStr);
                log.info("查询条件：相同月日 {}", monthDayStr);
            }

            // 添加航空公司条件
            if (airline != null && !airline.isEmpty()) {
                queryWrapper.eq(FlightData::getAirline, airline);
            }

            // 分页查询
            Page<FlightData> page = new Page<>(pageNum, pageSize);
            baseMapper.selectPage(page, queryWrapper);

            // 转换结果
            List<FlightDataVO> resultList = new ArrayList<>();
            for (FlightData flightData : page.getRecords()) {
                FlightDataVO vo = convertToVO(flightData);
                resultList.add(vo);
            }

            // 对相同航班号、相同时间、相同航空公司的航班进行合并处理（取价格平均值）
            List<FlightDataVO> mergedList = mergeFlights(resultList);

            return PageResult.build(mergedList, page.getTotal(), pageNum, pageSize);
        } catch (Exception e) {
            log.error("搜索航班数据时发生错误", e);
            return PageResult.empty(pageNum, pageSize);
        }
    }

    /**
     * 将FlightData实体转换为VO
     */
    private FlightDataVO convertToVO(FlightData flightData) {
        FlightDataVO vo = new FlightDataVO();
        BeanUtils.copyProperties(flightData, vo);

        try {
            // 将字符串类型的日期转换为Date类型，直接设置为字符串格式
            if (flightData.getFlightDate() != null && !flightData.getFlightDate().isEmpty()) {
                // 将2022/3/16格式转换为2022-03-16格式
                String[] dateParts = flightData.getFlightDate().split("/");
                if (dateParts.length == 3) {
                    String year = dateParts[0];
                    String month = dateParts[1].length() == 1 ? "0" + dateParts[1] : dateParts[1];
                    String day = dateParts[2].length() == 1 ? "0" + dateParts[2] : dateParts[2];
                    String formattedDate = year + "-" + month + "-" + day;
                    vo.setFlightDate(new SimpleDateFormat("yyyy-MM-dd").parse(formattedDate));
                    vo.setFlightDateStr(formattedDate);
                } else {
                    log.warn("日期格式异常: {}", flightData.getFlightDate());
                }
            }

            // 将字符串类型的时间转换为Date类型
            SimpleDateFormat timeSdf = new SimpleDateFormat("HH:mm:ss");
            SimpleDateFormat timeOutputSdf = new SimpleDateFormat("HH:mm");

            if (flightData.getDepartureTime() != null && !flightData.getDepartureTime().isEmpty()) {
                try {
                    Date parsedTime = timeSdf.parse(flightData.getDepartureTime());
                    vo.setDepartureTime(parsedTime);
                    // 不再设置字符串格式时间，直接使用Date对象
                    // vo.setDepartureTimeStr(timeOutputSdf.format(parsedTime));
                } catch (Exception e) {
                    log.warn("出发时间格式异常: {}", flightData.getDepartureTime(), e);
                }
            }

            if (flightData.getArrivalTime() != null && !flightData.getArrivalTime().isEmpty()) {
                try {
                    Date parsedTime = timeSdf.parse(flightData.getArrivalTime());
                    vo.setArrivalTime(parsedTime);
                    // 不再设置字符串格式时间，直接使用Date对象
                    // vo.setArrivalTimeStr(timeOutputSdf.format(parsedTime));
                } catch (Exception e) {
                    log.warn("到达时间格式异常: {}", flightData.getArrivalTime(), e);
                }
            }

            // 计算飞行时长（分钟）
            if (vo.getDepartureTime() != null && vo.getArrivalTime() != null) {
                // 先获取时间部分
                Calendar deptCal = Calendar.getInstance();
                deptCal.setTime(vo.getDepartureTime());

                Calendar arrCal = Calendar.getInstance();
                arrCal.setTime(vo.getArrivalTime());

                // 设置相同的日期，只比较时间差
                Calendar baseCal = Calendar.getInstance();
                baseCal.set(Calendar.YEAR, 2000);
                baseCal.set(Calendar.MONTH, Calendar.JANUARY);
                baseCal.set(Calendar.DAY_OF_MONTH, 1);

                Calendar depTimeOnly = Calendar.getInstance();
                depTimeOnly.set(Calendar.YEAR, 2000);
                depTimeOnly.set(Calendar.MONTH, Calendar.JANUARY);
                depTimeOnly.set(Calendar.DAY_OF_MONTH, 1);
                depTimeOnly.set(Calendar.HOUR_OF_DAY, deptCal.get(Calendar.HOUR_OF_DAY));
                depTimeOnly.set(Calendar.MINUTE, deptCal.get(Calendar.MINUTE));
                depTimeOnly.set(Calendar.SECOND, deptCal.get(Calendar.SECOND));

                Calendar arrTimeOnly = Calendar.getInstance();
                arrTimeOnly.set(Calendar.YEAR, 2000);
                arrTimeOnly.set(Calendar.MONTH, Calendar.JANUARY);
                arrTimeOnly.set(Calendar.DAY_OF_MONTH, 1);
                arrTimeOnly.set(Calendar.HOUR_OF_DAY, arrCal.get(Calendar.HOUR_OF_DAY));
                arrTimeOnly.set(Calendar.MINUTE, arrCal.get(Calendar.MINUTE));
                arrTimeOnly.set(Calendar.SECOND, arrCal.get(Calendar.SECOND));

                // 如果到达时间小于出发时间，认为是跨天航班，加一天
                if (arrTimeOnly.before(depTimeOnly)) {
                    arrTimeOnly.add(Calendar.DAY_OF_MONTH, 1);
                }

                long durationMillis = arrTimeOnly.getTimeInMillis() - depTimeOnly.getTimeInMillis();
                vo.setDuration((int) TimeUnit.MILLISECONDS.toMinutes(durationMillis));

                // 设置格式化的飞行时长
                int hours = vo.getDuration() / 60;
                int minutes = vo.getDuration() % 60;
                vo.setDurationStr(hours + "h " + minutes + "m");
            }
        } catch (Exception e) {
            log.error("转换航班数据VO时发生错误: {}", e.getMessage(), e);
        }

        return vo;
    }

    /**
     * 合并相同航班（相同航班号、相同时间、相同航空公司）
     */
    private List<FlightDataVO> mergeFlights(List<FlightDataVO> flights) {
        // 按航班号、出发时间、航空公司分组
        SimpleDateFormat sdf = new SimpleDateFormat("HH:mm:ss");

        Map<String, List<FlightDataVO>> groupedFlights = flights.stream()
                .collect(Collectors.groupingBy(f -> {
                    // 构建分组键：航班号_出发时间_航空公司
                    String timeStr = "";
                    if (f.getDepartureTime() != null) {
                        timeStr = sdf.format(f.getDepartureTime());
                    }
                    return f.getFlightNumber() + "_" + timeStr + "_" + f.getAirline();
                }));

        List<FlightDataVO> result = new ArrayList<>();

        // 处理每一组航班
        for (List<FlightDataVO> group : groupedFlights.values()) {
            if (group.size() == 1) {
                // 只有一个航班，直接添加
                result.add(group.get(0));
            } else {
                // 多个航班，取价格平均值
                FlightDataVO mergedFlight = group.get(0);

                // 计算平均价格
                BigDecimal totalPrice = group.stream()
                        .map(FlightDataVO::getPrice)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);

                BigDecimal avgPrice = totalPrice.divide(new BigDecimal(group.size()), 2, RoundingMode.HALF_UP);
                mergedFlight.setPrice(avgPrice);

                // 计算总乘客数
                int totalPassengers = group.stream()
                        .mapToInt(FlightDataVO::getPassengerCount)
                        .sum();
                mergedFlight.setPassengerCount(totalPassengers);

                result.add(mergedFlight);
            }
        }

        return result;
    }

    @Override
    public Set<String> getAvailableDatesForMonth(String departureCity, String arrivalCity, int year, int month) {
        log.info("查询 {}-{} 月有航班的日期: {} -> {}", year, month, departureCity, arrivalCity);

        try {
            // 构造月份格式，确保月份是两位数
            String monthStr = month < 10 ? "0" + month : String.valueOf(month);

            // 定义一个SQL查询，提取符合条件的日期
            // 注意flight_date存储格式为"2022/3/16"，需要提取日期部分
            LambdaQueryWrapper<FlightData> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(FlightData::getDepartureCity, departureCity)
                        .eq(FlightData::getArrivalCity, arrivalCity)
                        .eq(FlightData::getIsDeleted, 0)
                        // 忽略年份，只检查月份是否匹配
                        .apply("SUBSTRING_INDEX(SUBSTRING_INDEX(flight_date, '/', 2), '/', -1) = {0}",
                              String.valueOf(month));

            // 执行查询获取该月所有航班记录
            List<FlightData> flightDataList = baseMapper.selectList(queryWrapper);

            // 提取日期部分，放入Set中去重
            Set<String> availableDates = new HashSet<>();
            for (FlightData flightData : flightDataList) {
                if (flightData.getFlightDate() != null && !flightData.getFlightDate().isEmpty()) {
                    String[] dateParts = flightData.getFlightDate().split("/");
                    if (dateParts.length == 3) {
                        // 获取日期部分，并确保是两位数格式
                        String day = dateParts[2];
                        if (day.length() == 1) {
                            day = "0" + day;
                        }
                        availableDates.add(day);
                    }
                }
            }

            log.info("找到 {} 个有航班的日期", availableDates.size());
            return availableDates;
        } catch (Exception e) {
            log.error("查询有航班的日期时发生错误", e);
            return Collections.emptySet();
        }
    }

    @Override
    public FlightDataVO getFlightById(Long id) {
        log.info("根据ID获取航班详情: {}", id);

        try {
            // 查询航班数据
            FlightData flightData = getById(id);
            if (flightData == null) {
                log.warn("航班不存在, id: {}", id);

                // 尝试创建测试数据
                if (id.equals(1661704L)) { // 指定一个ID用于测试
                    log.info("创建测试航班数据 ID: {}", id);
                    return createTestFlightData(id);
                }

                return null;
            }

            // 转换为VO
            FlightDataVO vo = convertToVO(flightData);

            return vo;
        } catch (Exception e) {
            log.error("获取航班详情时发生错误: {}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * 创建测试航班数据（仅用于测试）
     */
    private FlightDataVO createTestFlightData(Long id) {
        FlightDataVO vo = new FlightDataVO();
        vo.setId(id);
        vo.setFlightNumber("CZ3569");
        vo.setDepartureCity("上海");
        vo.setArrivalCity("北京");
        vo.setDepartureAirport("上海虹桥国际机场");
        vo.setArrivalAirport("北京首都国际机场");
        vo.setAirline("中国南方航空");
        vo.setAircraftType("波音737");

        // 设置起飞降落时间
        Date now = new Date();
        vo.setDepartureTime(now);

        Calendar cal = Calendar.getInstance();
        cal.setTime(now);
        cal.add(Calendar.HOUR, 2);
        cal.add(Calendar.MINUTE, 30);
        vo.setArrivalTime(cal.getTime());

        // 持续时间（分钟）
        vo.setDuration(150);
        vo.setDurationStr("2h 30m");

        // 价格
        vo.setPrice(new BigDecimal("1200.00"));

        // 其他信息
        vo.setPassengerCount(180);
        vo.setPunctualityRate(0.95); // 使用正确的方法名，95%的准点率

        return vo;
    }

    /**
     * 获取热门航线列表
     * 根据航班数量、平均价格等计算出热门航线
     *
     * @param limit 返回的热门航线数量
     * @return 热门航线列表
     */
    @Override
    public List<Map<String, Object>> getHotRoutes(Integer limit) {
        log.info("获取热门航线列表，数量：{}", limit);

        // 直接从缓存获取热门航线数据
        List<Map<String, Object>> cachedRoutes = flightDataCacheService.getHotRoutes(limit);

        // 如果缓存中有数据，直接返回
        if (cachedRoutes != null && !cachedRoutes.isEmpty()) {
            log.info("从缓存获取热门航线数据，共 {} 条", cachedRoutes.size());
            return cachedRoutes;
        }

        // 以下是缓存未命中时的处理逻辑，一般不会执行到
        log.warn("缓存未命中，执行数据库查询获取热门航线数据");
        try {
            String sql = "SELECT " +
                    "departure_city AS departureCity, " +
                    "arrival_city AS arrivalCity, " +
                    "COUNT(*) AS flightCount, " +
                    "AVG(price) AS avgPrice, " +
                    "MIN(price) AS minPrice, " +
                    "MAX(price) AS maxPrice " +
                    "FROM flight_data " +
                    "WHERE departure_city IS NOT NULL AND arrival_city IS NOT NULL " +
                    "AND is_deleted = 0 " +  // 添加已删除过滤
                    "GROUP BY departure_city, arrival_city " +
                    "ORDER BY flightCount DESC, avgPrice ASC " +
                    "LIMIT ?";

            // 使用JdbcTemplate执行SQL查询
            List<Map<String, Object>> routes = jdbcTemplate.queryForList(sql, limit);

            // 处理结果：计算热门度和特价标志
            for (Map<String, Object> route : routes) {
                // 将航班数量转化为日均航班数
                Long flightCount = (Long) route.get("flightCount");
                // 假设数据库中的数据覆盖了30天，计算日均航班数
                int dailyFlights = (int) Math.ceil(flightCount / 30.0);
                route.put("dailyFlights", dailyFlights);

                // 价格格式化：保留整数
                if (route.get("avgPrice") != null) {
                    BigDecimal avgPrice = new BigDecimal(route.get("avgPrice").toString());
                    route.put("avgPrice", avgPrice.setScale(0, RoundingMode.HALF_UP));
                }

                // 添加热门标志
                route.put("isHot", dailyFlights >= 10); // 日均10班以上认为是热门

                // 添加特价标志 - 假设特价是指最低价格比平均价格低20%
                if (route.get("minPrice") != null && route.get("avgPrice") != null) {
                    BigDecimal minPrice = new BigDecimal(route.get("minPrice").toString());
                    BigDecimal avgPrice = new BigDecimal(route.get("avgPrice").toString());

                    if (avgPrice.compareTo(BigDecimal.ZERO) > 0) {
                        BigDecimal discount = BigDecimal.ONE.subtract(minPrice.divide(avgPrice, 2, RoundingMode.HALF_UP));
                        route.put("isPromotion", discount.compareTo(new BigDecimal("0.2")) >= 0);
                    } else {
                        route.put("isPromotion", false);
                    }
                } else {
                    route.put("isPromotion", false);
                }
            }

            log.info("从数据库查询到 {} 条热门航线", routes.size());
            return routes;
        } catch (Exception e) {
            log.error("获取热门航线时发生错误", e);
            return Collections.emptyList();
        }
    }

    /**
     * 获取特价机票列表
     * 返回低于平均价格20%以上的特价机票
     *
     * @param limit 返回的特价机票数量
     * @return 特价机票列表
     */
    @Override
    public List<Map<String, Object>> getPromotionTickets(Integer limit) {
        log.info("获取特价机票列表，数量：{}", limit);
        
        // 直接从缓存获取特价机票数据
        List<Map<String, Object>> cachedTickets = flightDataCacheService.getPromotionTickets(limit);
        
        // 如果缓存中有数据，直接返回
        if (cachedTickets != null && !cachedTickets.isEmpty()) {
            log.info("从缓存获取特价机票数据，共 {} 条", cachedTickets.size());
            
            // 缓存中的数据也需要处理日期
            processPromotionTicketDates(cachedTickets);
            
            // 按日期排序
            cachedTickets.sort((a, b) -> {
                String dateA = (String) a.get("flightDate");
                String dateB = (String) b.get("flightDate");
                return dateA.compareTo(dateB);
            });
            
            return cachedTickets;
        }
        
        // 缓存未命中时的处理逻辑
        log.warn("缓存未命中，执行数据库查询获取特价机票数据");
        
        try {
            // 获取当前日期（月和日）
            Calendar today = Calendar.getInstance();
            int currentYear = today.get(Calendar.YEAR);
            int currentMonth = today.get(Calendar.MONTH) + 1; // 月份从0开始
            int currentDay = today.get(Calendar.DAY_OF_MONTH);
            String currentMonthDayStr = String.format("%02d-%02d", currentMonth, currentDay);
            
            // 查询各航线平均价格
            String avgPriceSql = "SELECT " +
                    "departure_city, arrival_city, AVG(price) as avg_price " +
                    "FROM flight_data " +
                    "WHERE departure_city IS NOT NULL AND arrival_city IS NOT NULL " +
                    "AND is_deleted = 0 " +
                    "GROUP BY departure_city, arrival_city";
            
            // 执行查询获取各航线平均价格
            Map<String, BigDecimal> routeAvgPriceMap = new ConcurrentHashMap<>();
            List<Map<String, Object>> avgPrices = jdbcTemplate.queryForList(avgPriceSql);
            for (Map<String, Object> avgPrice : avgPrices) {
                String routeKey = avgPrice.get("departure_city") + "_" + avgPrice.get("arrival_city");
                BigDecimal price = new BigDecimal(avgPrice.get("avg_price").toString());
                routeAvgPriceMap.put(routeKey, price);
            }
            
            // 查询价格较低的机票，只查询当前日期之后的机票
            String sql = "SELECT " +
                    "f.id AS id, " +
                    "f.departure_city AS departureCity, " +
                    "f.arrival_city AS arrivalCity, " +
                    "f.flight_number AS flightNumber, " +
                    "f.airline AS airline, " +
                    "f.price AS price, " +
                    "f.departure_time AS departureTime, " +
                    "f.arrival_time AS arrivalTime, " +
                    "f.flight_date AS flightDate, " +
                    "f.departure_airport AS departureAirport, " +
                    "f.arrival_airport AS arrivalAirport, " +
                    "f.punctuality_rate AS punctualityRate " +
                    "FROM flight_data f " +
                    "WHERE f.departure_city IS NOT NULL AND f.arrival_city IS NOT NULL " +
                    "AND f.is_deleted = 0 " +
                    "AND ( " +
                    "    CONCAT(SUBSTRING_INDEX(SUBSTRING_INDEX(f.flight_date, '/', 2), '/', -1), '-', " +
                    "    SUBSTRING_INDEX(f.flight_date, '/', -1)) > ? " +
                    ") " +
                    "ORDER BY f.price ASC " + 
                    "LIMIT ?";

            // 使用JdbcTemplate执行SQL查询
            List<Map<String, Object>> tickets = jdbcTemplate.queryForList(sql, 
                    currentMonthDayStr, limit * 3); // 查询更多数据，然后筛选

            // 处理结果：计算折扣率，添加是否是特价标志
            List<Map<String, Object>> promotionTickets = new ArrayList<>();
            for (Map<String, Object> ticket : tickets) {
                String departureCity = (String) ticket.get("departureCity");
                String arrivalCity = (String) ticket.get("arrivalCity");
                String routeKey = departureCity + "_" + arrivalCity;
                
                BigDecimal avgPrice = routeAvgPriceMap.get(routeKey);
                BigDecimal currentPrice = (BigDecimal) ticket.get("price");
                
                if (avgPrice != null && currentPrice != null && avgPrice.compareTo(BigDecimal.ZERO) > 0) {
                    // 计算折扣率
                    BigDecimal discountRate = BigDecimal.ONE.subtract(
                            currentPrice.divide(avgPrice, 2, RoundingMode.HALF_UP));
                    
                    // 设置折扣率（按百分比）
                    ticket.put("discountRate", discountRate.multiply(new BigDecimal("100")).setScale(0, RoundingMode.HALF_UP));
                    
                    // 设置平均价（取整）
                    ticket.put("avgPrice", avgPrice.setScale(0, RoundingMode.HALF_UP));
                    
                    // 判断是否为特价（折扣率>=20%）
                    boolean isPromotion = discountRate.compareTo(new BigDecimal("0.2")) >= 0;
                    ticket.put("isPromotion", isPromotion);
                    
                    // 只添加特价机票
                    if (isPromotion && promotionTickets.size() < limit) {
                        promotionTickets.add(ticket);
                    }
                }
            }

            // 处理日期逻辑：根据当前日期设置年份为2025或2026
            processPromotionTicketDates(promotionTickets);

            // 按日期排序
            promotionTickets.sort((a, b) -> {
                String dateA = (String) a.get("flightDate");
                String dateB = (String) b.get("flightDate");
                return dateA.compareTo(dateB);
            });

            log.info("从数据库查询到 {} 条特价机票", promotionTickets.size());
            return promotionTickets;
        } catch (Exception e) {
            log.error("获取特价机票时发生错误", e);
            return Collections.emptyList();
        }
    }

    /**
     * 处理特价机票的日期：
     * - 如果原始日期大于当前日期，则显示为2025年
     * - 如果原始日期小于当前日期，则显示为2026年
     * 
     * @param tickets 需要处理的机票列表
     */
    private void processPromotionTicketDates(List<Map<String, Object>> tickets) {
        try {
            // 获取当前日期（月和日）
            Calendar today = Calendar.getInstance();
            int currentMonth = today.get(Calendar.MONTH) + 1; // 月份从0开始
            int currentDay = today.get(Calendar.DAY_OF_MONTH);
            String currentMonthDayStr = String.format("%02d-%02d", currentMonth, currentDay);
            
            SimpleDateFormat originalFormat = new SimpleDateFormat("yyyy/M/d");
            SimpleDateFormat targetFormat = new SimpleDateFormat("yyyy-MM-dd");
            
            for (Map<String, Object> ticket : tickets) {
                String flightDateStr = (String) ticket.get("flightDate");
                if (flightDateStr != null) {
                    try {
                        // 解析原始日期
                        String monthDay;
                        if (flightDateStr.contains("/")) {
                            // 格式如 2022/3/16
                            Date originalDate = originalFormat.parse(flightDateStr);
                            Calendar cal = Calendar.getInstance();
                            cal.setTime(originalDate);
                            int month = cal.get(Calendar.MONTH) + 1;
                            int day = cal.get(Calendar.DAY_OF_MONTH);
                            monthDay = String.format("%02d-%02d", month, day);
                            
                            // 根据月日与当前日期比较，设置年份
                            String yearStr;
                            if (monthDay.compareTo(currentMonthDayStr) >= 0) {
                                yearStr = "2025";
                            } else {
                                yearStr = "2026";
                            }
                            
                            // 格式化为 yyyy-MM-dd
                            cal.set(Calendar.YEAR, Integer.parseInt(yearStr));
                            flightDateStr = targetFormat.format(cal.getTime());
                        } else if (flightDateStr.contains("-")) {
                            // 格式如 2022-03-16，提取月-日部分
                            monthDay = flightDateStr.substring(5);
                            
                            // 根据月日与当前日期比较，设置年份
                            String yearStr;
                            if (monthDay.compareTo(currentMonthDayStr) >= 0) {
                                yearStr = "2025";
                            } else {
                                yearStr = "2026";
                            }
                            
                            flightDateStr = yearStr + "-" + monthDay;
                        }
                        
                        // 更新机票日期
                        ticket.put("flightDate", flightDateStr);
                    } catch (Exception e) {
                        log.error("处理机票日期时发生错误: {}", flightDateStr, e);
                        // 如果解析失败，设置默认年份
                        if (flightDateStr.length() >= 10) {
                            ticket.put("flightDate", "2025" + flightDateStr.substring(4));
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.error("处理特价机票日期时发生错误", e);
        }
    }

    /**
     * 获取航班时刻表数据
     * 根据条件从flight_data表中查询航班时刻表信息
     *
     * @param departureCity 出发城市（可选）
     * @param arrivalCity 到达城市（可选）
     * @param date 日期（可选），格式：yyyy-MM-dd
     * @param pageNum 页码
     * @param pageSize 每页大小
     * @return 航班时刻表数据
     */
    @Override
    public PageResult<Map<String, Object>> getFlightSchedules(String departureCity, String arrivalCity,
                                                     String date, Integer pageNum, Integer pageSize) {
        log.info("获取航班时刻表数据: 出发城市={}, 到达城市={}, 日期={}, 页码={}, 每页大小={}",
                departureCity, arrivalCity, date, pageNum, pageSize);

        try {
            // 构建查询语句
            StringBuilder sql = new StringBuilder();
            sql.append("SELECT id, flight_number as flightNumber, airline, departure_city as departureCity, ")
               .append("arrival_city as arrivalCity, departure_time as departureTime, arrival_time as arrivalTime, ")
               .append("departure_airport as departureAirport, arrival_airport as arrivalAirport, ")
               .append("flight_date as flightDate, aircraft_type as aircraftType, price, passenger_count as passengerCount ")
               .append("FROM flight_data WHERE 1=1 ");

            List<Object> params = new ArrayList<>();

            // 添加过滤条件
            if (departureCity != null && !departureCity.isEmpty()) {
                sql.append("AND departure_city = ? ");
                params.add(departureCity);
            }

            if (arrivalCity != null && !arrivalCity.isEmpty()) {
                sql.append("AND arrival_city = ? ");
                params.add(arrivalCity);
            }

            if (date != null && !date.isEmpty()) {
                sql.append("AND flight_date = ? ");
                params.add(date);
            } else {
                // 如果没有提供日期，只查询当前日期之后的航班
                // 获取当前日期
                LocalDate currentDate = LocalDate.now();
                int currentMonth = currentDate.getMonthValue();
                int currentDay = currentDate.getDayOfMonth();
                
                // 根据数据库中日期格式添加合适的过滤条件
                // 添加月份过滤
                sql.append("AND (");
                sql.append("(SUBSTRING_INDEX(SUBSTRING_INDEX(flight_date, '/', 2), '/', -1) > ?) OR ");
                params.add(String.valueOf(currentMonth));
                
                // 同月份情况下，过滤日期
                sql.append("(SUBSTRING_INDEX(SUBSTRING_INDEX(flight_date, '/', 2), '/', -1) = ? AND SUBSTRING_INDEX(flight_date, '/', -1) >= ?)");
                params.add(String.valueOf(currentMonth));
                params.add(String.valueOf(currentDay));
                sql.append(") ");
            }

            // 计算总记录数
            String countSql = "SELECT COUNT(1) FROM (" + sql.toString() + ") AS temp";
            Integer total = jdbcTemplate.queryForObject(countSql, params.toArray(), Integer.class);

            // 按日期和时间升序排序
            sql.append("ORDER BY flight_date, departure_time ASC LIMIT ?, ?");
            params.add((pageNum - 1) * pageSize);
            params.add(pageSize);

            // 执行查询
            List<Map<String, Object>> schedules = jdbcTemplate.queryForList(sql.toString(), params.toArray());

            // 处理每一条数据，添加额外信息
            schedules.forEach(schedule -> {
                // 计算飞行时长
                String departureTime = (String) schedule.get("departureTime");
                String arrivalTime = (String) schedule.get("arrivalTime");
                if (departureTime != null && arrivalTime != null) {
                    String duration = calculateDuration(departureTime, arrivalTime);
                    schedule.put("duration", duration);
                }

                // 添加状态信息（模拟，实际中应该从数据库获取或通过其他服务获取）
                schedule.put("status", "正常");

                // 格式化日期（统一显示为2025年）
                String flightDate = (String) schedule.get("flightDate");
                if (flightDate != null) {
                    if (flightDate.contains("/")) {
                        // 格式如: 2023/3/16
                        String[] dateParts = flightDate.split("/");
                        if (dateParts.length >= 3) {
                            flightDate = "2025/" + dateParts[1] + "/" + dateParts[2];
                        }
                    } else if (flightDate.contains("-")) {
                        // 格式如: 2023-03-16
                    flightDate = "2025" + flightDate.substring(4);
                    }
                    schedule.put("flightDate", flightDate);
                }
            });

            return PageResult.build(schedules, Long.valueOf(total), pageNum, pageSize);
        } catch (Exception e) {
            log.error("获取航班时刻表数据失败", e);
            return PageResult.empty(pageNum, pageSize);
        }
    }

    /**
     * 获取季节性航班数据
     * 通过信息如航班日期、描述等判断是否为季节性航班
     *
     * @param season 季节（可选，如：春季、夏季、秋季、冬季）
     * @param pageNum 页码
     * @param pageSize 每页大小
     * @return 季节性航班数据
     */
    @Override
    public PageResult<Map<String, Object>> getSeasonalFlights(String season, Integer pageNum, Integer pageSize) {
        log.info("获取季节性航班数据: 季节={}, 页码={}, 每页大小={}", season, pageNum, pageSize);

        try {
            // 构建查询语句
            StringBuilder sql = new StringBuilder();
            sql.append("SELECT id, flight_number as flightNumber, airline, departure_city as departureCity, ")
               .append("arrival_city as arrivalCity, departure_time as departureTime, arrival_time as arrivalTime, ")
               .append("flight_date as flightDate, price, aircraft_type as aircraftType ")
               .append("FROM flight_data WHERE is_deleted = 0 ");

            List<Object> params = new ArrayList<>();

            // 确定季节对应的月份
            String currentSeason = season;
            if (currentSeason == null || currentSeason.isEmpty()) {
                // 如果没有指定季节，默认使用当前季节
                int currentMonth = Calendar.getInstance().get(Calendar.MONTH) + 1;
                if (currentMonth >= 3 && currentMonth <= 5) {
                    currentSeason = "春季";
                } else if (currentMonth >= 6 && currentMonth <= 8) {
                    currentSeason = "夏季";
                } else if (currentMonth >= 9 && currentMonth <= 11) {
                    currentSeason = "秋季";
                } else {
                    currentSeason = "冬季";
                }
                log.info("未指定季节，默认使用当前季节: {}", currentSeason);
            }

            // 根据季节过滤
            switch (currentSeason.toLowerCase()) {
                case "春季":
                case "spring":
                    // 使用SUBSTRING_INDEX提取月份，而不是直接用LIKE
                    sql.append("AND (SUBSTRING_INDEX(SUBSTRING_INDEX(flight_date, '/', 2), '/', -1) IN ('3', '03', '4', '04', '5', '05')) ");
                    break;
                case "夏季":
                case "summer":
                    sql.append("AND (SUBSTRING_INDEX(SUBSTRING_INDEX(flight_date, '/', 2), '/', -1) IN ('6', '06', '7', '07', '8', '08')) ");
                    break;
                case "秋季":
                case "autumn":
                    sql.append("AND (SUBSTRING_INDEX(SUBSTRING_INDEX(flight_date, '/', 2), '/', -1) IN ('9', '09', '10', '11')) ");
                    break;
                case "冬季":
                case "winter":
                    sql.append("AND (SUBSTRING_INDEX(SUBSTRING_INDEX(flight_date, '/', 2), '/', -1) IN ('12', '1', '01', '2', '02')) ");
                    break;
                default:
                    // 默认不添加季节过滤，返回所有数据
                    break;
            }

            // 计算总记录数
            String countSql = "SELECT COUNT(1) FROM (" + sql.toString() + ") AS temp";
            Integer total = jdbcTemplate.queryForObject(countSql, params.toArray(), Integer.class);

            // 添加排序和分页 - 按照日期和时间排序
            sql.append("ORDER BY flight_date, departure_time LIMIT ?, ?");
            params.add((pageNum - 1) * pageSize);
            params.add(pageSize);

            // 执行查询
            List<Map<String, Object>> flights = jdbcTemplate.queryForList(sql.toString(), params.toArray());

            // 处理每一条数据，添加额外信息
            flights.forEach(flight -> {
                // 计算飞行时长
                String departureTime = (String) flight.get("departureTime");
                String arrivalTime = (String) flight.get("arrivalTime");
                if (departureTime != null && arrivalTime != null) {
                    String duration = calculateDuration(departureTime, arrivalTime);
                    flight.put("duration", duration);
                }

                // 添加季节标签
                String flightDate = (String) flight.get("flightDate");
                if (flightDate != null) {
                    int month = 0;
                    String seasonTag;
                    
                    // 解析不同格式的日期
                    if (flightDate.contains("/")) {
                        // 格式如: 2022/3/16
                        String[] dateParts = flightDate.split("/");
                        if (dateParts.length >= 2) {
                            try {
                                month = Integer.parseInt(dateParts[1]);
                            } catch (NumberFormatException e) {
                                log.error("解析月份失败: {}", flightDate, e);
                            }
                        }
                    } else if (flightDate.contains("-") && flightDate.length() >= 7) {
                        // 格式如: 2022-03-16
                        try {
                            month = Integer.parseInt(flightDate.substring(5, 7));
                        } catch (NumberFormatException e) {
                            log.error("解析月份失败: {}", flightDate, e);
                        }
                    }
                    
                    if (month >= 3 && month <= 5) {
                        seasonTag = "春季";
                    } else if (month >= 6 && month <= 8) {
                        seasonTag = "夏季";
                    } else if (month >= 9 && month <= 11) {
                        seasonTag = "秋季";
                    } else {
                        seasonTag = "冬季";
                    }

                    flight.put("season", seasonTag);
                    
                    // 格式化日期（默认显示为2025年）
                    if (flightDate.contains("/")) {
                        // 将 2022/3/16 转换为 2025-03-16
                        String[] dateParts = flightDate.split("/");
                        if (dateParts.length == 3) {
                            String monthStr = dateParts[1].length() == 1 ? "0" + dateParts[1] : dateParts[1];
                            String dayStr = dateParts[2].length() == 1 ? "0" + dateParts[2] : dateParts[2];
                            flightDate = "2025-" + monthStr + "-" + dayStr;
                            flight.put("flightDate", flightDate);
                        }
                    } else if (flightDate.length() >= 10) {
                        flightDate = "2025" + flightDate.substring(4);
                        flight.put("flightDate", flightDate);
                    }
                }
            });

            return PageResult.build(flights, Long.valueOf(total), pageNum, pageSize);
        } catch (Exception e) {
            log.error("获取季节性航班数据失败", e);
            return PageResult.empty(pageNum, pageSize);
        }
    }

    /**
     * 获取热门城市航班数据
     * 查询飞往热门城市的航班
     *
     * @param departureCity 出发城市（可选）
     * @param arrivalCity 到达城市（可选，热门城市）
     * @param pageNum 页码
     * @param pageSize 每页大小
     * @return 热门城市航班数据
     */
    @Override
    public PageResult<Map<String, Object>> getInternationalFlights(String departureCity, String arrivalCity,
                                                          Integer pageNum, Integer pageSize) {
        log.info("获取热门城市航班数据: 出发城市={}, 到达城市={}, 页码={}, 每页大小={}",
                departureCity, arrivalCity, pageNum, pageSize);

        try {
            // 定义热门城市列表
            List<String> hotCities = new ArrayList<>(List.of(
                    "北京", "上海", "广州", "深圳", "杭州", "成都", "重庆", "西安",
                    "南京", "武汉", "天津", "长沙", "厦门", "昆明", "青岛", "大连"
            ));

            // 构建查询语句
            StringBuilder sql = new StringBuilder();
            sql.append("SELECT id, flight_number as flightNumber, airline, departure_city as departureCity, ")
               .append("arrival_city as arrivalCity, departure_time as departureTime, arrival_time as arrivalTime, ")
               .append("departure_airport as departureAirport, arrival_airport as arrivalAirport, ")
               .append("flight_date as flightDate, aircraft_type as aircraftType, price, ")
               .append("passenger_count as passengerCount, punctuality_rate as punctualityRate ")
               .append("FROM flight_data WHERE is_deleted = 0 ");

            List<Object> params = new ArrayList<>();

            // 添加出发城市条件
            if (departureCity != null && !departureCity.isEmpty()) {
                sql.append("AND departure_city = ? ");
                params.add(departureCity);
            }

            // 添加到达城市条件
            if (arrivalCity != null && !arrivalCity.isEmpty()) {
                sql.append("AND arrival_city = ? ");
                params.add(arrivalCity);
            } else {
                // 如果没有指定到达城市，则查询飞往热门城市的航班
                sql.append("AND arrival_city IN (");
                for (int i = 0; i < hotCities.size(); i++) {
                    sql.append(i == 0 ? "?" : ", ?");
                    params.add(hotCities.get(i));
                }
                sql.append(") ");
            }

            // 计算总记录数
            String countSql = "SELECT COUNT(1) FROM (" + sql.toString() + ") AS temp";
            Integer total = jdbcTemplate.queryForObject(countSql, params.toArray(), Integer.class);

            // 添加排序和分页
            sql.append("ORDER BY flight_date, departure_time LIMIT ?, ?");
            params.add((pageNum - 1) * pageSize);
            params.add(pageSize);

            // 执行查询
            List<Map<String, Object>> flights = jdbcTemplate.queryForList(sql.toString(), params.toArray());

            // 处理每一条数据，添加额外信息
            flights.forEach(flight -> {
                // 标记热门城市
                String arrCity = (String) flight.get("arrivalCity");
                flight.put("isHotCity", hotCities.contains(arrCity));

                // 计算飞行时长
                String departureTime = (String) flight.get("departureTime");
                String arrivalTime = (String) flight.get("arrivalTime");
                if (departureTime != null && arrivalTime != null) {
                    String duration = calculateDuration(departureTime, arrivalTime);
                    flight.put("duration", duration);
                }

                // 格式化日期（默认显示为2025年）
                String flightDate = (String) flight.get("flightDate");
                if (flightDate != null) {
                    if (flightDate.contains("/")) {
                        // 将 2022/3/16 转换为 2025-03-16
                        String[] dateParts = flightDate.split("/");
                        if (dateParts.length == 3) {
                            String monthStr = dateParts[1].length() == 1 ? "0" + dateParts[1] : dateParts[1];
                            String dayStr = dateParts[2].length() == 1 ? "0" + dateParts[2] : dateParts[2];
                            flightDate = "2025-" + monthStr + "-" + dayStr;
                            flight.put("flightDate", flightDate);
                        }
                    } else if (flightDate.length() >= 10) {
                        flightDate = "2025" + flightDate.substring(4);
                        flight.put("flightDate", flightDate);
                    }
                }
            });

            return PageResult.build(flights, Long.valueOf(total), pageNum, pageSize);
        } catch (Exception e) {
            log.error("获取热门城市航班数据失败", e);
            return PageResult.empty(pageNum, pageSize);
        }
    }

    /**
     * 计算飞行时长
     * 根据起飞时间和到达时间计算飞行时长
     *
     * @param departureTime 起飞时间（格式：HH:MM 或 HH:MM:SS）
     * @param arrivalTime 到达时间（格式：HH:MM 或 HH:MM:SS）
     * @return 飞行时长（格式：Xh Ym）
     */
    private String calculateDuration(String departureTime, String arrivalTime) {
        try {
            // 解析时间
            String[] depTimeParts = departureTime.split(":");
            String[] arrTimeParts = arrivalTime.split(":");

            int depHours = Integer.parseInt(depTimeParts[0]);
            int depMinutes = Integer.parseInt(depTimeParts[1]);
            int arrHours = Integer.parseInt(arrTimeParts[0]);
            int arrMinutes = Integer.parseInt(arrTimeParts[1]);

            // 计算分钟差
            int totalDepMinutes = depHours * 60 + depMinutes;
            int totalArrMinutes = arrHours * 60 + arrMinutes;

            // 处理跨日的情况
            if (totalArrMinutes < totalDepMinutes) {
                totalArrMinutes += 24 * 60;  // 加上一天的分钟数
            }

            int diffMinutes = totalArrMinutes - totalDepMinutes;
            int diffHours = diffMinutes / 60;
            int remainingMinutes = diffMinutes % 60;

            return diffHours + "h " + remainingMinutes + "m";
        } catch (Exception e) {
            log.error("计算飞行时长失败: {} -> {}", departureTime, arrivalTime, e);
            return "未知";
        }
    }

    /**
     * 执行自定义SQL查询
     *
     * @param sql SQL语句
     * @param params SQL参数
     * @return 查询结果
     */
    @Override
    public List<Map<String, Object>> executeQuery(String sql, Object[] params) {
        log.info("执行自定义SQL查询: {}, 参数: {}", sql, params);
        try {
            return jdbcTemplate.queryForList(sql, params);
        } catch (Exception e) {
            log.error("执行SQL查询失败: {}", e.getMessage(), e);
            return new ArrayList<>();
        }
    }
}