package com.flightprice.prediction.service.impl;

import com.flightprice.prediction.service.DataDashboardService;
import com.flightprice.prediction.vo.dashboard.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * 数据大屏服务实现类
 */
@Service
@Slf4j
public class DataDashboardServiceImpl implements DataDashboardService {

    private static final Random random = new Random();
    public static final String[] CITIES = {"北京", "上海", "广州", "深圳", "成都", "重庆", "杭州", "南京", "武汉", "西安", "长沙", "青岛", "厦门", "昆明", "大连"};
    public static final String[] AIRLINES = {"中国国航", "东方航空", "南方航空", "海南航空", "深圳航空", "四川航空", "厦门航空", "山东航空", "春秋航空", "吉祥航空"};

    @Override
    public DashboardOverviewVO getOverview() {
        log.info("获取顶部概览数据");
        DashboardOverviewVO vo = new DashboardOverviewVO();

        // 设置随机数据
        vo.setTotalFlights(10000 + random.nextInt(5000));
        vo.setTotalPassengers(vo.getTotalFlights() * (100 + random.nextInt(50)));

        // 设置总收入和增长率
        vo.setTotalRevenue(new BigDecimal(vo.getTotalPassengers() * (500 + random.nextInt(300))));
        vo.setFlightIncrease(random.nextInt(15) - 3);
        vo.setPassengerIncrease(random.nextInt(20) - 5);
        vo.setRevenueIncrease(random.nextInt(25) - 5);

        // 设置今日数据
        vo.setTodayFlights(100 + random.nextInt(200));
        vo.setTodayPassengers(vo.getTodayFlights() * (80 + random.nextInt(120)));

        // 设置航线和城市数据
        vo.setTotalRoutes(500 + random.nextInt(300));
        vo.setTotalCities(CITIES.length);

        // 设置航空公司数据
        vo.setActiveAirlines(AIRLINES.length);

        // 设置票价和客座率数据
        vo.setAvgTicketPrice(new BigDecimal(800 + random.nextInt(500)).setScale(2, RoundingMode.HALF_UP));
        vo.setPriceIncreaseRate(new BigDecimal(random.nextInt(20) - 5).setScale(2, RoundingMode.HALF_UP));
        vo.setAvgLoadFactor(new BigDecimal(65 + random.nextInt(25)).setScale(2, RoundingMode.HALF_UP));

        // 设置准点率和预测准确率
        vo.setPunctualityRate(new BigDecimal(70 + random.nextInt(25)).setScale(2, RoundingMode.HALF_UP));
        vo.setPredictionAccuracy(new BigDecimal(80 + random.nextInt(15)).setScale(2, RoundingMode.HALF_UP));

        return vo;
    }

    @Override
    public PriceTrendsVO getPriceTrends(String departureCity, String arrivalCity, String airline, String timeRange) {
        log.info("获取价格趋势数据 出发城市: {}, 到达城市: {}, 航空公司: {}, 时间范围: {}",
                departureCity, arrivalCity, airline, timeRange);

        PriceTrendsVO vo = new PriceTrendsVO();

        // 设置时间范围标签
        if ("week".equals(timeRange)) {
            vo.setTimeRangeLabel("最近7天");
        } else if ("month".equals(timeRange)) {
            vo.setTimeRangeLabel("最近30天");
        } else {
            vo.setTimeRangeLabel("最近90天");
        }

        // 生成趋势线数据
        List<PriceTrendsVO.TrendLine> trendLines = new ArrayList<>();

        // 创建实际价格趋势线
        PriceTrendsVO.TrendLine actualLine = new PriceTrendsVO.TrendLine();
        actualLine.setName("实际价格");

        // 创建预测价格趋势线
        PriceTrendsVO.TrendLine predictedLine = new PriceTrendsVO.TrendLine();
        predictedLine.setName("预测价格");

        // 生成数据点
        List<PriceTrendsVO.DataPoint> actualDataPoints = new ArrayList<>();
        List<PriceTrendsVO.DataPoint> predictedDataPoints = new ArrayList<>();

        // 生成日期和价格数据
        LocalDate now = LocalDate.now();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("MM-dd");
        int days = "week".equals(timeRange) ? 7 : "month".equals(timeRange) ? 30 : 90;

        BigDecimal highestPrice = BigDecimal.ZERO;
        BigDecimal lowestPrice = new BigDecimal("10000");
        BigDecimal totalPrice = BigDecimal.ZERO;

        for (int i = days - 1; i >= 0; i--) {
            LocalDate date = now.minusDays(i);
            String dateStr = date.format(formatter);

            // 生成价格数据
            double basePrice = 800 + random.nextInt(1000);
            double fluctuation = 0.8 + (random.nextDouble() * 0.4); // 0.8-1.2

            BigDecimal actualPrice = new BigDecimal(basePrice * fluctuation).setScale(2, RoundingMode.HALF_UP);

            // 更新统计信息
            if (actualPrice.compareTo(highestPrice) > 0) {
                highestPrice = actualPrice;
            }
            if (actualPrice.compareTo(lowestPrice) < 0) {
                lowestPrice = actualPrice;
            }
            totalPrice = totalPrice.add(actualPrice);

            // 创建实际价格数据点
            PriceTrendsVO.DataPoint actualPoint = new PriceTrendsVO.DataPoint();
            actualPoint.setDate(dateStr);
            actualPoint.setPrice(actualPrice);
            actualPoint.setPassengerCount(100 + random.nextInt(300));
            actualDataPoints.add(actualPoint);

            // 预测价格与实际价格稍有差异
            double predictError = 0.9 + (random.nextDouble() * 0.2); // 0.9-1.1
            BigDecimal predictedPrice = actualPrice.multiply(new BigDecimal(predictError)).setScale(2, RoundingMode.HALF_UP);

            // 创建预测价格数据点
            PriceTrendsVO.DataPoint predictedPoint = new PriceTrendsVO.DataPoint();
            predictedPoint.setDate(dateStr);
            predictedPoint.setPrice(predictedPrice);
            predictedPoint.setPassengerCount(actualPoint.getPassengerCount()); // 使用相同的乘客数
            predictedDataPoints.add(predictedPoint);
        }

        // 设置趋势线数据点
        actualLine.setDataPoints(actualDataPoints);
        predictedLine.setDataPoints(predictedDataPoints);

        // 添加趋势线
        trendLines.add(actualLine);
        trendLines.add(predictedLine);
        vo.setTrendLines(trendLines);

        // 设置统计数据
        vo.setHighestPrice(highestPrice);
        vo.setLowestPrice(lowestPrice);
        vo.setAveragePrice(totalPrice.divide(new BigDecimal(days), 2, RoundingMode.HALF_UP));

        // 计算价格波动率 (最高价-最低价)/平均价
        BigDecimal range = highestPrice.subtract(lowestPrice);
        vo.setVolatility(range.divide(vo.getAveragePrice(), 4, RoundingMode.HALF_UP)
                .multiply(new BigDecimal("100"))
                .setScale(2, RoundingMode.HALF_UP)); // 波动率以百分比表示

        return vo;
    }

    @Override
    public RouteHeatVO getRouteHeat() {
        log.info("获取航线热度数据");

        RouteHeatVO vo = new RouteHeatVO();
        List<RouteHeatVO.RouteHeat> routes = new ArrayList<>();

        // 生成热门航线数据
        for (int i = 0; i < 20; i++) {
            String dep = CITIES[random.nextInt(CITIES.length)];
            String arr = CITIES[random.nextInt(CITIES.length)];

            // 确保出发和到达城市不同
            while (dep.equals(arr)) {
                arr = CITIES[random.nextInt(CITIES.length)];
            }

            RouteHeatVO.RouteHeat routeHeat = new RouteHeatVO.RouteHeat();
            routeHeat.setRouteId((long)(i + 1));
            routeHeat.setDepartureCity(dep);
            routeHeat.setArrivalCity(arr);
            routeHeat.setRouteName(dep + "-" + arr);

            // 设置航班频次和客流量
            int flightFrequency = 100 + random.nextInt(900);
            routeHeat.setFlightFrequency(flightFrequency);
            routeHeat.setPassengerVolume(flightFrequency * (80 + random.nextInt(120)));

            // 设置票价相关数据
            routeHeat.setAvgPrice(new BigDecimal(500 + random.nextInt(1500)).setScale(2, RoundingMode.HALF_UP));
            routeHeat.setPriceGrowthRate(new BigDecimal(random.nextInt(30) - 10).setScale(2, RoundingMode.HALF_UP));

            // 设置客座率和热度得分
            routeHeat.setAvgLoadFactor(new BigDecimal(65 + random.nextInt(30)).setScale(2, RoundingMode.HALF_UP));
            routeHeat.setHeatScore(new BigDecimal(1 + random.nextInt(100)).setScale(2, RoundingMode.HALF_UP));

            routes.add(routeHeat);
        }

        // 按航班频次排序
        routes.sort((a, b) -> b.getFlightFrequency() - a.getFlightFrequency());
        vo.setHotRoutes(routes);

        // 设置航线总数
        vo.setTotalRoutes(routes.size() + random.nextInt(50));

        // 设置最高客流量航线
        RouteHeatVO.RouteHeat maxVolumeRoute = routes.get(0); // 已经排序，第一个就是最高
        vo.setHighestVolumeRoute(maxVolumeRoute.getRouteName());
        vo.setHighestVolume(maxVolumeRoute.getPassengerVolume());

        // 设置最高价格航线
        routes.sort((a, b) -> b.getAvgPrice().compareTo(a.getAvgPrice()));
        RouteHeatVO.RouteHeat maxPriceRoute = routes.get(0);
        vo.setHighestPriceRoute(maxPriceRoute.getRouteName());
        vo.setHighestPrice(maxPriceRoute.getAvgPrice());

        return vo;
    }

    @Override
    public AirlineMarketShareVO getAirlineMarketShare() {
        log.info("获取航空公司市场份额");

        AirlineMarketShareVO vo = new AirlineMarketShareVO();
        List<AirlineMarketShareVO.AirlineShare> airlines = new ArrayList<>();

        // 确保总百分比为100%
        int remaining = 100;
        for (int i = 0; i < AIRLINES.length; i++) {
            AirlineMarketShareVO.AirlineShare airlineData = new AirlineMarketShareVO.AirlineShare();
            airlineData.setAirlineName(AIRLINES[i]);

            // 最后一个航空公司占剩余份额
            if (i == AIRLINES.length - 1) {
                airlineData.setMarketShare(new BigDecimal(remaining));
            } else {
                // 生成一个合理的随机份额
                int share = i == 0 ? 15 + random.nextInt(10) : 5 + random.nextInt(10);
                share = Math.min(share, remaining - (AIRLINES.length - i - 1));
                airlineData.setMarketShare(new BigDecimal(share));
                remaining -= share;
            }

            airlineData.setFlightCount(1000 + random.nextInt(3000));
            airlineData.setYearOverYearGrowth(new BigDecimal(random.nextInt(30) - 10).setScale(2, RoundingMode.HALF_UP));

            airlines.add(airlineData);
        }

        // 按市场份额排序
        airlines.sort((a, b) -> b.getMarketShare().compareTo(a.getMarketShare()));
        vo.setAirlineShares(airlines);

        return vo;
    }

    @Override
    public HotCitiesVO getHotCities(Integer limit) {
        log.info("获取热门城市排行，数量限制: {}", limit);

        HotCitiesVO vo = new HotCitiesVO();
        List<HotCitiesVO.HotCity> departureCities = new ArrayList<>();
        List<HotCitiesVO.HotCity> arrivalCities = new ArrayList<>();

        // 生成出发城市数据
        for (String city : CITIES) {
            HotCitiesVO.HotCity cityData = new HotCitiesVO.HotCity();
            cityData.setCityName(city);
            cityData.setFlightCount(100 + random.nextInt(900));
            cityData.setPassengerCount(cityData.getFlightCount() * (80 + random.nextInt(120)));

            departureCities.add(cityData);
        }

        // 生成到达城市数据
        for (String city : CITIES) {
            HotCitiesVO.HotCity cityData = new HotCitiesVO.HotCity();
            cityData.setCityName(city);
            cityData.setFlightCount(100 + random.nextInt(900));
            cityData.setPassengerCount(cityData.getFlightCount() * (80 + random.nextInt(120)));

            arrivalCities.add(cityData);
        }

        // 按航班数量排序并限制数量
        departureCities.sort((a, b) -> b.getFlightCount() - a.getFlightCount());
        arrivalCities.sort((a, b) -> b.getFlightCount() - a.getFlightCount());

        if (departureCities.size() > limit) {
            departureCities = departureCities.subList(0, limit);
        }

        if (arrivalCities.size() > limit) {
            arrivalCities = arrivalCities.subList(0, limit);
        }

        vo.setDepartureCities(departureCities);
        vo.setArrivalCities(arrivalCities);

        return vo;
    }

    @Override
    public PunctualityVO getPunctuality(String airline) {
        log.info("获取航班准点率统计，航空公司: {}", airline);

        PunctualityVO vo = new PunctualityVO();
        List<PunctualityVO.AirlinePunctuality> airlinePunctualities = new ArrayList<>();

        String[] targetAirlines = airline != null ? new String[]{airline} : AIRLINES;

        for (String name : targetAirlines) {
            PunctualityVO.AirlinePunctuality punctuality = new PunctualityVO.AirlinePunctuality();
            punctuality.setAirlineName(name);
            int onTimeRate = 60 + random.nextInt(35);
            punctuality.setPunctualityRate(new BigDecimal(onTimeRate));
            punctuality.setAvgDelayTime(10 + random.nextInt(50));
            punctuality.setTotalFlights(1000 + random.nextInt(2000));
            punctuality.setPunctualFlights((int)(punctuality.getTotalFlights() * (onTimeRate / 100.0)));

            airlinePunctualities.add(punctuality);
        }

        // 按准点率排序
        airlinePunctualities.sort((a, b) -> b.getPunctualityRate().compareTo(a.getPunctualityRate()));
        vo.setAirlinePunctualities(airlinePunctualities);

        // 设置整体准点率
        vo.setOverallPunctualityRate(new BigDecimal(75 + random.nextInt(15)));

        // 设置最高和最低准点率
        if (!airlinePunctualities.isEmpty()) {
            vo.setHighestPunctualityRate(airlinePunctualities.get(0).getPunctualityRate());
            vo.setHighestPunctualityAirline(airlinePunctualities.get(0).getAirlineName());

            vo.setLowestPunctualityRate(airlinePunctualities.get(airlinePunctualities.size() - 1).getPunctualityRate());
            vo.setLowestPunctualityAirline(airlinePunctualities.get(airlinePunctualities.size() - 1).getAirlineName());
        }

        return vo;
    }

    @Override
    public PassengerFlowVO getPassengerFlow(String timeRange) {
        log.info("获取客流量统计，时间范围: {}", timeRange);

        PassengerFlowVO vo = new PassengerFlowVO();
        List<PassengerFlowVO.FlowData> flowDataList = new ArrayList<>();
        List<Integer> passengerCounts = new ArrayList<>();
        List<Integer> flightCounts = new ArrayList<>();

        // 设置时间维度
        vo.setTimeDimension("week".equals(timeRange) ? "日" : "month".equals(timeRange) ? "周" : "月");

        // 生成日期数据
        LocalDate now = LocalDate.now();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("MM-dd");

        int days = "week".equals(timeRange) ? 7 : "month".equals(timeRange) ? 30 : 90;

        for (int i = days - 1; i >= 0; i--) {
            LocalDate date = now.minusDays(i);
            String dateLabel = date.format(formatter);

            // 生成客流量数据
            int flights = 50 + random.nextInt(150);
            int passengers = flights * (80 + random.nextInt(120));

            // 创建流量数据项
            PassengerFlowVO.FlowData flowData = new PassengerFlowVO.FlowData();
            flowData.setDateLabel(dateLabel);
            flowData.setFlightCount(flights);
            flowData.setPassengerCount(passengers);
            flowDataList.add(flowData);

            // 保存到列表中用于计算统计数据
            passengerCounts.add(passengers);
            flightCounts.add(flights);
        }

        vo.setFlowData(flowDataList);

        // 添加峰值和均值统计
        int maxPassengerIndex = 0;
        int maxPassengerCount = 0;
        int minPassengerIndex = 0;
        int minPassengerCount = Integer.MAX_VALUE;

        for (int i = 0; i < passengerCounts.size(); i++) {
            int count = passengerCounts.get(i);
            if (count > maxPassengerCount) {
                maxPassengerCount = count;
                maxPassengerIndex = i;
            }
            if (count < minPassengerCount) {
                minPassengerCount = count;
                minPassengerIndex = i;
            }
        }

        // 设置峰值和谷值
        if (!flowDataList.isEmpty()) {
            vo.setPeakPeriod(flowDataList.get(maxPassengerIndex).getDateLabel());
            vo.setPeakVolume(maxPassengerCount);
            vo.setValleyPeriod(flowDataList.get(minPassengerIndex).getDateLabel());
            vo.setValleyVolume(minPassengerCount);
        }

        // 设置总量和平均值
        vo.setTotalVolume(passengerCounts.stream().mapToInt(Integer::intValue).sum());
        vo.setAverageVolume(vo.getTotalVolume() / passengerCounts.size());

        // 设置增长率（模拟数据）
        vo.setYearOverYearGrowth(5.0 + random.nextDouble() * 10.0);
        vo.setMonthOverMonthGrowth(2.0 + random.nextDouble() * 5.0);

        return vo;
    }

    @Override
    public MapDataVO getMapData() {
        log.info("获取地图数据");

        MapDataVO vo = new MapDataVO();
        List<MapDataVO.RouteConnection> routes = new ArrayList<>();

        // 生成主要城市之间的航线连接
        for (int i = 0; i < 30; i++) {
            String dep = CITIES[random.nextInt(CITIES.length)];
            String arr = CITIES[random.nextInt(CITIES.length)];

            // 确保出发和到达城市不同
            while (dep.equals(arr)) {
                arr = CITIES[random.nextInt(CITIES.length)];
            }

            MapDataVO.RouteConnection connection = new MapDataVO.RouteConnection();
            connection.setSourceCity(dep);
            connection.setTargetCity(arr);
            connection.setFlightCount(50 + random.nextInt(200));
            connection.setPassengerCount(connection.getFlightCount() * (80 + random.nextInt(120)));

            routes.add(connection);
        }

        vo.setRoutes(routes);

        // 生成城市节点数据
        List<MapDataVO.CityNode> cities = new ArrayList<>();
        for (String city : CITIES) {
            MapDataVO.CityNode node = new MapDataVO.CityNode();
            node.setCityName(city);

            // 统计该城市的航班总数
            int flightCount = routes.stream()
                    .filter(c -> c.getSourceCity().equals(city) || c.getTargetCity().equals(city))
                    .mapToInt(MapDataVO.RouteConnection::getFlightCount)
                    .sum();

            node.setFlightCount(flightCount);
            node.setSize(1 + random.nextInt(10)); // 节点大小1-10

            cities.add(node);
        }

        vo.setCities(cities);

        return vo;
    }

    @Override
    public PriceDistributionVO getPriceDistribution() {
        log.info("获取价格区间分布");

        PriceDistributionVO vo = new PriceDistributionVO();

        // 定义价格区间
        List<String> priceRanges = Arrays.asList(
                "0-500", "500-1000", "1000-1500", "1500-2000",
                "2000-3000", "3000-5000", "5000以上");

        List<Integer> economyCounts = new ArrayList<>();
        List<Integer> businessCounts = new ArrayList<>();
        List<Integer> firstCounts = new ArrayList<>();

        // 为经济舱生成数据，主要集中在低价区间
        int totalEconomy = 10000;
        economyCounts.add(2000 + random.nextInt(1000)); // 0-500
        economyCounts.add(3000 + random.nextInt(1000)); // 500-1000
        economyCounts.add(2000 + random.nextInt(1000)); // 1000-1500
        economyCounts.add(1000 + random.nextInt(500));  // 1500-2000
        economyCounts.add(500 + random.nextInt(300));   // 2000-3000
        economyCounts.add(200 + random.nextInt(200));   // 3000-5000
        economyCounts.add(100 + random.nextInt(100));   // 5000以上

        // 为商务舱生成数据，主要集中在中价区间
        int totalBusiness = 5000;
        businessCounts.add(100 + random.nextInt(100));  // 0-500
        businessCounts.add(500 + random.nextInt(300));  // 500-1000
        businessCounts.add(1000 + random.nextInt(500)); // 1000-1500
        businessCounts.add(1500 + random.nextInt(500)); // 1500-2000
        businessCounts.add(1000 + random.nextInt(500)); // 2000-3000
        businessCounts.add(500 + random.nextInt(300));  // 3000-5000
        businessCounts.add(200 + random.nextInt(200));  // 5000以上

        // 为头等舱生成数据，主要集中在高价区间
        int totalFirst = 2000;
        firstCounts.add(50 + random.nextInt(50));       // 0-500
        firstCounts.add(100 + random.nextInt(100));     // 500-1000
        firstCounts.add(200 + random.nextInt(100));     // 1000-1500
        firstCounts.add(300 + random.nextInt(200));     // 1500-2000
        firstCounts.add(500 + random.nextInt(300));     // 2000-3000
        firstCounts.add(500 + random.nextInt(300));     // 3000-5000
        firstCounts.add(350 + random.nextInt(250));     // 5000以上

        vo.setPriceRanges(priceRanges);
        vo.setEconomyCounts(economyCounts);
        vo.setBusinessCounts(businessCounts);
        vo.setFirstCounts(firstCounts);

        return vo;
    }

    @Override
    public CityAvgPricesVO getCityAvgPrices(String type) {
        log.info("获取各城市平均价格，类型: {}", type);

        CityAvgPricesVO vo = new CityAvgPricesVO();
        List<CityAvgPricesVO.CityPrice> cityPrices = new ArrayList<>();

        // 根据类型决定使用哪些城市
        String[] targetCities = "departure".equals(type) ? CITIES : CITIES;

        for (String city : targetCities) {
            CityAvgPricesVO.CityPrice cityPrice = new CityAvgPricesVO.CityPrice();
            cityPrice.setCityName(city);

            // 生成各舱位平均价格
            cityPrice.setMinPrice(new BigDecimal(500 + random.nextInt(1000)).setScale(2, RoundingMode.HALF_UP));
            cityPrice.setMaxPrice(new BigDecimal(1500 + random.nextInt(2000)).setScale(2, RoundingMode.HALF_UP));
            cityPrice.setFlightCount(100 + random.nextInt(500));

            // 计算总平均价格
            BigDecimal total = cityPrice.getMinPrice()
                    .add(cityPrice.getMaxPrice())
                    .divide(new BigDecimal(2), 2, RoundingMode.HALF_UP);
            cityPrice.setAvgPrice(total);

            cityPrices.add(cityPrice);
        }

        // 按平均价格排序
        cityPrices.sort((a, b) -> b.getAvgPrice().compareTo(a.getAvgPrice()));

        vo.setCityPrices(cityPrices);
        vo.setType(type);

        return vo;
    }

    @Override
    public PredictionAccuracyVO getPredictionAccuracy() {
        log.info("获取预测准确率统计");

        PredictionAccuracyVO vo = new PredictionAccuracyVO();
        List<String> timeRanges = Arrays.asList("1天", "3天", "7天", "15天", "30天", "60天", "90天");
        List<BigDecimal> accuracyRates = new ArrayList<>();
        List<BigDecimal> errorRates = new ArrayList<>();

        // 随机生成准确率数据，时间越短准确率越高
        accuracyRates.add(new BigDecimal(90 + random.nextInt(10)).setScale(2, RoundingMode.HALF_UP));  // 1天
        accuracyRates.add(new BigDecimal(85 + random.nextInt(10)).setScale(2, RoundingMode.HALF_UP));  // 3天
        accuracyRates.add(new BigDecimal(80 + random.nextInt(10)).setScale(2, RoundingMode.HALF_UP));  // 7天
        accuracyRates.add(new BigDecimal(75 + random.nextInt(10)).setScale(2, RoundingMode.HALF_UP));  // 15天
        accuracyRates.add(new BigDecimal(70 + random.nextInt(10)).setScale(2, RoundingMode.HALF_UP));  // 30天
        accuracyRates.add(new BigDecimal(65 + random.nextInt(10)).setScale(2, RoundingMode.HALF_UP));  // 60天
        accuracyRates.add(new BigDecimal(60 + random.nextInt(10)).setScale(2, RoundingMode.HALF_UP));  // 90天

        // 计算误差率
        for (BigDecimal accuracy : accuracyRates) {
            errorRates.add(new BigDecimal(100).subtract(accuracy).setScale(2, RoundingMode.HALF_UP));
        }

        vo.setTimeRanges(timeRanges);
        vo.setAccuracyRates(accuracyRates);
        vo.setErrorRates(errorRates);

        // 设置平均准确率
        BigDecimal sum = accuracyRates.stream().reduce(BigDecimal.ZERO, BigDecimal::add);
        vo.setAverageAccuracy(sum.divide(new BigDecimal(accuracyRates.size()), 2, RoundingMode.HALF_UP));

        // 设置各算法准确率比较
        List<PredictionAccuracyVO.AlgorithmAccuracy> algorithmAccuracyList = new ArrayList<>();

        PredictionAccuracyVO.AlgorithmAccuracy cnn = new PredictionAccuracyVO.AlgorithmAccuracy();
        cnn.setAlgorithmName("CNN");
        cnn.setAccuracy(new BigDecimal(85 + random.nextInt(10)).setScale(2, RoundingMode.HALF_UP));
        algorithmAccuracyList.add(cnn);

        PredictionAccuracyVO.AlgorithmAccuracy rf = new PredictionAccuracyVO.AlgorithmAccuracy();
        rf.setAlgorithmName("随机森林");
        rf.setAccuracy(new BigDecimal(80 + random.nextInt(10)).setScale(2, RoundingMode.HALF_UP));
        algorithmAccuracyList.add(rf);

        PredictionAccuracyVO.AlgorithmAccuracy lr = new PredictionAccuracyVO.AlgorithmAccuracy();
        lr.setAlgorithmName("线性回归");
        lr.setAccuracy(new BigDecimal(75 + random.nextInt(10)).setScale(2, RoundingMode.HALF_UP));
        algorithmAccuracyList.add(lr);

        PredictionAccuracyVO.AlgorithmAccuracy dt = new PredictionAccuracyVO.AlgorithmAccuracy();
        dt.setAlgorithmName("决策树");
        dt.setAccuracy(new BigDecimal(70 + random.nextInt(10)).setScale(2, RoundingMode.HALF_UP));
        algorithmAccuracyList.add(dt);

        PredictionAccuracyVO.AlgorithmAccuracy svm = new PredictionAccuracyVO.AlgorithmAccuracy();
        svm.setAlgorithmName("SVM");
        svm.setAccuracy(new BigDecimal(65 + random.nextInt(10)).setScale(2, RoundingMode.HALF_UP));
        algorithmAccuracyList.add(svm);

        vo.setAlgorithmAccuracy(algorithmAccuracyList);

        return vo;
    }

    @Override
    public RealtimeMonitorVO getRealtimeMonitor() {
        log.info("获取实时数据监控");

        RealtimeMonitorVO vo = new RealtimeMonitorVO();

        // 设置当前数据
        vo.setOnlineUsers(1000 + random.nextInt(5000));
        vo.setTodayVisits(5000 + random.nextInt(10000));
        vo.setTodayPredictions(500 + random.nextInt(1000));
        vo.setTodayOrders(100 + random.nextInt(200));

        // 生成最近API调用数据
        List<RealtimeMonitorVO.ApiCall> recentApiCalls = new ArrayList<>();
        String[] apiPaths = {"/api/flights", "/api/predictions", "/api/users", "/api/orders", "/api/statistics"};

        for (int i = 0; i < 10; i++) {
            RealtimeMonitorVO.ApiCall apiCall = new RealtimeMonitorVO.ApiCall();
            apiCall.setPath(apiPaths[random.nextInt(apiPaths.length)]);
            apiCall.setTime(String.format("%02d:%02d:%02d", random.nextInt(24), random.nextInt(60), random.nextInt(60)));
            apiCall.setResponseTime(10 + random.nextInt(100));
            apiCall.setStatusCode(200);
            apiCall.setClientIp("192.168.1." + (random.nextInt(254) + 1));
            recentApiCalls.add(apiCall);
        }

        vo.setRecentApiCalls(recentApiCalls);

        // 生成活跃城市数据
        List<RealtimeMonitorVO.ActiveCity> activeCities = new ArrayList<>();
        for (int i = 0; i < 5; i++) {
            RealtimeMonitorVO.ActiveCity activeCity = new RealtimeMonitorVO.ActiveCity();
            activeCity.setCityName(CITIES[random.nextInt(CITIES.length)]);
            activeCity.setSearchCount(100 + random.nextInt(500));
            activeCity.setActiveUsers(50 + random.nextInt(200));
            activeCities.add(activeCity);
        }

        vo.setActiveCities(activeCities);

        // 设置系统状态
        vo.setCpuUsage(new BigDecimal(20 + random.nextInt(60)).setScale(2, RoundingMode.HALF_UP));
        vo.setMemoryUsage(new BigDecimal(30 + random.nextInt(50)).setScale(2, RoundingMode.HALF_UP));
        vo.setDiskUsage(new BigDecimal(40 + random.nextInt(40)).setScale(2, RoundingMode.HALF_UP));
        vo.setResponseTime(10 + random.nextInt(90));

        // 生成热搜航线数据
        List<RealtimeMonitorVO.HotSearch> hotSearches = new ArrayList<>();
        for (int i = 0; i < 5; i++) {
            RealtimeMonitorVO.HotSearch hotSearch = new RealtimeMonitorVO.HotSearch();
            String dep = CITIES[random.nextInt(CITIES.length)];
            String arr = CITIES[random.nextInt(CITIES.length)];
            while (dep.equals(arr)) {
                arr = CITIES[random.nextInt(CITIES.length)];
            }
            hotSearch.setRouteName(dep + "-" + arr);
            hotSearch.setSearchCount(50 + random.nextInt(200));
            hotSearch.setAvgPrice(new BigDecimal(500 + random.nextInt(1500)).setScale(2, RoundingMode.HALF_UP));
            hotSearches.add(hotSearch);
        }

        vo.setHotSearches(hotSearches);

        return vo;
    }

    @Override
    public DatePriceImpactVO getDatePriceImpact() {
        log.info("获取日期因素对价格的影响");

        DatePriceImpactVO vo = new DatePriceImpactVO();

        // 设置提前天数影响
        List<DatePriceImpactVO.AdvanceBookingImpact> advanceBookingImpacts = new ArrayList<>();
        Integer[] daysBeforeDeparture = {1, 3, 7, 14, 30, 60, 90};
        BigDecimal[] priceFactors = {
            new BigDecimal("1.50"), // 1天
            new BigDecimal("1.35"), // 3天
            new BigDecimal("1.20"), // 7天
            new BigDecimal("1.10"), // 14天
            new BigDecimal("1.00"), // 30天
            new BigDecimal("0.90"), // 60天
            new BigDecimal("0.85")  // 90天
        };

        BigDecimal basePrice = new BigDecimal("1000");
        for (int i = 0; i < daysBeforeDeparture.length; i++) {
            DatePriceImpactVO.AdvanceBookingImpact impact = new DatePriceImpactVO.AdvanceBookingImpact();
            impact.setDaysInAdvance(daysBeforeDeparture[i]);
            impact.setImpactFactor(priceFactors[i]);
            impact.setAvgPrice(basePrice.multiply(priceFactors[i]).setScale(2, RoundingMode.HALF_UP));
            impact.setSampleCount(100 + random.nextInt(900));
            advanceBookingImpacts.add(impact);
        }
        vo.setAdvanceBookingImpacts(advanceBookingImpacts);

        // 设置星期几影响
        List<DatePriceImpactVO.DayOfWeekImpact> dayOfWeekImpacts = new ArrayList<>();
        String[] weekdays = {"周一", "周二", "周三", "周四", "周五", "周六", "周日"};
        BigDecimal[] weekdayFactors = {
            new BigDecimal("1.05"), // 周一
            new BigDecimal("1.00"), // 周二
            new BigDecimal("1.00"), // 周三
            new BigDecimal("1.05"), // 周四
            new BigDecimal("1.15"), // 周五
            new BigDecimal("1.20"), // 周六
            new BigDecimal("1.15")  // 周日
        };

        for (int i = 0; i < weekdays.length; i++) {
            DatePriceImpactVO.DayOfWeekImpact impact = new DatePriceImpactVO.DayOfWeekImpact();
            impact.setDayOfWeek(i + 1);
            impact.setDayName(weekdays[i]);
            impact.setImpactFactor(weekdayFactors[i]);
            impact.setAvgPrice(basePrice.multiply(weekdayFactors[i]).setScale(2, RoundingMode.HALF_UP));
            impact.setFlightCount(500 + random.nextInt(500));
            dayOfWeekImpacts.add(impact);
        }
        vo.setDayOfWeekImpacts(dayOfWeekImpacts);

        // 设置月份影响
        List<DatePriceImpactVO.MonthImpact> monthImpacts = new ArrayList<>();
        String[] months = {"1月", "2月", "3月", "4月", "5月", "6月", "7月", "8月", "9月", "10月", "11月", "12月"};
        BigDecimal[] monthFactors = {
            new BigDecimal("1.10"), // 1月
            new BigDecimal("1.15"), // 2月 (春节)
            new BigDecimal("1.00"), // 3月
            new BigDecimal("1.05"), // 4月
            new BigDecimal("1.10"), // 5月 (五一)
            new BigDecimal("1.05"), // 6月
            new BigDecimal("1.15"), // 7月 (暑假)
            new BigDecimal("1.20"), // 8月 (暑假)
            new BigDecimal("1.10"), // 9月
            new BigDecimal("1.15"), // 10月 (国庆)
            new BigDecimal("1.00"), // 11月
            new BigDecimal("1.10")  // 12月 (元旦)
        };

        for (int i = 0; i < months.length; i++) {
            DatePriceImpactVO.MonthImpact impact = new DatePriceImpactVO.MonthImpact();
            impact.setMonth(i + 1);
            impact.setMonthName(months[i]);
            impact.setImpactFactor(monthFactors[i]);
            impact.setAvgPrice(basePrice.multiply(monthFactors[i]).setScale(2, RoundingMode.HALF_UP));
            impact.setFlightCount(1000 + random.nextInt(1000));
            monthImpacts.add(impact);
        }
        vo.setMonthImpacts(monthImpacts);

        // 设置假期影响
        List<DatePriceImpactVO.HolidayImpact> holidayImpacts = new ArrayList<>();
        String[] holidays = {"春节", "国庆节", "五一劳动节", "暑假", "元旦", "中秋节", "清明节"};
        BigDecimal[] holidayFactors = {
            new BigDecimal("1.50"), // 春节
            new BigDecimal("1.40"), // 国庆节
            new BigDecimal("1.35"), // 五一劳动节
            new BigDecimal("1.30"), // 暑假
            new BigDecimal("1.25"), // 元旦
            new BigDecimal("1.30"), // 中秋节
            new BigDecimal("1.20")  // 清明节
        };

        for (int i = 0; i < holidays.length; i++) {
            DatePriceImpactVO.HolidayImpact impact = new DatePriceImpactVO.HolidayImpact();
            impact.setHolidayName(holidays[i]);
            impact.setImpactFactor(holidayFactors[i]);
            impact.setAvgPrice(basePrice.multiply(holidayFactors[i]).setScale(2, RoundingMode.HALF_UP));
            impact.setSampleCount(200 + random.nextInt(800));
            holidayImpacts.add(impact);
        }
        vo.setHolidayImpacts(holidayImpacts);

        return vo;
    }

    @Override
    public Map<String, Object> getAllDashboardData() {
        log.info("获取综合大屏数据");

        Map<String, Object> result = new HashMap<>();

        // 汇总所有数据
        result.put("overview", getOverview());
        result.put("priceTrends", getPriceTrends(null, null, null, "month"));
        result.put("routeHeat", getRouteHeat());
        result.put("airlineMarketShare", getAirlineMarketShare());
        result.put("hotCities", getHotCities(10));
        result.put("punctuality", getPunctuality(null));
        result.put("passengerFlow", getPassengerFlow("month"));
        result.put("mapData", getMapData());
        result.put("priceDistribution", getPriceDistribution());
        result.put("cityAvgPrices", getCityAvgPrices("departure"));
        result.put("predictionAccuracy", getPredictionAccuracy());
        result.put("realtimeMonitor", getRealtimeMonitor());
        result.put("datePriceImpact", getDatePriceImpact());

        return result;
    }
}