package com.flightprice.prediction.service.impl;

import com.flightprice.prediction.entity.PriceComparison;
import com.flightprice.prediction.repository.PriceComparisonRepository;
import com.flightprice.prediction.service.PriceComparisonService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Date;
import java.util.List;

@Slf4j
@Service
public class PriceComparisonServiceImpl implements PriceComparisonService {

    @Autowired
    private PriceComparisonRepository priceComparisonRepository;

    @Override
    public PriceComparison saveComparison(Long flightId, String cabinClass, 
                                        BigDecimal originalPrice, BigDecimal predictedPrice,
                                        BigDecimal actualPrice) {
        try {
            PriceComparison comparison = new PriceComparison();
            comparison.setFlightId(flightId);
            comparison.setCabinClass(cabinClass);
            comparison.setOriginalPrice(originalPrice);
            comparison.setPredictedPrice(predictedPrice);
            comparison.setActualPrice(actualPrice);
            comparison.setPredictionDate(new Date());
            comparison.setActualDate(new Date());
            
            // 计算预测准确度
            BigDecimal accuracy = calculateAccuracy(predictedPrice, actualPrice);
            comparison.setAccuracy(accuracy);
            
            comparison.setCreateTime(new Date());
            comparison.setUpdateTime(new Date());
            
            return priceComparisonRepository.save(comparison);
        } catch (Exception e) {
            log.error("保存价格比较数据失败", e);
            throw new RuntimeException("保存价格比较数据失败: " + e.getMessage());
        }
    }

    @Override
    public List<PriceComparison> getComparisonsByFlight(Long flightId) {
        return priceComparisonRepository.findByFlightId(flightId);
    }

    @Override
    public List<PriceComparison> getComparisonsByCabinClass(String cabinClass) {
        return priceComparisonRepository.findByCabinClass(cabinClass);
    }

    @Override
    public List<PriceComparison> getComparisonsByFlightAndCabin(Long flightId, String cabinClass) {
        return priceComparisonRepository.findByFlightIdAndCabinClass(flightId, cabinClass);
    }

    private BigDecimal calculateAccuracy(BigDecimal predictedPrice, BigDecimal actualPrice) {
        if (actualPrice.compareTo(BigDecimal.ZERO) == 0) {
            return BigDecimal.ZERO;
        }
        
        BigDecimal difference = predictedPrice.subtract(actualPrice).abs();
        BigDecimal accuracy = BigDecimal.ONE.subtract(
            difference.divide(actualPrice, 4, RoundingMode.HALF_UP)
        );
        
        // 确保准确度在0-1之间
        return accuracy.max(BigDecimal.ZERO).min(BigDecimal.ONE)
                      .setScale(4, RoundingMode.HALF_UP);
    }
} 