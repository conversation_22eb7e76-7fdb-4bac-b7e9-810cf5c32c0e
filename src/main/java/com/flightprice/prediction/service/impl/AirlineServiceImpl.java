package com.flightprice.prediction.service.impl;

import com.flightprice.prediction.dto.AirlineDTO;
import com.flightprice.prediction.dto.AirlineStatDTO;
import com.flightprice.prediction.dto.MonthlyStatDTO;
import com.flightprice.prediction.entity.Airline;
import com.flightprice.prediction.exception.ResourceNotFoundException;
import com.flightprice.prediction.exception.BusinessException;
import com.flightprice.prediction.repository.AirlineRepository;
import com.flightprice.prediction.service.AirlineService;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Random;
import java.util.stream.Collectors;

/**
 * 航空公司服务实现类
 */
@Service
@RequiredArgsConstructor
public class AirlineServiceImpl implements AirlineService {

    private final AirlineRepository airlineRepository;
    private final Random random = new Random();

    @Override
    public Page<Airline> findAll(String keyword, Pageable pageable) {
        return airlineRepository.findByKeyword(keyword, pageable);
    }

    @Override
    public Airline findById(Long id) {
        return airlineRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("航空公司不存在，ID: " + id));
    }

    @Override
    public Airline findByCode(String code) {
        return airlineRepository.findByCode(code)
                .orElseThrow(() -> new ResourceNotFoundException("航空公司不存在，代码: " + code));
    }

    @Override
    @Transactional
    public Airline createAirline(AirlineDTO airlineDTO) {
        if (airlineRepository.existsByCode(airlineDTO.getCode())) {
            throw new BusinessException("航空公司代码已存在");
        }

        Airline airline = convertToEntity(airlineDTO);
        return airlineRepository.save(airline);
    }

    @Override
    @Transactional
    public Airline updateAirline(Long id, AirlineDTO airlineDTO) {
        Airline airline = findById(id);

        // 如果代码发生变化，需要检查唯一性
        if (!airline.getCode().equals(airlineDTO.getCode()) &&
                airlineRepository.existsByCode(airlineDTO.getCode())) {
            throw new BusinessException("航空公司代码已存在");
        }

        // 保存原始ID
        Long originalId = airline.getId();
        
        // 使用我们安全的转换方法
        airline = convertToEntity(airlineDTO);
        
        // 确保ID不变
        airline.setId(originalId);
        
        return airlineRepository.save(airline);
    }

    @Override
    @Transactional
    public void deleteAirline(Long id) {
        if (!airlineRepository.existsById(id)) {
            throw new ResourceNotFoundException("航空公司不存在，ID: " + id);
        }
        airlineRepository.deleteById(id);
    }

    @Override
    @Transactional
    public Airline updateStatus(Long id, Boolean enabled) {
        Airline airline = findById(id);
        // 注意：airline实体中没有enabled字段，此方法已不再支持启用/禁用功能
        // 但为了保持接口兼容性，仍返回airline对象
        return airline;
    }

    @Override
    public Map<String, Object> getStatistics(Long id) {
        // 确保航空公司存在
        findById(id);

        // 这里应该是从数据库中查询真实的统计数据
        // 为了演示，我们生成一些模拟数据
        Map<String, Object> stats = new HashMap<>();
        stats.put("flightsCount", 100 + random.nextInt(900));
        stats.put("avgPrice", 500 + random.nextInt(1500));
        stats.put("onTimeRate", 70 + random.nextInt(30));
        stats.put("popularRoutes", 10 + random.nextInt(30));

        // 月度数据
        Map<String, Object> monthlyData = new HashMap<>();
        String[] months = {"Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"};
        int[] flightCounts = new int[12];
        double[] avgPrices = new double[12];
        double[] onTimeRates = new double[12];

        for (int i = 0; i < 12; i++) {
            flightCounts[i] = 50 + random.nextInt(200);
            avgPrices[i] = 400 + random.nextInt(600);
            onTimeRates[i] = 60 + random.nextInt(40);
        }

        monthlyData.put("months", months);
        monthlyData.put("flightCounts", flightCounts);
        monthlyData.put("avgPrices", avgPrices);
        monthlyData.put("onTimeRates", onTimeRates);

        stats.put("monthlyData", monthlyData);

        // 热门航线
        Map<String, Integer> popularRoutes = new HashMap<>();
        String[] routes = {"北京-上海", "北京-广州", "上海-深圳", "广州-成都", "北京-成都"};
        for (String route : routes) {
            popularRoutes.put(route, 50 + random.nextInt(200));
        }
        stats.put("popularRoutes", popularRoutes);

        return stats;
    }

    @Override
    public Airline convertToEntity(AirlineDTO airlineDTO) {
        Airline airline = new Airline();
        // 手动复制属性，排除enabled字段
        airline.setId(airlineDTO.getId());
        airline.setCode(airlineDTO.getCode());
        airline.setName(airlineDTO.getName());
        airline.setLogoUrl(airlineDTO.getLogo());
        
        // 设置虚拟字段
        airline.setEnglishName(airlineDTO.getEnglishName());
        airline.setCountry(airlineDTO.getCountry());
        airline.setFoundedYear(airlineDTO.getFoundedYear());
        airline.setHeadquarters(airlineDTO.getHeadquarters());
        airline.setAlliance(airlineDTO.getAlliance());
        airline.setFleetSize(airlineDTO.getFleetSize());
        airline.setRoutesCount(airlineDTO.getRoutesCount());
        airline.setDescription(airlineDTO.getDescription());
        airline.setLogo(airlineDTO.getLogo());
        airline.setWebsite(airlineDTO.getWebsite());
        
        return airline;
    }

    @Override
    public AirlineDTO convertToDTO(Airline airline) {
        AirlineDTO dto = new AirlineDTO();
        // 手动复制属性
        dto.setId(airline.getId());
        dto.setCode(airline.getCode());
        dto.setName(airline.getName());
        dto.setLogo(airline.getLogoUrl());
        
        // 复制虚拟字段
        dto.setEnglishName(airline.getEnglishName());
        dto.setCountry(airline.getCountry());
        dto.setFoundedYear(airline.getFoundedYear());
        dto.setHeadquarters(airline.getHeadquarters());
        dto.setAlliance(airline.getAlliance());
        dto.setFleetSize(airline.getFleetSize());
        dto.setRoutesCount(airline.getRoutesCount());
        dto.setDescription(airline.getDescription());
        dto.setWebsite(airline.getWebsite());
        
        // 默认设置为启用状态，保持与前端兼容
        dto.setEnabled(true);
        
        return dto;
    }

    @Override
    public List<AirlineDTO> getAllAirlines() {
        return airlineRepository.findAll().stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
    }

    @Override
    public List<AirlineDTO> getAllEnabledAirlines() {
        // 使用自定义查询获取所有航空公司（没有enabled字段，返回所有数据）
        return airlineRepository.findByEnabledTrue().stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
    }

    @Override
    public AirlineDTO getAirlineById(Long id) {
        return convertToDTO(findById(id));
    }

    @Override
    public AirlineDTO getAirlineByCode(String code) {
        return convertToDTO(findByCode(code));
    }

    @Override
    public boolean batchDeleteAirline(List<Long> ids) {
        try {
            airlineRepository.deleteAllById(ids);
            return true;
        } catch (Exception e) {
            throw new BusinessException("批量删除航空公司失败: " + e.getMessage());
        }
    }

    @Override
    public AirlineStatDTO getAirlineMonthlyStats(Long id, Integer year) {
        // 确保航空公司存在
        findById(id);

        // 这里应该是从数据库中查询真实的统计数据
        // 为了演示，我们生成一些模拟数据
        AirlineStatDTO statDTO = new AirlineStatDTO();
        statDTO.setAirlineId(id);
        statDTO.setYear(year);

        // 生成月度数据
        List<MonthlyStatDTO> monthlyStats = new ArrayList<>();
        for (int i = 1; i <= 12; i++) {
            MonthlyStatDTO monthStat = new MonthlyStatDTO();
            monthStat.setMonth(i);
            monthStat.setFlightCount(50 + random.nextInt(200));
            monthStat.setAvgPrice(400 + random.nextInt(600));
            monthStat.setOnTimeRate(60 + random.nextInt(40));
            monthlyStats.add(monthStat);
        }

        statDTO.setMonthlyStats(monthlyStats);
        return statDTO;
    }
}