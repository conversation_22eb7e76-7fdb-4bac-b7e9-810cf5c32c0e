package com.flightprice.prediction.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.flightprice.prediction.common.Constants;
import com.flightprice.prediction.common.PageResult;
import com.flightprice.prediction.dto.CreateOrderRequest;
import com.flightprice.prediction.entity.Flight;
import com.flightprice.prediction.entity.Order;
import com.flightprice.prediction.entity.Ticket;
import com.flightprice.prediction.entity.User;
import com.flightprice.prediction.entity.Route;
import com.flightprice.prediction.entity.City;
import com.flightprice.prediction.entity.Airport;
import com.flightprice.prediction.entity.Airline;
import com.flightprice.prediction.entity.FlightData;
import com.flightprice.prediction.exception.BusinessException;
import com.flightprice.prediction.mapper.FlightMapper;
import com.flightprice.prediction.mapper.OrderMapper;
import com.flightprice.prediction.mapper.RouteMapper;
import com.flightprice.prediction.mapper.TicketMapper;
import com.flightprice.prediction.mapper.CityMapper;
import com.flightprice.prediction.mapper.AirportMapper;
import com.flightprice.prediction.mapper.AirlineMapper;
import com.flightprice.prediction.mapper.FlightDataMapper;
import com.flightprice.prediction.service.OrderService;
import com.flightprice.prediction.service.UserService;
import com.flightprice.prediction.vo.OrderVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Random;
import java.util.UUID;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 订单服务实现类
 */
@Slf4j
@Service
public class OrderServiceImpl extends ServiceImpl<OrderMapper, Order> implements OrderService {

    @Resource
    private UserService userService;

    @Resource
    private TicketMapper ticketMapper;

    @Resource
    private FlightMapper flightMapper;

    @Resource
    private RouteMapper routeMapper;

    @Resource
    private CityMapper cityMapper;

    @Resource
    private AirportMapper airportMapper;

    @Resource
    private AirlineMapper airlineMapper;

    @Resource
    private FlightDataMapper flightDataMapper;

    @Override
    public List<OrderVO> getRecentOrders(Integer limit) {
        // 获取当前用户
        User user = userService.getCurrentUser();

        // 查询用户最近的订单
        LambdaQueryWrapper<Order> queryWrapper = new LambdaQueryWrapper<Order>()
                .eq(Order::getUserId, user.getId())
                .orderByDesc(Order::getCreateTime)
                .last("LIMIT " + limit);
                
        List<Order> orders = list(queryWrapper);
        
        // 转换为VO对象
        return orders.stream()
                .map(this::convertToOrderVO)
                .collect(Collectors.toList());
    }

    @Override
    @Transactional
    public String createOrder(CreateOrderRequest request) {
        // 获取当前用户
        User user = userService.getCurrentUser();

        // 查询机票
        Ticket ticket = ticketMapper.selectById(request.getTicketId());

        // 如果机票不存在，则动态创建
        if (ticket == null && request.getFlightId() != null && request.getCabinClass() != null) {
            ticket = createTicketForFlight(request.getFlightId(), request.getCabinClass());
        } else if (ticket == null) {
            throw new BusinessException("机票不存在");
        }

        // 检查座位是否充足
        if (ticket.getAvailableSeats() <= 0) {
            throw new BusinessException("该舱位已售罄");
        }

        // 创建订单
        Order order = new Order();
        order.setOrderNo(generateOrderNo());
        order.setUserId(user.getId());
        order.setTicketId(ticket.getId());
        order.setPassengerName(request.getPassengerName());
        order.setPassengerIdCard(request.getPassengerIdCard());
        order.setPassengerPhone(request.getPassengerPhone());
        order.setStatus(Constants.OrderStatus.UNPAID);
        order.setTotalAmount(ticket.getPrice());

        // 保存订单
        save(order);

        // 减少可用座位数
        ticket.setAvailableSeats(ticket.getAvailableSeats() - 1);
        ticketMapper.updateById(ticket);

        return order.getOrderNo();
    }

    @Override
    @Transactional
    public boolean payOrder(String orderNo) {
        // 获取当前用户
        User user = userService.getCurrentUser();

        // 查询订单
        Order order = getOne(new LambdaQueryWrapper<Order>()
                .eq(Order::getOrderNo, orderNo)
                .eq(Order::getUserId, user.getId()));

        if (order == null) {
            throw new BusinessException("订单不存在");
        }

        if (!Constants.OrderStatus.UNPAID.equals(order.getStatus())) {
            throw new BusinessException("订单状态不正确");
        }

        // 更新订单状态
        order.setStatus(Constants.OrderStatus.PAID);
        order.setPaymentTime(new Date());
        updateById(order);

        return true;
    }

    @Override
    @Transactional
    public void cancelOrder(String orderNo) {
        // 获取当前用户
        User user = userService.getCurrentUser();

        // 查询订单
        Order order = getOne(new LambdaQueryWrapper<Order>()
                .eq(Order::getOrderNo, orderNo)
                .eq(Order::getUserId, user.getId()));

        if (order == null) {
            throw new BusinessException("订单不存在");
        }

        if (Constants.OrderStatus.PAID.equals(order.getStatus())) {
            throw new BusinessException("已支付订单不能取消，请联系客服");
        }

        if (Constants.OrderStatus.CANCELLED.equals(order.getStatus())) {
            throw new BusinessException("订单已取消");
        }

        // 更新订单状态
        order.setStatus(Constants.OrderStatus.CANCELLED);
        updateById(order);

        // 恢复座位数
        Ticket ticket = ticketMapper.selectById(order.getTicketId());
        if (ticket != null) {
            ticket.setAvailableSeats(ticket.getAvailableSeats() + 1);
            ticketMapper.updateById(ticket);
        }
    }

    @Override
    public OrderVO getOrderDetail(String orderNo) {
        // 获取当前用户
        User user = userService.getCurrentUser();
        boolean isAdmin = false;

        // Check if user has admin role
        if (user.getRoles() != null && !user.getRoles().isEmpty()) {
            isAdmin = user.getRoles().stream()
                    .anyMatch(role -> "ADMIN".equals(role.getName()));
        }

        // 查询订单
        LambdaQueryWrapper<Order> queryWrapper = new LambdaQueryWrapper<Order>()
                .eq(Order::getOrderNo, orderNo);

        // 如果不是管理员，只能查看自己的订单
        if (!isAdmin) {
            queryWrapper.eq(Order::getUserId, user.getId());
        }

        Order order = getOne(queryWrapper);

        if (order == null) {
            throw new BusinessException("订单不存在");
        }

        return convertToOrderVO(order);
    }

    @Override
    public PageResult<OrderVO> getUserOrders(Integer pageNum, Integer pageSize, String status) {
        // 获取当前用户
        User user = userService.getCurrentUser();

        // 构建查询条件
        LambdaQueryWrapper<Order> queryWrapper = new LambdaQueryWrapper<Order>()
                .eq(Order::getUserId, user.getId())
                .eq(StringUtils.isNotBlank(status), Order::getStatus, status)
                .orderByDesc(Order::getCreateTime);

        // 分页查询
        Page<Order> page = new Page<>(pageNum, pageSize);
        page(page, queryWrapper);

        // 转换结果
        List<OrderVO> orderVOList = new ArrayList<>();
        for (Order order : page.getRecords()) {
            orderVOList.add(convertToOrderVO(order));
        }

        return PageResult.build(orderVOList, page.getTotal(), pageNum, pageSize);
    }

    @Override
    public PageResult<OrderVO> getAllOrders(Integer pageNum, Integer pageSize, String status) {
        // 构建查询条件
        LambdaQueryWrapper<Order> queryWrapper = new LambdaQueryWrapper<Order>()
                .eq(StringUtils.isNotBlank(status), Order::getStatus, status)
                .orderByDesc(Order::getCreateTime);

        // 分页查询
        Page<Order> page = new Page<>(pageNum, pageSize);
        page(page, queryWrapper);

        // 转换结果
        List<OrderVO> orderVOList = new ArrayList<>();
        for (Order order : page.getRecords()) {
            orderVOList.add(convertToOrderVO(order));
        }

        return PageResult.build(orderVOList, page.getTotal(), pageNum, pageSize);
    }

    /**
     * 生成订单号
     */
    private String generateOrderNo() {
        return "ORD" + System.currentTimeMillis() + UUID.randomUUID().toString().substring(0, 4).toUpperCase();
    }

    /**
     * 为航班创建机票
     */
    private Ticket createTicketForFlight(Long flightId, String cabinClass) {
        log.info("为航班ID={}创建{}舱位的机票", flightId, cabinClass);

        // 查询是否已有相同舱位的机票
        Ticket existingTicket = ticketMapper.selectOne(new LambdaQueryWrapper<Ticket>()
                .eq(Ticket::getFlightId, flightId)
                .eq(Ticket::getCabinClass, cabinClass));

        if (existingTicket != null) {
            log.info("找到现有机票 ID={}", existingTicket.getId());
            return existingTicket;
        }

        BigDecimal basePrice = BigDecimal.valueOf(1000); // 默认价格
        int availableSeats = 150; // 默认座位数

        // 查询航班数据（从FlightData表）
        FlightData flightData = flightDataMapper.selectById(flightId);
        if (flightData != null) {
            // 使用FlightData中的价格
            if (flightData.getPrice() != null) {
                basePrice = flightData.getPrice();
            }

            if (flightData.getPassengerCount() != null) {
                availableSeats = flightData.getPassengerCount();
            }
        } else {
            log.warn("航班数据不存在 (ID={}), 使用测试数据创建机票", flightId);
            // 创建测试数据继续流程，而不是抛出异常
        }

        // 创建新的机票
        Ticket ticket = new Ticket();
        ticket.setFlightId(flightId);
        ticket.setCabinClass(cabinClass);

        // 根据舱位类型设置价格和座位
        switch (cabinClass) {
            case "ECONOMY":
                ticket.setPrice(basePrice);
                ticket.setAvailableSeats(availableSeats);
                break;
            case "BUSINESS":
                ticket.setPrice(basePrice.multiply(new BigDecimal("2.5")));
                ticket.setAvailableSeats(30);
                break;
            case "FIRST":
                ticket.setPrice(basePrice.multiply(new BigDecimal("4.0")));
                ticket.setAvailableSeats(10);
                break;
            default:
                ticket.setPrice(basePrice);
                ticket.setAvailableSeats(availableSeats);
        }

        ticket.setDiscount(new BigDecimal("1.0"));
        ticket.setCreateTime(new Date());
        ticket.setUpdateTime(new Date());

        // 保存机票
        ticketMapper.insert(ticket);
        log.info("成功创建新机票 ID={}", ticket.getId());

        return ticket;
    }

    /**
     * 获取航班的基础价格
     */
    private BigDecimal getBasePrice(Flight flight) {
        // 如果有航线信息，使用航线的基础价格
        if (flight.getRouteId() != null) {
            Route route = routeMapper.selectById(flight.getRouteId());
            if (route != null && route.getBasePrice() != null) {
                return route.getBasePrice();
            }
        }

        // 否则生成一个默认价格
        return new BigDecimal("1000.00");
    }

    /**
     * 获取舱位的默认座位数
     */
    private int getDefaultSeats(String cabinClass) {
        switch (cabinClass) {
            case "ECONOMY":
                return 150;
            case "BUSINESS":
                return 30;
            case "FIRST":
                return 10;
            default:
                return 150;
        }
    }

    /**
     * 调整机票价格
     */
    private void adjustTicketPrice(Ticket ticket, Date departureTime) {
        if (departureTime == null) {
            return;
        }

        // 查询航班信息，获取更多详细信息
        Flight flight = flightMapper.selectById(ticket.getFlightId());
        if (flight == null) {
            return;
        }

        // 获取出发时间信息
        Calendar cal = Calendar.getInstance();
        cal.setTime(departureTime);

        // 距离当前日期的天数
        long daysBetween = TimeUnit.MILLISECONDS.toDays(departureTime.getTime() - new Date().getTime());

        // 周几（1-7）
        int dayOfWeek = cal.get(Calendar.DAY_OF_WEEK);

        // 月份（1-12）
        int month = cal.get(Calendar.MONTH) + 1;

        // 当天小时
        int hour = cal.get(Calendar.HOUR_OF_DAY);

        // 基础价格调整因子
        double baseFactor = 1.0;

        // 1. 距离当前日期越近，价格越高
        if (daysBetween <= 3) {
            baseFactor *= 1.3; // 紧急出行，价格高
            log.debug("紧急出行因子(<=3天): 1.3");
        } else if (daysBetween <= 7) {
            baseFactor *= 1.2;
            log.debug("紧急出行因子(<=7天): 1.2");
        } else if (daysBetween <= 14) {
            baseFactor *= 1.1;
            log.debug("提前预订因子(<=14天): 1.1");
        } else if (daysBetween <= 30) {
            baseFactor *= 1.0;
            log.debug("提前预订因子(<=30天): 1.0");
        } else if (daysBetween <= 60) {
            baseFactor *= 0.9;
            log.debug("提前预订因子(<=60天): 0.9");
        } else {
            baseFactor *= 0.85; // 提前预订，价格低
            log.debug("提前预订因子(>60天): 0.85");
        }

        // 2. 周末价格较高
        if (dayOfWeek == Calendar.SATURDAY || dayOfWeek == Calendar.SUNDAY) {
            baseFactor *= 1.1;
            log.debug("周末因子: 1.1");
        }

        // 3. 旅游时节价格较高
        double seasonFactor = 1.0;
        if (month == 1 || month == 2) { // 春节
            seasonFactor = 1.2;
            log.debug("春节季节因子: 1.2");
        } else if (month == 7 || month == 8) { // 暑假
            seasonFactor = 1.3;
            log.debug("暑假季节因子: 1.3");
        } else if (month == 10) { // 国庆
            seasonFactor = 1.25;
            log.debug("国庆季节因子: 1.25");
        } else if (month == 5 || month == 6) { // 毕业季
            seasonFactor = 1.15;
            log.debug("毕业季季节因子: 1.15");
        } else if (month == 12) { // 圣诞/元旦
            seasonFactor = 1.2;
            log.debug("圣诞/元旦季节因子: 1.2");
        }
        baseFactor *= seasonFactor;

        // 4. 时间段因素（早晚高峰价格更高）
        double timeSlotFactor = 1.0;
        if (hour >= 6 && hour < 9) {
            // 早高峰 6:00-9:00
            timeSlotFactor = 1.15;
            log.debug("早高峰时段因子: 1.15");
        } else if (hour >= 17 && hour < 20) {
            // 晚高峰 17:00-20:00
            timeSlotFactor = 1.2;
            log.debug("晚高峰时段因子: 1.2");
        } else if (hour >= 12 && hour < 14) {
            // 中午 12:00-14:00
            timeSlotFactor = 0.95;
            log.debug("中午时段因子: 0.95");
        } else if (hour >= 0 && hour < 6) {
            // 凌晨 0:00-6:00
            timeSlotFactor = 0.85;
            log.debug("凌晨时段因子: 0.85");
        } else if (hour >= 20 && hour < 24) {
            // 晚间 20:00-24:00
            timeSlotFactor = 0.9;
            log.debug("晚间时段因子: 0.9");
        }
        baseFactor *= timeSlotFactor;

        // 5. 距离因素
        double distanceFactor = 1.0;
        if (flight.getRouteId() != null) {
            Route route = routeMapper.selectById(flight.getRouteId());
            if (route != null && route.getDistance() != null) {
                int distance = route.getDistance();

                // 距离越长，每公里价格越低（规模经济效应）
                if (distance < 500) {
                    distanceFactor = 1.2; // 短途航线，价格较高
                    log.debug("短途航线距离因子(<500km): 1.2");
                } else if (distance < 1000) {
                    distanceFactor = 1.0; // 中程航线，标准价格
                    log.debug("中程航线距离因子(<1000km): 1.0");
                } else if (distance < 2000) {
                    distanceFactor = 0.9; // 中长程航线
                    log.debug("中长程航线距离因子(<2000km): 0.9");
                } else {
                    distanceFactor = 0.85; // 长程航线，单位距离价格较低
                    log.debug("长程航线距离因子(>=2000km): 0.85");
                }
            }
        }
        baseFactor *= distanceFactor;

        // 6. 舱位类型特定调整
        double cabinFactor = 1.0;
        switch (ticket.getCabinClass()) {
            case "BUSINESS":
                // 商务舱在早高峰和晚高峰更受商务人士欢迎
                if ((hour >= 6 && hour < 9) || (hour >= 17 && hour < 20)) {
                    cabinFactor = 1.1;
                    log.debug("商务舱高峰时段额外因子: 1.1");
                }
                break;
            case "FIRST":
                // 头等舱在长途航线更值得
                if (flight.getRouteId() != null) {
                    Route route = routeMapper.selectById(flight.getRouteId());
                    if (route != null && route.getDistance() != null && route.getDistance() >= 2000) {
                        cabinFactor = 1.05;
                        log.debug("头等舱长途航线额外因子: 1.05");
                    }
                }
                break;
        }
        baseFactor *= cabinFactor;

        // 7. 添加一些随机浮动（使价格看起来更自然）
        Random random = new Random(flight.getId() + ticket.getCabinClass().hashCode() + departureTime.getTime());
        double randomFactor = 0.95 + random.nextDouble() * 0.1; // 0.95-1.05之间的随机数
        baseFactor *= randomFactor;
        log.debug("随机浮动因子: {}", randomFactor);

        // 综合所有因素，计算最终价格
        log.debug("原始价格: {}, 最终调整因子: {}", ticket.getPrice(), baseFactor);
        BigDecimal newPrice = ticket.getPrice().multiply(new BigDecimal(baseFactor)).setScale(2, RoundingMode.HALF_UP);
        ticket.setPrice(newPrice);
        log.debug("调整后价格: {}", newPrice);
    }

    /**
     * 转换为订单VO
     */
    private OrderVO convertToOrderVO(Order order) {
        OrderVO orderVO = new OrderVO();
        BeanUtils.copyProperties(order, orderVO);

        // 查询机票信息
        Ticket ticket = ticketMapper.selectById(order.getTicketId());
        if (ticket != null) {
            orderVO.setCabinClass(ticket.getCabinClass());

            // 查询航班信息
            Flight flight = flightMapper.selectById(ticket.getFlightId());
            if (flight != null) {
                orderVO.setFlightNumber(flight.getFlightNumber());
                orderVO.setDepartureTime(flight.getDepartureTime());
                orderVO.setArrivalTime(flight.getArrivalTime());

                // 查询航线信息
                Route route = routeMapper.selectById(flight.getRouteId());
                if (route != null) {
                    // 查询出发城市
                    City departureCity = cityMapper.selectById(route.getDepartureCityId());
                    if (departureCity != null) {
                        orderVO.setDepartureCityName(departureCity.getName());
                    } else {
                        orderVO.setDepartureCityName("出发城市");
                    }

                    // 查询到达城市
                    City arrivalCity = cityMapper.selectById(route.getArrivalCityId());
                    if (arrivalCity != null) {
                        orderVO.setArrivalCityName(arrivalCity.getName());
                    } else {
                        orderVO.setArrivalCityName("到达城市");
                    }
                } else {
                    // 如果没有航线信息，设置默认值
                    orderVO.setDepartureCityName("出发城市");
                    orderVO.setArrivalCityName("到达城市");
                }

                // 查询出发机场
                Airport departureAirport = airportMapper.selectById(flight.getDepartureAirportId());
                if (departureAirport != null) {
                    orderVO.setDepartureAirportName(departureAirport.getName());
                } else {
                    orderVO.setDepartureAirportName("出发机场");
                }

                // 查询到达机场
                Airport arrivalAirport = airportMapper.selectById(flight.getArrivalAirportId());
                if (arrivalAirport != null) {
                    orderVO.setArrivalAirportName(arrivalAirport.getName());
                } else {
                    orderVO.setArrivalAirportName("到达机场");
                }

                // 查询航空公司
                Airline airline = airlineMapper.selectById(flight.getAirlineId());
                if (airline != null) {
                    orderVO.setAirlineName(airline.getName());
                    orderVO.setAirlineLogo(airline.getLogoUrl());
                } else {
                    orderVO.setAirlineName("航空公司");
                }
            } else {
                // 如果在flight表中没有找到数据，尝试从flight_data表获取
                FlightData flightData = flightDataMapper.selectById(ticket.getFlightId());
                if (flightData != null) {
                    // 填充航班信息
                    orderVO.setFlightNumber(flightData.getFlightNumber());

                    // 处理时间字段
                    Date departureTime = null;
                    Date arrivalTime = null;

                    // 处理字符串时间
                    if (flightData.getDepartureTime() != null && !flightData.getDepartureTime().isEmpty()) {
                        try {
                            // 解析时间字符串，假设格式为 "HH:mm"
                            String timeStr = flightData.getDepartureTime();
                            if (flightData.getFlightDate() != null && !flightData.getFlightDate().isEmpty()) {
                                // 合并日期和时间
                                SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy/MM/dd");
                                Date flightDate = dateFormat.parse(flightData.getFlightDate());

                                Calendar cal = Calendar.getInstance();
                                cal.setTime(flightDate);

                                String[] parts = timeStr.split(":");
                                if (parts.length >= 2) {
                                    cal.set(Calendar.HOUR_OF_DAY, Integer.parseInt(parts[0]));
                                    cal.set(Calendar.MINUTE, Integer.parseInt(parts[1]));
                                    cal.set(Calendar.SECOND, 0);
                                    cal.set(Calendar.MILLISECOND, 0);
                                    departureTime = cal.getTime();
                                }
                            }
                        } catch (Exception e) {
                            log.error("解析出发时间字符串失败", e);
                        }
                    }

                    // 同样处理到达时间
                    if (flightData.getArrivalTime() != null && !flightData.getArrivalTime().isEmpty()) {
                        try {
                            // 解析时间字符串，假设格式为 "HH:mm"
                            String timeStr = flightData.getArrivalTime();
                            if (flightData.getFlightDate() != null && !flightData.getFlightDate().isEmpty()) {
                                // 合并日期和时间
                                SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy/MM/dd");
                                Date flightDate = dateFormat.parse(flightData.getFlightDate());

                                Calendar cal = Calendar.getInstance();
                                cal.setTime(flightDate);

                                String[] parts = timeStr.split(":");
                                if (parts.length >= 2) {
                                    cal.set(Calendar.HOUR_OF_DAY, Integer.parseInt(parts[0]));
                                    cal.set(Calendar.MINUTE, Integer.parseInt(parts[1]));
                                    cal.set(Calendar.SECOND, 0);
                                    cal.set(Calendar.MILLISECOND, 0);
                                    arrivalTime = cal.getTime();
                                }
                            }
                        } catch (Exception e) {
                            log.error("解析到达时间字符串失败", e);
                        }
                    }

                    orderVO.setDepartureTime(departureTime);
                    orderVO.setArrivalTime(arrivalTime);

                    // 填充城市信息
                    orderVO.setDepartureCityName(flightData.getDepartureCity());
                    orderVO.setArrivalCityName(flightData.getArrivalCity());

                    // 填充机场信息
                    orderVO.setDepartureAirportName(flightData.getDepartureAirport());
                    orderVO.setArrivalAirportName(flightData.getArrivalAirport());

                    // 填充航空公司信息
                    orderVO.setAirlineName(flightData.getAirline());
                }
            }
        }

        return orderVO;
    }
}
