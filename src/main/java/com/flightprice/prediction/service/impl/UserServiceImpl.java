package com.flightprice.prediction.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.flightprice.prediction.common.Constants;
import com.flightprice.prediction.dto.AssignUserRolesRequest;
import com.flightprice.prediction.dto.BindEmailRequest;
import com.flightprice.prediction.dto.BindPhoneRequest;
import com.flightprice.prediction.dto.ChangePasswordRequest;
import com.flightprice.prediction.dto.LoginDTO;
import com.flightprice.prediction.dto.LoginRequest;
import com.flightprice.prediction.dto.LoginResponse;
import com.flightprice.prediction.dto.RegisterDTO;
import com.flightprice.prediction.dto.RegisterRequest;
import com.flightprice.prediction.dto.UpdateUserInfoRequest;
import com.flightprice.prediction.dto.UserDTO;
import com.flightprice.prediction.dto.UserPermissionsDTO;
import com.flightprice.prediction.vo.LoginVO;
import com.flightprice.prediction.entity.Permission;
import com.flightprice.prediction.entity.Role;
import com.flightprice.prediction.entity.User;
import com.flightprice.prediction.exception.BusinessException;
import com.flightprice.prediction.mapper.UserMapper;
import com.flightprice.prediction.repository.PermissionRepository;
import com.flightprice.prediction.repository.RoleRepository;
import com.flightprice.prediction.repository.UserRepository;
import com.flightprice.prediction.security.UserDetailsImpl;
import com.flightprice.prediction.service.UserService;
import com.flightprice.prediction.util.JwtUtil;
import com.flightprice.prediction.vo.UserVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Optional;
import java.util.Set;

/**
 * 用户服务实现类
 * 结合JPA与MyBatis-Plus
 */
@Slf4j
@Service
public class UserServiceImpl extends ServiceImpl<UserMapper, User> implements UserService {

    private final AuthenticationManager authenticationManager;
    private final PasswordEncoder passwordEncoder;
    private final JwtUtil jwtUtil;

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private RoleRepository roleRepository;

    @Autowired
    private PermissionRepository permissionRepository;

    public UserServiceImpl(AuthenticationManager authenticationManager, PasswordEncoder passwordEncoder, JwtUtil jwtUtil) {
        this.authenticationManager = authenticationManager;
        this.passwordEncoder = passwordEncoder;
        this.jwtUtil = jwtUtil;
    }

    @Override
    public LoginResponse login(LoginRequest request) {
        // 认证
        Authentication authentication = authenticationManager.authenticate(
                new UsernamePasswordAuthenticationToken(request.getUsername(), request.getPassword()));

        // 保存认证信息
        SecurityContextHolder.getContext().setAuthentication(authentication);

        // 获取用户信息
        UserDetailsImpl userDetails = (UserDetailsImpl) authentication.getPrincipal();
        User user = userDetails.getUser();

        // 生成token
        String token = jwtUtil.generateToken(user.getId(), user.getUsername());

        // 构建返回结果
        LoginResponse response = new LoginResponse();
        response.setUserId(user.getId());
        response.setUsername(user.getUsername());
        response.setName(user.getFullName());
        // Set role from the first role in the set if available
        if (user.getRoles() != null && !user.getRoles().isEmpty()) {
            response.setRole(user.getRoles().iterator().next().getName());
        }
        response.setToken(token);

        return response;
    }

    @Override
    @Transactional
    public Long register(RegisterRequest request) {
        // 检查用户名是否已存在
        long count = count(new LambdaQueryWrapper<User>()
                .eq(User::getUsername, request.getUsername()));
        if (count > 0) {
            throw new BusinessException("用户名已存在");
        }

        // 创建用户
        User user = new User();
        BeanUtils.copyProperties(request, user);
        // 默认角色为普通用户 - 需要通过关联表设置
        // 密码加密
        user.setPassword(passwordEncoder.encode(request.getPassword()));

        // 保存用户
        save(user);

        return user.getId();
    }

    @Override
    public User getCurrentUser() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (authentication != null && authentication.getPrincipal() instanceof UserDetailsImpl) {
            UserDetailsImpl userDetails = (UserDetailsImpl) authentication.getPrincipal();
            return userDetails.getUser();
        }
        throw new BusinessException("当前用户未登录");
    }

    @Override
    public UserVO getCurrentUserInfo() {
        // 获取当前用户
        User user = getCurrentUser();

        // 转换为VO
        UserVO userVO = new UserVO();
        BeanUtils.copyProperties(user, userVO);

        return userVO;
    }

    @Override
    @Transactional
    public boolean updateUserInfo(UpdateUserInfoRequest request) {
        // 获取当前用户
        User user = getCurrentUser();

        // 更新用户信息
        if (request.getNickname() != null) {
            user.setFullName(request.getNickname());
        }
        if (request.getEmail() != null) {
            user.setEmail(request.getEmail());
        }
        if (request.getPhone() != null) {
            user.setPhone(request.getPhone());
        }

        // 更新用户
        // Updated time will be handled by @UpdateTimestamp
        updateById(user);

        return true;
    }

    @Override
    @Transactional
    public boolean changePassword(ChangePasswordRequest request) {
        // 获取当前用户
        User user = getCurrentUser();

        // 验证旧密码
        if (!passwordEncoder.matches(request.getOldPassword(), user.getPassword())) {
            throw new BusinessException("旧密码不正确");
        }

        // 验证新密码与确认密码是否一致
        if (!request.getNewPassword().equals(request.getConfirmPassword())) {
            throw new BusinessException("新密码与确认密码不一致");
        }

        // 更新密码
        user.setPassword(passwordEncoder.encode(request.getNewPassword()));
        // Updated time will be handled by @UpdateTimestamp
        updateById(user);

        return true;
    }

    @Override
    @Transactional
    public boolean bindPhone(BindPhoneRequest request) {
        // 获取当前用户
        User user = getCurrentUser();
        
        // 这里可以添加验证码验证逻辑
        // 由于是直接修改，我们简化了流程，不验证验证码
        
        // 更新手机号
        user.setPhone(request.getPhone());
        
        // 保存用户
        updateById(user);
        
        return true;
    }

    @Override
    @Transactional
    public boolean bindEmail(BindEmailRequest request) {
        // 获取当前用户
        User user = getCurrentUser();
        
        // 这里可以添加验证码验证逻辑
        // 由于是直接修改，我们简化了流程，不验证验证码
        
        // 更新邮箱
        user.setEmail(request.getEmail());
        
        // 保存用户
        updateById(user);
        
        return true;
    }

    /**
     * 使用JPA保存用户 - 用于简单操作
     */
    @Override
    @Transactional
    public User saveUserWithJpa(User user) {
        return userRepository.save(user);
    }

    /**
     * 使用MyBatis-Plus保存用户 - 可与其他MyBatis操作组合
     */
    @Override
    @Transactional
    public boolean saveUserWithMybatis(User user) {
        return this.save(user);
    }

    /**
     * 使用JPA查询 - 简单查询，利用JPA自动生成查询方法
     */
    @Override
    public Optional<User> findUserByUsername(String username) {
        return userRepository.findByUsername(username);
    }

    /**
     * 使用MyBatis-Plus进行复杂查询
     */
    @Override
    public List<User> findUsersByCondition(String role, String keyword) {
        LambdaQueryWrapper<User> queryWrapper = Wrappers.<User>lambdaQuery();

        // Check if user has roles that match the given role
        if (role != null) {
            queryWrapper.exists("SELECT 1 FROM user_roles ur JOIN roles r ON ur.role_id = r.id WHERE ur.user_id = users.id AND r.name = {0}", role);
        }

        // Add keyword search conditions
        if (keyword != null) {
            queryWrapper.and(wrapper -> wrapper
                    .like(User::getUsername, keyword)
                    .or()
                    .like(User::getFullName, keyword)
                    .or()
                    .like(User::getEmail, keyword)
            );
        }

        return this.list(queryWrapper);
    }

    @Override
    @Transactional
    public void updateLastLogin(String username) {
        Optional<User> userOpt = userRepository.findByUsername(username);
        if (userOpt.isPresent()) {
            User user = userOpt.get();
            user.setLastLogin(LocalDateTime.now());
            userRepository.save(user);
        }
    }

    @Override
    @Transactional
    public void updatePassword(Long id, String oldPassword, String newPassword) {
        User user = userRepository.findById(id)
                .orElseThrow(() -> new BusinessException("用户不存在"));

        // 验证旧密码
        if (!passwordEncoder.matches(oldPassword, user.getPassword())) {
            throw new BusinessException("旧密码不正确");
        }

        // 更新密码
        user.setPassword(passwordEncoder.encode(newPassword));
        userRepository.save(user);
    }

    @Override
    public boolean existsByEmail(String email) {
        return userRepository.existsByEmail(email);
    }

    @Override
    public boolean existsByUsername(String username) {
        return userRepository.existsByUsername(username);
    }

    @Override
    @Transactional
    public void assignUserRolesAndPermissions(AssignUserRolesRequest request) {
        // 获取用户
        User user = userRepository.findById(request.getUserId())
                .orElseThrow(() -> new BusinessException("用户不存在"));

        // 清空现有角色
        user.getRoles().clear();

        // 添加新角色
        if (request.getRoleIds() != null && !request.getRoleIds().isEmpty()) {
            for (Long roleId : request.getRoleIds()) {
                Role role = roleRepository.findById(roleId)
                        .orElseThrow(() -> new BusinessException("角色不存在，ID: " + roleId));
                user.getRoles().add(role);
            }
        }

        // 保存用户
        userRepository.save(user);
    }

    @Override
    public Page<User> findAll(String keyword, Pageable pageable) {
        if (keyword != null && !keyword.isEmpty()) {
            // Use a more generic approach with a specification or query
            return userRepository.findAll(pageable);
        } else {
            return userRepository.findAll(pageable);
        }
    }

    @Override
    public User findByUsername(String username) {
        return userRepository.findByUsername(username)
                .orElseThrow(() -> new BusinessException("用户不存在"));
    }

    @Override
    public User findById(Long id) {
        return userRepository.findById(id)
                .orElseThrow(() -> new BusinessException("用户不存在"));
    }

    @Override
    @Transactional
    public User createUserEntity(UserDTO userDTO) {
        // 检查用户名是否已存在
        if (userRepository.existsByUsername(userDTO.getUsername())) {
            throw new BusinessException("用户名已存在");
        }

        // 检查邮箱是否已存在
        if (userDTO.getEmail() != null && userRepository.existsByEmail(userDTO.getEmail())) {
            throw new BusinessException("邮箱已存在");
        }

        // 创建用户
        User user = new User();
        BeanUtils.copyProperties(userDTO, user);

        // 密码加密
        if (userDTO.getPassword() != null && !userDTO.getPassword().isEmpty()) {
            user.setPassword(passwordEncoder.encode(userDTO.getPassword()));
        }

        // 设置创建和更新时间
        LocalDateTime now = LocalDateTime.now();
        user.setCreatedAt(now);
        user.setUpdatedAt(now);

        // 设置角色
        if (userDTO.getRoleIds() != null && !userDTO.getRoleIds().isEmpty()) {
            Set<Role> roles = new HashSet<>();
            userDTO.getRoleIds().forEach(roleId -> {
                Role role = roleRepository.findById(roleId)
                        .orElseThrow(() -> new BusinessException("角色不存在，ID: " + roleId));
                roles.add(role);
            });
            user.setRoles(roles);
        }

        // 保存用户
        return userRepository.save(user);
    }

    @Override
    @Transactional
    public void updateUserRoles(Long userId, Set<Long> roleIds) {
        User user = userRepository.findById(userId)
                .orElseThrow(() -> new BusinessException("用户不存在"));

        // 清空现有角色
        user.getRoles().clear();

        // 添加新角色
        if (roleIds != null && !roleIds.isEmpty()) {
            roleIds.forEach(roleId -> {
                Role role = roleRepository.findById(roleId)
                        .orElseThrow(() -> new BusinessException("角色不存在，ID: " + roleId));
                user.getRoles().add(role);
            });
        }

        // 保存用户
        userRepository.save(user);
    }

    @Override
    @Transactional
    public User updateUserEntity(Long id, UserDTO userDTO) {
        User user = userRepository.findById(id)
                .orElseThrow(() -> new BusinessException("用户不存在"));

        // 更新用户信息
        if (userDTO.getUsername() != null) {
            // 检查用户名是否已存在
            if (!user.getUsername().equals(userDTO.getUsername()) &&
                    userRepository.existsByUsername(userDTO.getUsername())) {
                throw new BusinessException("用户名已存在");
            }
            user.setUsername(userDTO.getUsername());
        }

        if (userDTO.getPassword() != null && !userDTO.getPassword().isEmpty()) {
            user.setPassword(passwordEncoder.encode(userDTO.getPassword()));
        }

        if (userDTO.getFullName() != null) {
            user.setFullName(userDTO.getFullName());
        }

        if (userDTO.getEmail() != null) {
            // 检查邮箱是否已存在
            if (!user.getEmail().equals(userDTO.getEmail()) &&
                    userRepository.existsByEmail(userDTO.getEmail())) {
                throw new BusinessException("邮箱已存在");
            }
            user.setEmail(userDTO.getEmail());
        }

        if (userDTO.getPhone() != null) {
            user.setPhone(userDTO.getPhone());
        }

        if (userDTO.getEnabled() != null) {
            user.setEnabled(userDTO.getEnabled());
        }

        if (userDTO.getAvatar() != null) {
            user.setAvatar(userDTO.getAvatar());
        }

        if (userDTO.getDescription() != null) {
            user.setDescription(userDTO.getDescription());
        }

        // 更新角色
        if (userDTO.getRoleIds() != null && !userDTO.getRoleIds().isEmpty()) {
            updateUserRoles(id, userDTO.getRoleIds());
        }

        // 保存用户
        return userRepository.save(user);
    }

    @Override
    public UserPermissionsDTO getUserPermissions(Long id) {
        // 获取用户
        User user = userRepository.findById(id)
                .orElseThrow(() -> new BusinessException("用户不存在"));

        // 创建返回对象
        UserPermissionsDTO dto = new UserPermissionsDTO();
        dto.setUserId(user.getId());
        dto.setUsername(user.getUsername());

        // 获取用户角色
        Set<Role> roles = user.getRoles();
        if (roles != null && !roles.isEmpty()) {
            dto.setRoles(new ArrayList<>(roles));

            // 收集所有权限
            List<Permission> allPermissions = new ArrayList<>();
            for (Role role : roles) {
                if (role.getPermissions() != null) {
                    allPermissions.addAll(role.getPermissions());
                }
            }

            // 去重
            List<Permission> uniquePermissions = allPermissions.stream()
                    .distinct()
                    .collect(java.util.stream.Collectors.toList());

            dto.setPermissions(uniquePermissions);
        }

        return dto;
    }

    @Override
    @Transactional
    public void changePassword(Long id, ChangePasswordRequest request) {
        User user = userRepository.findById(id)
                .orElseThrow(() -> new BusinessException("用户不存在"));

        // 验证旧密码
        if (!passwordEncoder.matches(request.getOldPassword(), user.getPassword())) {
            throw new BusinessException("旧密码不正确");
        }

        // 验证新密码与确认密码是否一致
        if (!request.getNewPassword().equals(request.getConfirmPassword())) {
            throw new BusinessException("新密码与确认密码不一致");
        }

        // 更新密码
        user.setPassword(passwordEncoder.encode(request.getNewPassword()));
        userRepository.save(user);
    }

    @Override
    @Transactional
    public void removeUser(Long id) {
        User user = userRepository.findById(id)
                .orElseThrow(() -> new BusinessException("用户不存在"));

        // 删除用户
        userRepository.delete(user);
    }

    @Override
    @Transactional
    public UserDTO updateUser(Long id, UserDTO userDTO) {
        User user = updateUserEntity(id, userDTO);

        // 转换为DTO
        UserDTO result = new UserDTO();
        BeanUtils.copyProperties(user, result);

        // 设置角色ID
        if (user.getRoles() != null && !user.getRoles().isEmpty()) {
            Set<Long> roleIds = new HashSet<>();
            for (Role role : user.getRoles()) {
                roleIds.add(role.getId());
            }
            result.setRoleIds(roleIds);
        }

        return result;
    }

    @Override
    @Transactional
    public UserDTO createUserWithDetails(UserDTO userDTO) {
        User user = createUserEntity(userDTO);

        // 转换为DTO
        UserDTO result = new UserDTO();
        BeanUtils.copyProperties(user, result);

        // 设置角色ID
        if (user.getRoles() != null && !user.getRoles().isEmpty()) {
            Set<Long> roleIds = new HashSet<>();
            for (Role role : user.getRoles()) {
                roleIds.add(role.getId());
            }
            result.setRoleIds(roleIds);
        }

        return result;
    }

    @Override
    public UserDTO getUserInfo() {
        // 获取当前用户
        User user = getCurrentUser();

        // 转换为DTO
        UserDTO userDTO = new UserDTO();
        BeanUtils.copyProperties(user, userDTO);

        // 设置角色ID
        if (user.getRoles() != null && !user.getRoles().isEmpty()) {
            Set<Long> roleIds = new HashSet<>();
            for (Role role : user.getRoles()) {
                roleIds.add(role.getId());
            }
            userDTO.setRoleIds(roleIds);
        }

        return userDTO;
    }

    @Override
    public UserDTO getUserDetail(Long id) {
        // 获取用户
        User user = userRepository.findById(id)
                .orElseThrow(() -> new BusinessException("用户不存在"));

        // 转换为DTO
        UserDTO userDTO = new UserDTO();
        BeanUtils.copyProperties(user, userDTO);

        // 设置角色ID
        if (user.getRoles() != null && !user.getRoles().isEmpty()) {
            Set<Long> roleIds = new HashSet<>();
            for (Role role : user.getRoles()) {
                roleIds.add(role.getId());
            }
            userDTO.setRoleIds(roleIds);
        }

        return userDTO;
    }

    @Override
    public com.baomidou.mybatisplus.core.metadata.IPage<UserDTO> getUserPage(Integer page, Integer limit, String keyword, String role) {
        // 创建分页对象
        com.baomidou.mybatisplus.extension.plugins.pagination.Page<User> pageParam = new com.baomidou.mybatisplus.extension.plugins.pagination.Page<>(page, limit);

        // 创建查询条件
        LambdaQueryWrapper<User> queryWrapper = Wrappers.<User>lambdaQuery();

        // 根据角色过滤
        if (role != null && !role.isEmpty()) {
            queryWrapper.exists("SELECT 1 FROM user_roles ur JOIN roles r ON ur.role_id = r.id WHERE ur.user_id = users.id AND r.name = {0}", role);
        }

        // 根据关键字过滤
        if (keyword != null && !keyword.isEmpty()) {
            queryWrapper.and(wrapper -> wrapper
                    .like(User::getUsername, keyword)
                    .or()
                    .like(User::getFullName, keyword)
                    .or()
                    .like(User::getEmail, keyword)
            );
        }

        // 执行查询
        com.baomidou.mybatisplus.extension.plugins.pagination.Page<User> userPage = this.page(pageParam, queryWrapper);

        // 转换为DTO
        com.baomidou.mybatisplus.extension.plugins.pagination.Page<UserDTO> dtoPage = new com.baomidou.mybatisplus.extension.plugins.pagination.Page<>(userPage.getCurrent(), userPage.getSize(), userPage.getTotal());

        List<UserDTO> userDTOList = userPage.getRecords().stream().map(user -> {
            UserDTO dto = new UserDTO();
            BeanUtils.copyProperties(user, dto);

            // 设置角色ID
            if (user.getRoles() != null && !user.getRoles().isEmpty()) {
                Set<Long> roleIds = new HashSet<>();
                for (Role r : user.getRoles()) {
                    roleIds.add(r.getId());
                }
                dto.setRoleIds(roleIds);
            }

            return dto;
        }).collect(java.util.stream.Collectors.toList());

        dtoPage.setRecords(userDTOList);

        return dtoPage;
    }

    @Override
    @Transactional
    public boolean assignUserRoles(Long userId, List<Long> roleIds) {
        // 获取用户
        User user = userRepository.findById(userId)
                .orElseThrow(() -> new BusinessException("用户不存在"));

        // 清空现有角色
        user.getRoles().clear();

        // 添加新角色
        if (roleIds != null && !roleIds.isEmpty()) {
            for (Long roleId : roleIds) {
                Role role = roleRepository.findById(roleId)
                        .orElseThrow(() -> new BusinessException("角色不存在，ID: " + roleId));
                user.getRoles().add(role);
            }
        }

        // 保存用户
        userRepository.save(user);

        return true;
    }

    @Override
    public List<Long> getUserRoleIds(Long userId) {
        // 获取用户
        User user = userRepository.findById(userId)
                .orElseThrow(() -> new BusinessException("用户不存在"));

        // 获取用户角色ID列表
        List<Long> roleIds = new ArrayList<>();
        if (user.getRoles() != null && !user.getRoles().isEmpty()) {
            for (Role role : user.getRoles()) {
                roleIds.add(role.getId());
            }
        }

        return roleIds;
    }

    @Override
    @Transactional
    public String resetPassword(Long id) {
        // 获取用户
        User user = userRepository.findById(id)
                .orElseThrow(() -> new BusinessException("用户不存在"));

        // 生成随机密码
        String newPassword = generateRandomPassword();

        // 更新密码
        user.setPassword(passwordEncoder.encode(newPassword));
        userRepository.save(user);

        return newPassword;
    }

    /**
     * 生成随机密码
     *
     * @return 随机密码
     */
    private String generateRandomPassword() {
        // 定义密码字符集
        String chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*()";
        StringBuilder sb = new StringBuilder();
        java.util.Random random = new java.util.Random();

        // 生成随机8位密码
        for (int i = 0; i < 8; i++) {
            int index = random.nextInt(chars.length());
            sb.append(chars.charAt(index));
        }

        return sb.toString();
    }

    @Override
    @Transactional
    public boolean enableUser(Long id, Boolean enabled) {
        // 获取用户
        User user = userRepository.findById(id)
                .orElseThrow(() -> new BusinessException("用户不存在"));

        // 更新启用状态
        user.setEnabled(enabled);
        userRepository.save(user);

        return true;
    }

    @Override
    @Transactional
    public boolean batchDeleteUser(List<Long> ids) {
        if (ids == null || ids.isEmpty()) {
            return false;
        }

        // 批量删除用户
        for (Long id : ids) {
            // 先检查用户是否存在
            if (userRepository.existsById(id)) {
                userRepository.deleteById(id);
            }
        }

        return true;
    }

    @Override
    @Transactional
    public boolean deleteUser(Long id) {
        // 检查用户是否存在
        if (!userRepository.existsById(id)) {
            throw new BusinessException("用户不存在");
        }

        // 删除用户
        userRepository.deleteById(id);

        return true;
    }

    @Override
    @Transactional
    public boolean updateUser(UserDTO userDTO) {
        if (userDTO == null || userDTO.getId() == null) {
            throw new BusinessException("用户信息不完整");
        }

        // 获取用户
        User user = userRepository.findById(userDTO.getId())
                .orElseThrow(() -> new BusinessException("用户不存在"));

        // 更新用户信息
        if (userDTO.getUsername() != null) {
            // 检查用户名是否已存在
            if (!user.getUsername().equals(userDTO.getUsername()) &&
                    userRepository.existsByUsername(userDTO.getUsername())) {
                throw new BusinessException("用户名已存在");
            }
            user.setUsername(userDTO.getUsername());
        }

        if (userDTO.getPassword() != null && !userDTO.getPassword().isEmpty()) {
            user.setPassword(passwordEncoder.encode(userDTO.getPassword()));
        }

        if (userDTO.getFullName() != null) {
            user.setFullName(userDTO.getFullName());
        }

        if (userDTO.getEmail() != null) {
            // 检查邮箱是否已存在
            if (!user.getEmail().equals(userDTO.getEmail()) &&
                    userRepository.existsByEmail(userDTO.getEmail())) {
                throw new BusinessException("邮箱已存在");
            }
            user.setEmail(userDTO.getEmail());
        }

        if (userDTO.getPhone() != null) {
            user.setPhone(userDTO.getPhone());
        }

        if (userDTO.getEnabled() != null) {
            user.setEnabled(userDTO.getEnabled());
        }

        if (userDTO.getAvatar() != null) {
            user.setAvatar(userDTO.getAvatar());
        }

        if (userDTO.getDescription() != null) {
            user.setDescription(userDTO.getDescription());
        }

        // 更新角色
        if (userDTO.getRoleIds() != null && !userDTO.getRoleIds().isEmpty()) {
            updateUserRoles(userDTO.getId(), userDTO.getRoleIds());
        }

        // 保存用户
        userRepository.save(user);

        return true;
    }

    @Override
    @Transactional
    public Long createUser(UserDTO userDTO) {
        if (userDTO == null || userDTO.getUsername() == null || userDTO.getPassword() == null) {
            throw new BusinessException("用户信息不完整");
        }

        // 检查用户名是否已存在
        if (userRepository.existsByUsername(userDTO.getUsername())) {
            throw new BusinessException("用户名已存在");
        }

        // 检查邮箱是否已存在
        if (userDTO.getEmail() != null && userRepository.existsByEmail(userDTO.getEmail())) {
            throw new BusinessException("邮箱已存在");
        }

        // 创建用户
        User user = new User();
        BeanUtils.copyProperties(userDTO, user);

        // 密码加密
        user.setPassword(passwordEncoder.encode(userDTO.getPassword()));

        // 设置创建和更新时间
        LocalDateTime now = LocalDateTime.now();
        user.setCreatedAt(now);
        user.setUpdatedAt(now);

        // 设置角色
        if (userDTO.getRoleIds() != null && !userDTO.getRoleIds().isEmpty()) {
            Set<Role> roles = new HashSet<>();
            userDTO.getRoleIds().forEach(roleId -> {
                Role role = roleRepository.findById(roleId)
                        .orElseThrow(() -> new BusinessException("角色不存在，ID: " + roleId));
                roles.add(role);
            });
            user.setRoles(roles);
        }

        // 保存用户
        userRepository.save(user);

        return user.getId();
    }

    @Override
    public UserDTO getUserById(Long id) {
        // 获取用户
        User user = userRepository.findById(id)
                .orElseThrow(() -> new BusinessException("用户不存在"));

        // 转换为DTO
        UserDTO userDTO = new UserDTO();
        BeanUtils.copyProperties(user, userDTO);

        // 设置角色ID
        if (user.getRoles() != null && !user.getRoles().isEmpty()) {
            Set<Long> roleIds = new HashSet<>();
            for (Role role : user.getRoles()) {
                roleIds.add(role.getId());
            }
            userDTO.setRoleIds(roleIds);
        }

        return userDTO;
    }

    @Override
    public Page<UserVO> getUserPage(String keyword, Pageable pageable) {
        // 根据关键字查询用户
        Page<User> userPage;
        if (keyword != null && !keyword.isEmpty()) {
            // 使用关键字搜索
            // 使用已定义的查询方法
            userPage = userRepository.findByKeyword(keyword, pageable);
        } else {
            // 不使用关键字，查询所有用户
            userPage = userRepository.findAll(pageable);
        }

        // 转换为VO
        return userPage.map(user -> {
            UserVO userVO = new UserVO();
            BeanUtils.copyProperties(user, userVO);

            // 设置角色信息
            if (user.getRoles() != null && !user.getRoles().isEmpty()) {
                Set<UserVO.RoleVO> roleVOs = new HashSet<>();
                for (Role role : user.getRoles()) {
                    UserVO.RoleVO roleVO = new UserVO.RoleVO();
                    roleVO.setId(role.getId());
                    roleVO.setName(role.getName());
                    roleVOs.add(roleVO);
                }
                userVO.setRoles(roleVOs);
            }

            return userVO;
        });
    }

    @Override
    @Transactional
    public boolean register(RegisterDTO registerDTO) {
        // 检查用户名是否已存在
        if (userRepository.existsByUsername(registerDTO.getUsername())) {
            throw new BusinessException("用户名已存在");
        }

        // 检查邮箱是否已存在
        if (registerDTO.getEmail() != null && !registerDTO.getEmail().isEmpty() &&
                userRepository.existsByEmail(registerDTO.getEmail())) {
            throw new BusinessException("邮箱已存在");
        }

        // 创建用户
        User user = new User();
        user.setUsername(registerDTO.getUsername());
        user.setPassword(passwordEncoder.encode(registerDTO.getPassword()));
        user.setFullName(registerDTO.getName());
        user.setEmail(registerDTO.getEmail());
        user.setPhone(registerDTO.getPhone());
        user.setEnabled(true);

        // 设置创建和更新时间
        LocalDateTime now = LocalDateTime.now();
        user.setCreatedAt(now);
        user.setUpdatedAt(now);

        // 设置默认角色（普通用户）
        Optional<Role> userRole = roleRepository.findByName("USER");
        if (userRole.isPresent()) {
            Set<Role> roles = new HashSet<>();
            roles.add(userRole.get());
            user.setRoles(roles);
        }

        // 保存用户
        userRepository.save(user);

        return true;
    }

    @Override
    public LoginVO login(LoginDTO loginDTO) {
        // 查询用户
        User user = findByUsername(loginDTO.getUsername());

        // 验证密码
        if (!passwordEncoder.matches(loginDTO.getPassword(), user.getPassword())) {
            throw new BusinessException("密码错误");
        }

        // 更新最后登录时间
        updateLastLogin(user.getUsername());

        // 生成token
        String token = jwtUtil.generateToken(user.getId(), user.getUsername());

        // 构建返回结果
        LoginVO loginVO = new LoginVO();
        loginVO.setUserId(user.getId());
        loginVO.setUsername(user.getUsername());
        loginVO.setName(user.getFullName());
        loginVO.setToken(token);

        // 设置角色信息
        if (user.getRoles() != null && !user.getRoles().isEmpty()) {
            List<String> roleNames = new ArrayList<>();
            for (Role role : user.getRoles()) {
                roleNames.add(role.getName());
            }
            loginVO.setRoles(roleNames);

            // 设置首个角色为主角色
            loginVO.setRole(roleNames.get(0));

            // 收集所有权限
            List<String> permissions = new ArrayList<>();
            for (Role role : user.getRoles()) {
                if (role.getPermissions() != null) {
                    for (Permission permission : role.getPermissions()) {
                        permissions.add(permission.getCode());
                    }
                }
            }

            // 去重
            List<String> uniquePermissions = permissions.stream()
                    .distinct()
                    .collect(java.util.stream.Collectors.toList());

            loginVO.setPermissions(uniquePermissions);
        }

        return loginVO;
    }
}