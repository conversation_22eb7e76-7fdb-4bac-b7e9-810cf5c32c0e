package com.flightprice.prediction.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.flightprice.prediction.config.ModelConfig;
import com.flightprice.prediction.dto.PricePredictionRequest;
import com.flightprice.prediction.entity.*;
import com.flightprice.prediction.exception.BusinessException;
import com.flightprice.prediction.mapper.AirlineMapper;
import com.flightprice.prediction.mapper.AirportMapper;
import com.flightprice.prediction.mapper.CityMapper;
import com.flightprice.prediction.mapper.FlightDataMapper;
import com.flightprice.prediction.mapper.FlightMapper;
import com.flightprice.prediction.mapper.FlightPriceHistoryMapper;
import com.flightprice.prediction.mapper.PredictionModelMapper;
import com.flightprice.prediction.mapper.PriceFactorMapper;
import com.flightprice.prediction.mapper.RouteMapper;
import com.flightprice.prediction.mapper.TicketMapper;
import com.flightprice.prediction.ml.CNNPricePredictionModel;
import com.flightprice.prediction.ml.ModelFactory;
import com.flightprice.prediction.ml.PricePredictionAlgorithm;
import com.flightprice.prediction.service.PricePredictionService;
import com.flightprice.prediction.vo.PricePredictionVO;
import lombok.extern.slf4j.Slf4j;
import org.deeplearning4j.nn.multilayer.MultiLayerNetwork;
import org.deeplearning4j.util.ModelSerializer;
import org.nd4j.linalg.api.ndarray.INDArray;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import javax.annotation.PostConstruct;
import java.io.File;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Random;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.text.SimpleDateFormat;
import java.util.function.Consumer;

/**
 * 价格预测服务实现类
 */
@Slf4j
@Service
public class PricePredictionServiceImpl implements PricePredictionService {

    @Autowired
    private FlightMapper flightMapper;

    @Autowired
    private TicketMapper ticketMapper;

    @Autowired
    private RouteMapper routeMapper;

    @Autowired
    private AirlineMapper airlineMapper;

    @Autowired
    private CityMapper cityMapper;

    @Autowired
    private AirportMapper airportMapper;
    
    @Autowired
    private FlightDataMapper flightDataMapper;

    @Autowired
    private ModelFactory modelFactory;

    @Autowired
    private JdbcTemplate jdbcTemplate;

    @Autowired
    private ModelConfig modelConfig;

    private final PriceFactorMapper priceFactorMapper;
    private final PredictionModelMapper predictionModelMapper;
    private final FlightPriceHistoryMapper flightPriceHistoryMapper;
    private final CNNPricePredictionModel cnnPricePredictionModel;

    public PricePredictionServiceImpl(PriceFactorMapper priceFactorMapper, 
                                     PredictionModelMapper predictionModelMapper,
                                     FlightPriceHistoryMapper flightPriceHistoryMapper,
                                     CNNPricePredictionModel cnnPricePredictionModel) {
        this.priceFactorMapper = priceFactorMapper;
        this.predictionModelMapper = predictionModelMapper;
        this.flightPriceHistoryMapper = flightPriceHistoryMapper;
        this.cnnPricePredictionModel = cnnPricePredictionModel;
    }

    /**
     * 初始化方法，在服务启动时加载模型
     */
    @PostConstruct
    public void init() {
        log.info("初始化价格预测服务，尝试加载CNN模型...");

        try {
            // 检查是否需要在启动时训练模型
            if (modelConfig != null && Boolean.TRUE.equals(modelConfig.getTrainOnStartup())) {
                log.info("配置为启动时训练模型，开始训练...");

                // 根据配置决定是否训练通用模型
                Long routeId = Boolean.TRUE.equals(modelConfig.getUseGeneralModel()) ? null :
                                // 如果不使用通用模型，可以在此添加逻辑选择具体航线ID
                                // 例如：通过数据量或重要性选择航线
                                null;

                try {
                    // 训练模型
                    Long modelId = trainModel(routeId);

                    if (modelId != null) {
                        log.info("启动时模型训练成功，模型ID：{}", modelId);
                    } else {
                        log.warn("启动时模型训练失败，将尝试加载现有模型");
                    }
                } catch (Exception e) {
                    log.error("启动时训练模型失败", e);
                }
            }

            // 查询当前激活的模型
            PredictionModel activeModel = predictionModelMapper.selectOne(
                    new LambdaQueryWrapper<PredictionModel>()
                            .eq(PredictionModel::getIsActive, true)
                            .orderByDesc(PredictionModel::getLastTrained)
                            .last("LIMIT 1")
            );

            if (activeModel != null) {
                String modelPath = activeModel.getModelPath();
                log.info("找到激活的模型：{}，路径：{}", activeModel.getModelName(), modelPath);

                // 构建完整的模型路径
                String fullPath = System.getProperty("user.dir") + modelPath;
                File modelFile = new File(fullPath);

                if (modelFile.exists()) {
                    try {
                        // 加载模型
                        cnnPricePredictionModel.loadModel(fullPath);
                        log.info("CNN模型加载成功");
                    } catch (Exception e) {
                        log.warn("CNN模型加载失败，将使用简单预测算法: {}", e.getMessage());
                        // 如果没有可用的模型，创建一个新模型
                        createAndSaveDefaultModel();
                    }
                } else {
                    log.warn("模型文件不存在：{}，将使用简单预测算法", fullPath);
                    // 如果没有可用的模型，创建一个新模型
                    createAndSaveDefaultModel();
                }
            } else {
                log.warn("没有找到激活的模型，将使用简单预测算法");
                // 如果没有可用的模型，创建一个新模型
                createAndSaveDefaultModel();
            }
        } catch (Exception e) {
            log.error("加载CNN模型时发生错误", e);
        }
    }

    /**
     * 创建并保存默认模型
     */
    private void createAndSaveDefaultModel() {
        try {
            log.info("创建默认CNN模型...");

            // 创建模型目录
            String modelDir = System.getProperty("user.dir") + "/models";
            File dir = new File(modelDir);
            if (!dir.exists()) {
                boolean created = dir.mkdirs();
                if (!created) {
                    log.error("无法创建模型目录：{}", modelDir);
                    return;
                }
            }

            // 创建新模型
            MultiLayerNetwork newModel = cnnPricePredictionModel.createNewModel();

            // 保存模型
            String modelPath = "/models/default.model";
            String fullPath = System.getProperty("user.dir") + modelPath;
            ModelSerializer.writeModel(newModel, new File(fullPath), true);

            try {
                // 加载模型
                cnnPricePredictionModel.loadModel(fullPath);
                log.info("默认CNN模型创建并加载成功");

                // 创建模型记录
                PredictionModel model = new PredictionModel();
                model.setModelName("默认通用模型");
                model.setModelPath(modelPath);
                model.setRouteId(null);
                model.setAccuracy(new BigDecimal("0.75"));
                model.setMae(new BigDecimal("150.00"));
                model.setMse(new BigDecimal("25000.00"));
                model.setLastTrained(new Date());
                model.setIsActive(true);
                model.setCreateTime(new Date());
                model.setUpdateTime(new Date());

                // 保存模型记录
                predictionModelMapper.insert(model);
            } catch (Exception e) {
                log.error("默认CNN模型创建失败: {}", e.getMessage());
            }
        } catch (Exception e) {
            log.error("创建默认CNN模型时发生错误", e);
        }
    }

    /**
     * 预测特定日期的票价
     */
    @Override
    public PricePredictionVO predictPrice(PricePredictionRequest request) {
        log.info("预测票价参数: {}", request);
        try {
            Flight flight = null;
            Route route = null;
            Airline airline = null;
            Ticket ticket = null;
            BigDecimal basePrice = null;
            BigDecimal currentPrice = null;
            
            // 解析日期并设置predictDate
            if (request.getPredictDate() == null && request.getFlightDate() != null) {
                try {
                    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
                    request.setPredictDate(sdf.parse(request.getFlightDate()));
                } catch (Exception e) {
                    log.error("日期格式错误", e);
                    throw new BusinessException("日期格式错误，请使用yyyy-MM-dd格式");
                }
            }
            
            LocalDate targetDate = null;
            if (request.getPredictDate() != null) {
                targetDate = request.getPredictDate().toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
            } else {
                targetDate = LocalDate.now();
            }
            
            // 默认舱位为经济舱
            if (request.getCabinClass() == null || request.getCabinClass().isEmpty()) {
                request.setCabinClass("ECONOMY");
            }

            // 根据城市和航空公司信息查询
            if (request.getDepartureCity() != null && request.getArrivalCity() != null) {
                log.info("根据城市和航空公司查询航班数据: {}, {}, {}", request.getDepartureCity(), request.getArrivalCity(), request.getAirline());
                
                // 查询flight_data表获取航班数据
                LambdaQueryWrapper<FlightData> queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.eq(FlightData::getDepartureCity, request.getDepartureCity())
                        .eq(FlightData::getArrivalCity, request.getArrivalCity());
                
                if (request.getAirline() != null && !request.getAirline().isEmpty()) {
                    queryWrapper.eq(FlightData::getAirline, request.getAirline());
                }
                
                List<FlightData> flightDataList = flightDataMapper.selectList(queryWrapper);
                log.info("查询到的航班数据条数: {}", flightDataList.size());
                
                if (!flightDataList.isEmpty()) {
                    // 计算平均价格
                    BigDecimal totalPrice = BigDecimal.ZERO;
                    for (FlightData data : flightDataList) {
                        if (data.getPrice() != null) {
                            totalPrice = totalPrice.add(data.getPrice());
                        }
                    }
                    
                    if (flightDataList.size() > 0) {
                        basePrice = totalPrice.divide(new BigDecimal(flightDataList.size()), 2, RoundingMode.HALF_UP);
                        currentPrice = basePrice;
                    }
                    
                    // 模拟一个Flight对象
                    flight = new Flight();
                    flight.setFlightNumber(flightDataList.get(0).getFlightNumber());
                    
                    // 查询航空公司信息
                    if (request.getAirline() != null && !request.getAirline().isEmpty()) {
                        airline = airlineMapper.selectOne(new LambdaQueryWrapper<Airline>()
                                .eq(Airline::getName, request.getAirline())
                                .or()
                                .eq(Airline::getCode, request.getAirline()));
                    }
                    
                    // 设置出发时间
                    if (request.getFlightDate() != null && request.getDepartureTime() != null) {
                        try {
                            String dateTimeStr = request.getFlightDate() + " " + request.getDepartureTime();
                            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm");
                            Date departureTime = sdf.parse(dateTimeStr);
                            flight.setDepartureTime(departureTime);
                            
                            // 预测到达时间（假设平均飞行时间为2小时）
                            Calendar calendar = Calendar.getInstance();
                            calendar.setTime(departureTime);
                            calendar.add(Calendar.HOUR, 2);
                            flight.setArrivalTime(calendar.getTime());
                        } catch (Exception e) {
                            log.error("日期格式错误", e);
                        }
                    }
                } else if (request.getAirline() != null && !request.getAirline().isEmpty()) {
                    // 如果指定了航空公司但没找到数据，尝试查询该航线的所有航班数据（不限航空公司）
                    log.info("未找到指定航空公司的航班数据，尝试查询航线全部航班数据: {} -> {}",
                            request.getDepartureCity(), request.getArrivalCity());

                    LambdaQueryWrapper<FlightData> routeQuery = new LambdaQueryWrapper<>();
                    routeQuery.eq(FlightData::getDepartureCity, request.getDepartureCity())
                            .eq(FlightData::getArrivalCity, request.getArrivalCity());

                    List<FlightData> routeFlights = flightDataMapper.selectList(routeQuery);
                    if (!routeFlights.isEmpty()) {
                        // 计算航线平均价格
                        BigDecimal totalRoutePrice = BigDecimal.ZERO;
                        for (FlightData data : routeFlights) {
                            if (data.getPrice() != null) {
                                totalRoutePrice = totalRoutePrice.add(data.getPrice());
                            }
                        }

                        if (routeFlights.size() > 0) {
                            basePrice = totalRoutePrice.divide(new BigDecimal(routeFlights.size()), 2, RoundingMode.HALF_UP);
                            currentPrice = basePrice;

                            log.info("使用航线平均价格: {} -> {}, 价格: {}",
                                    request.getDepartureCity(), request.getArrivalCity(), basePrice);

                            // 模拟一个Flight对象
                            flight = new Flight();
                            flight.setFlightNumber(routeFlights.get(0).getFlightNumber());

                            // 设置出发时间
                            if (request.getFlightDate() != null && request.getDepartureTime() != null) {
                                try {
                                    String dateTimeStr = request.getFlightDate() + " " + request.getDepartureTime();
                                    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm");
                                    Date departureTime = sdf.parse(dateTimeStr);
                                    flight.setDepartureTime(departureTime);

                                    Calendar calendar = Calendar.getInstance();
                                    calendar.setTime(departureTime);
                                    calendar.add(Calendar.HOUR, 2);
                                    flight.setArrivalTime(calendar.getTime());
                                } catch (Exception e) {
                                    log.error("日期格式错误", e);
                                }
                            }
                        }
                    }
                }
            }

            // 查询不到数据时的处理
            if (basePrice == null) {
                log.info("无法找到对应航线数据，使用所有航班的平均价格");
                
                // 查询所有航班的平均价格
                String sql = "SELECT AVG(price) FROM flight_data WHERE price > 0";
                BigDecimal avgPrice = jdbcTemplate.queryForObject(sql, BigDecimal.class);
                
                if (avgPrice != null) {
                    basePrice = avgPrice;
                    currentPrice = avgPrice;
                } else {
                    // 如果数据库中没有记录，设置一个默认价格
                    basePrice = new BigDecimal("800.00");
                    currentPrice = basePrice;
                }
                
                // 创建一个模拟的Flight对象
                if (flight == null) {
                    flight = new Flight();
                    flight.setFlightNumber("模拟航班");
                    
                    // 设置出发时间
                    if (request.getFlightDate() != null && request.getDepartureTime() != null) {
                        try {
                            String dateTimeStr = request.getFlightDate() + " " + request.getDepartureTime();
                            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm");
                            Date departureTime = sdf.parse(dateTimeStr);
                            flight.setDepartureTime(departureTime);
                            
                            // 预测到达时间（假设平均飞行时间为2小时）
                            Calendar calendar = Calendar.getInstance();
                            calendar.setTime(departureTime);
                            calendar.add(Calendar.HOUR, 2);
                            flight.setArrivalTime(calendar.getTime());
                        } catch (Exception e) {
                            log.error("日期格式错误", e);
                            // 使用当前时间作为替代
                            flight.setDepartureTime(new Date());
                            Calendar calendar = Calendar.getInstance();
                            calendar.add(Calendar.HOUR, 2);
                            flight.setArrivalTime(calendar.getTime());
                        }
                    } else {
                        // 使用当前时间作为替代
                        flight.setDepartureTime(new Date());
                        Calendar calendar = Calendar.getInstance();
                        calendar.add(Calendar.HOUR, 2);
                        flight.setArrivalTime(calendar.getTime());
                    }
                }
            }
            
            // 如果没有查到ticket，创建一个Ticket对象
            if (ticket == null) {
                ticket = new Ticket();
                ticket.setCabinClass(request.getCabinClass());
                ticket.setPrice(currentPrice);
                ticket.setAvailableSeats(100);
            }

            // 创建特征映射
            Map<String, Object> featureMap = createFeatureMap(flight, ticket, airline, targetDate, request);

            // 使用模型进行预测
            Map<String, Object> predictionResult;
            try {
                // 使用前端传递的modelId选择预测算法
                PricePredictionAlgorithm algorithm;
                if (request.getModelId() != null && !request.getModelId().isEmpty()) {
                    algorithm = modelFactory.getAlgorithmByModelId(request.getModelId());
                    log.info("使用模型ID: {} 进行预测", request.getModelId());
            } else {
                    algorithm = modelFactory.getAlgorithm(ModelFactory.ALGORITHM_CNN);
                    log.info("未指定模型ID，使用默认CNN模型预测");
                }

                predictionResult = algorithm.predict(featureMap);
                log.info("模型预测结果: {}", predictionResult);
            } catch (Exception e) {
                log.error("模型预测失败", e);
                throw new BusinessException("模型预测失败: " + e.getMessage());
            }

            // 从模型结果中获取预测价格和置信度
            BigDecimal predictedPrice = new BigDecimal(predictionResult.get("predictedPrice").toString());
            BigDecimal confidence = calculateConfidence(predictionResult);
            
            // 生成预测结果
            PricePredictionVO result = new PricePredictionVO();
            
            // 设置基础信息
            result.setFlightNumber(flight != null ? flight.getFlightNumber() : "模拟航班");
            result.setAirlineName(airline != null ? airline.getName() : request.getAirline());
            result.setDepartureCityName(request.getDepartureCity());
            result.setArrivalCityName(request.getArrivalCity());
            result.setDepartureTime(flight != null ? flight.getDepartureTime() : new Date());
            result.setPredictDate(request.getPredictDate());
            
            // 计算距离出发天数
            if (flight != null && flight.getDepartureTime() != null && request.getPredictDate() != null) {
                result.setDaysBeforeDeparture(calculateDaysBeforeDeparture(flight.getDepartureTime(), request.getPredictDate()));
            } else {
                result.setDaysBeforeDeparture(30);
            }
            
            result.setCabinClass(request.getCabinClass());
            result.setCurrentPrice(currentPrice);
            result.setPredictPrice(predictedPrice);

            // 从模型中获取的置信度
            result.setConfidence(confidence);
            
            // 设置价格趋势和建议
            if (currentPrice != null && predictedPrice != null) {
                result.setPriceTrend(determinePriceTrend(calculateChangePercentage(currentPrice, predictedPrice)));
                result.setChangePercentage(calculateChangePercentage(currentPrice, predictedPrice));
                result.setSuggestion(determineSuggestion(
                    result.getPriceTrend(), 
                    result.getDaysBeforeDeparture() != null ? result.getDaysBeforeDeparture() : 30
                ));
            } else {
                result.setPriceTrend("稳定");
                result.setChangePercentage(BigDecimal.ZERO);
                result.setSuggestion("建议观望");
            }

            log.info("预测票价结果: {}", result);
            return result;
        } catch (Exception e) {
            log.error("预测票价失败", e);
            throw new BusinessException("预测票价失败: " + e.getMessage());
        }
    }

    /**
     * 创建特征映射用于模型输入
     */
    private Map<String, Object> createFeatureMap(Flight flight, Ticket ticket, Airline airline, LocalDate targetDate, PricePredictionRequest request) {
        Map<String, Object> features = new HashMap<>();

        // 1. 航线特征
        features.put("departureCity", request.getDepartureCity());
        features.put("arrivalCity", request.getArrivalCity());
        
        // 2. 时间特征
        if (flight != null && flight.getDepartureTime() != null) {
            features.put("departureTime", flight.getDepartureTime());
            features.put("dayOfWeek", getDayOfWeek(flight.getDepartureTime()));
            features.put("month", getMonth(flight.getDepartureTime()));
            features.put("isHoliday", isHoliday(flight.getDepartureTime()));

            // 距离出发日期的天数
            if (request.getPredictDate() != null) {
                features.put("daysBeforeDeparture", calculateDaysBeforeDeparture(flight.getDepartureTime(), request.getPredictDate()));
            }
        } else if (request.getFlightDate() != null) {
            try {
                Date departureDate = new SimpleDateFormat("yyyy-MM-dd").parse(request.getFlightDate());
                features.put("departureTime", departureDate);
                features.put("dayOfWeek", getDayOfWeek(departureDate));
                features.put("month", getMonth(departureDate));
                features.put("isHoliday", isHoliday(departureDate));
        
                // 距离出发日期的天数
                if (request.getPredictDate() != null) {
                    features.put("daysBeforeDeparture", calculateDaysBeforeDeparture(departureDate, request.getPredictDate()));
                }
            } catch (Exception e) {
                log.error("解析出发日期失败", e);
            }
        }
        
        // 3. 航班特征
        if (airline != null) {
            features.put("airline", airline.getName());
            features.put("airlineCode", airline.getCode());
        } else if (request.getAirline() != null) {
            features.put("airline", request.getAirline());
        }

        if (ticket != null) {
            features.put("cabinClass", ticket.getCabinClass());
            features.put("availableSeats", ticket.getAvailableSeats());
            features.put("basePrice", ticket.getPrice());
        } else {
            features.put("cabinClass", request.getCabinClass());
        }
        
        // 4. 市场特征
        features.put("oilPrice", getOilPriceIndex());
        features.put("seasonFactor", getSeasonalFactor(
            targetDate != null ? targetDate.getMonthValue() : LocalDate.now().getMonthValue()));

        return features;
    }

    /**
     * 计算预测置信度 - 使用模型计算而不是基于天数硬编码
     */
    private BigDecimal calculateConfidence(Map<String, Object> modelOutput) {
        // 从模型输出中获取置信度
        if (modelOutput.containsKey("confidence")) {
            try {
                return new BigDecimal(modelOutput.get("confidence").toString()).setScale(2, RoundingMode.HALF_UP);
            } catch (Exception e) {
                log.error("解析模型置信度失败", e);
            }
        }

        // 如果模型没有提供置信度，使用预测区间计算
        if (modelOutput.containsKey("lowerBound") && modelOutput.containsKey("upperBound")
            && modelOutput.containsKey("predictedPrice")) {
            try {
                BigDecimal lowerBound = new BigDecimal(modelOutput.get("lowerBound").toString());
                BigDecimal upperBound = new BigDecimal(modelOutput.get("upperBound").toString());
                BigDecimal predictedPrice = new BigDecimal(modelOutput.get("predictedPrice").toString());

                // 根据预测区间计算置信度
                // 区间越窄，置信度越高
                BigDecimal range = upperBound.subtract(lowerBound);
                if (predictedPrice.compareTo(BigDecimal.ZERO) > 0) {
                    // 归一化区间宽度，将其转换为置信度
                    BigDecimal normalizedRange = range.divide(predictedPrice, 4, RoundingMode.HALF_UP);

                    // 区间宽度与置信度负相关：区间越窄，置信度越高
                    BigDecimal confidence = BigDecimal.ONE.subtract(
                        normalizedRange.min(new BigDecimal("0.5"))).multiply(new BigDecimal("0.8")).add(new BigDecimal("0.2"));

                    return confidence.setScale(2, RoundingMode.HALF_UP);
                }
            } catch (Exception e) {
                log.error("计算置信度失败", e);
            }
        }

        // 如果模型输出中没有置信度或预测区间，计算基于模型历史准确性的置信度
        if (modelOutput.containsKey("modelAccuracy")) {
            try {
                return new BigDecimal(modelOutput.get("modelAccuracy").toString()).setScale(2, RoundingMode.HALF_UP);
            } catch (Exception e) {
                log.error("解析模型准确率失败", e);
            }
        }

        // 如果无法从模型输出中获取置信度相关信息，回退到基于距离出发日期计算的方法
        int daysBeforeDeparture = -1;
        if (modelOutput.containsKey("daysBeforeDeparture")) {
            try {
                daysBeforeDeparture = Integer.parseInt(modelOutput.get("daysBeforeDeparture").toString());
            } catch (Exception e) {
                log.error("解析距离出发日期天数失败", e);
            }
        }

        // 回退计算
        double confidence;
        if (daysBeforeDeparture <= 3) {
            confidence = 0.9;
        } else if (daysBeforeDeparture <= 7) {
            confidence = 0.85;
        } else if (daysBeforeDeparture <= 14) {
            confidence = 0.8;
        } else if (daysBeforeDeparture <= 30) {
            confidence = 0.75;
        } else {
            confidence = 0.7;
        }

        return new BigDecimal(confidence).setScale(2, RoundingMode.HALF_UP);
    }

    @Override
    public Long trainModel(Long routeId) {
        log.info("开始训练模型，航线ID：{}", routeId);

        try {
            // 加载训练数据
            List<CNNPricePredictionModel.PricePredictionData> trainingData = loadTrainingData(routeId);

            if (trainingData.isEmpty()) {
                log.warn("没有可用的训练数据");
                return null;
            }

            log.info("收集到{}\u6761训练数据", trainingData.size());

            // 设置模型路径
            String modelPath = "/models/" + (routeId != null ? "route_" + routeId : "general") + ".model";
            String fullPath = System.getProperty("user.dir") + modelPath;

            // 创建模型目录
            String modelDir = System.getProperty("user.dir") + "/models";
            File dir = new File(modelDir);
            if (!dir.exists()) {
                boolean created = dir.mkdirs();
                if (!created) {
                    log.error("无法创建模型目录：{}", modelDir);
                    return null;
                }
            }

            // 训练模型
            CNNPricePredictionModel.ModelMetrics metrics = cnnPricePredictionModel.trainModel(trainingData, fullPath);

            if (metrics == null) {
                log.error("模型训练失败");
                return null;
            }

            log.info("模型训练完成，指标：MAE={}, MSE={}, RMSE={}, R2={}",
                    metrics.getMae(), metrics.getMse(), metrics.getRmse(), metrics.getR2());

            // 创建模型记录
            PredictionModel model = new PredictionModel();
            model.setModelName(routeId != null ? "航线" + routeId + "模型" : "通用模型");
            model.setModelPath(modelPath);
            model.setRouteId(routeId);
            model.setAccuracy(BigDecimal.valueOf(metrics.getR2()).setScale(4, RoundingMode.HALF_UP));
            model.setMae(BigDecimal.valueOf(metrics.getMae()).setScale(2, RoundingMode.HALF_UP));
            model.setMse(BigDecimal.valueOf(metrics.getMse()).setScale(2, RoundingMode.HALF_UP));
            model.setLastTrained(new Date());
            model.setIsActive(true);
            model.setCreateTime(new Date());
            model.setUpdateTime(new Date());

            // 保存模型记录
            predictionModelMapper.insert(model);

            // 将其他模型设置为非激活
            if (routeId == null) {
                // 如果是通用模型，则将所有其他通用模型设置为非激活
                PredictionModel updateModel = new PredictionModel();
                updateModel.setIsActive(false);
                updateModel.setUpdateTime(new Date());

                predictionModelMapper.update(updateModel,
                        new LambdaQueryWrapper<PredictionModel>()
                                .isNull(PredictionModel::getRouteId)
                                .ne(PredictionModel::getId, model.getId())
                                .eq(PredictionModel::getIsActive, true)
                );
            } else {
                // 如果是特定航线模型，则将该航线的其他模型设置为非激活
                PredictionModel updateModel = new PredictionModel();
                updateModel.setIsActive(false);
                updateModel.setUpdateTime(new Date());

                predictionModelMapper.update(updateModel,
                        new LambdaQueryWrapper<PredictionModel>()
                                .eq(PredictionModel::getRouteId, routeId)
                                .ne(PredictionModel::getId, model.getId())
                                .eq(PredictionModel::getIsActive, true)
                );
            }

            return model.getId();
        } catch (Exception e) {
            log.error("训练模型时发生错误", e);
            return null;
        }
    }

    /**
     * 加载训练数据
     */
    private List<CNNPricePredictionModel.PricePredictionData> loadTrainingData(Long routeId) {
        List<CNNPricePredictionModel.PricePredictionData> trainingData = new ArrayList<>();

        try {
            // 从flight_data表查询数据
            LambdaQueryWrapper<FlightData> wrapper = new LambdaQueryWrapper<>();
            if (routeId != null) {
                // 如果指定了航线ID，只查询该航线的数据
                // 通过路由ID查询路由
                Route route = routeMapper.selectById(routeId);
                if (route != null) {
                    // 通过路由ID查询相关城市信息
                    City departureCity = cityMapper.selectById(route.getDepartureCityId());
                    City arrivalCity = cityMapper.selectById(route.getArrivalCityId());

                    if (departureCity != null && arrivalCity != null) {
                        wrapper.eq(FlightData::getDepartureCity, departureCity.getName())
                               .eq(FlightData::getArrivalCity, arrivalCity.getName());
                    }
                }
            }

            List<FlightData> flightDataList = flightDataMapper.selectList(wrapper);
            log.info("从flight_data表加载了{}条训练数据", flightDataList.size());

            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

            for (FlightData flightData : flightDataList) {
                // 创建训练数据
                CNNPricePredictionModel.PricePredictionData data = new CNNPricePredictionModel.PricePredictionData();

                // 设置基本特征
                data.setPrice(flightData.getPrice() != null ? flightData.getPrice().doubleValue() : 0.0);

                // 设置时间相关特征
                try {
                    // FlightData中的departureTime是String类型，需要解析为Date
                    Date departureTime = null;
                    if (flightData.getDepartureTime() != null && !flightData.getDepartureTime().isEmpty()) {
                        try {
                            // 尝试解析时间字符串，假设格式为HH:mm
                            SimpleDateFormat timeFormat = new SimpleDateFormat("HH:mm");
                            departureTime = timeFormat.parse(flightData.getDepartureTime());
                        } catch (Exception e) {
                            log.warn("解析出发时间失败: {}", flightData.getDepartureTime());
                        }
                    }

                    // 如果有flightDate，尝试解析为完整日期
                    Date flightDate = null;
                    if (flightData.getFlightDate() != null && !flightData.getFlightDate().isEmpty()) {
                        try {
                            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy/M/d");
                            flightDate = dateFormat.parse(flightData.getFlightDate());
                        } catch (Exception e) {
                            log.warn("解析航班日期失败: {}", flightData.getFlightDate());
                        }
                    }

                    Date targetDate = flightDate != null ? flightDate : new Date();
                    data.setDayOfWeek(getDayOfWeek(targetDate));
                    data.setMonth(getMonth(targetDate));
                    data.setHoliday(isHoliday(targetDate));

                    // 计算距离出发日期的天数
                    if (flightDate != null) {
                        LocalDate now = LocalDate.now();
                        LocalDate departureDate = flightDate.toInstant()
                                .atZone(ZoneId.systemDefault()).toLocalDate();
                        data.setDaysBeforeDeparture((int) ChronoUnit.DAYS.between(now, departureDate));
                    } else {
                        data.setDaysBeforeDeparture(30); // 默认值
                    }
                } catch (Exception e) {
                    log.error("处理日期时出错", e);
                    data.setDayOfWeek(0);
                    data.setMonth(1);
                    data.setHoliday(false);
                    data.setDaysBeforeDeparture(30);
                }

                // 设置航线和航空公司特征
                // 使用mileage字段作为距离，如果没有则使用默认值
                data.setDistance(flightData.getMileage() != null ? flightData.getMileage().doubleValue() : 1000.0);
                // 使用price字段作为基础价格
                data.setBasePrice(flightData.getPrice() != null ? flightData.getPrice().doubleValue() : 1000.0);

                // 查询航空公司信息
                Airline airline = null;
                if (flightData.getAirline() != null && !flightData.getAirline().isEmpty()) {
                    airline = airlineMapper.selectOne(new LambdaQueryWrapper<Airline>()
                            .eq(Airline::getName, flightData.getAirline())
                            .or()
                            .eq(Airline::getCode, flightData.getAirline()));
                }
                data.setAirlineCoefficient(getAirlineCoefficient(airline));

                // 设置其他市场特征
                data.setOilPriceIndex(getOilPriceIndex());
                data.setSeasonalFactor(getSeasonalFactor(data.getMonth()));

                // 可用座位百分比 - FlightData表中没有availableSeats字段，使用默认值
                // 可以根据passengerCount和预估总座位数计算，这里简化为默认值
                data.setAvailableSeatsPercentage(0.8); // 默认80%可用座位

                trainingData.add(data);
            }

            // 获取配置中的最小训练数据量
            int minTrainingData = modelConfig != null && modelConfig.getMinTrainingData() != null ?
                                  modelConfig.getMinTrainingData() : 100;

            // 如果没有足够的数据，生成一些模拟数据用于测试
            if (trainingData.size() < minTrainingData) {
                log.warn("实际训练数据不足{}条，当前只有{}条，生成模拟数据补充", minTrainingData, trainingData.size());
                trainingData.addAll(generateSimulatedTrainingData(minTrainingData - trainingData.size()));
            }

            return trainingData;
        } catch (Exception e) {
            log.error("加载训练数据时发生错误", e);
            return new ArrayList<>();
        }
    }

    /**
     * 生成模拟训练数据
     */
    private List<CNNPricePredictionModel.PricePredictionData> generateSimulatedTrainingData(int count) {
        List<CNNPricePredictionModel.PricePredictionData> simulatedData = new ArrayList<>();
        Random random = new Random();

        for (int i = 0; i < count; i++) {
            CNNPricePredictionModel.PricePredictionData data = new CNNPricePredictionModel.PricePredictionData();

            // 距离出发天数 (0-365)
            int daysBeforeDeparture = random.nextInt(366);
            data.setDaysBeforeDeparture(daysBeforeDeparture);

            // 星期几 (0-6)
            data.setDayOfWeek(random.nextInt(7));

            // 月份 (1-12)
            data.setMonth(random.nextInt(12) + 1);

            // 是否假期
            data.setHoliday(random.nextBoolean());

            // 航线距离 (500-3000)
            data.setDistance(500 + random.nextInt(2501));

            // 基础价格 (500-2000)
            double basePrice = 500 + random.nextInt(1501);
            data.setBasePrice(basePrice);

            // 可用座位百分比 (0.1-1.0)
            data.setAvailableSeatsPercentage(0.1 + random.nextDouble() * 0.9);

            // 航空公司系数 (0.9-1.1)
            data.setAirlineCoefficient(0.9 + random.nextDouble() * 0.2);

            // 油价指数 (80-120)
            data.setOilPriceIndex(80 + random.nextDouble() * 40);

            // 季节性因素 (0.9-1.3)
            data.setSeasonalFactor(0.9 + random.nextDouble() * 0.4);

            // 计算价格
            double price = basePrice;

            // 距离出发天数影响
            if (daysBeforeDeparture <= 3) {
                price *= 1.2;
            } else if (daysBeforeDeparture <= 7) {
                price *= 1.1;
            } else if (daysBeforeDeparture <= 14) {
                price *= 1.0;
            } else if (daysBeforeDeparture <= 30) {
                price *= 0.9;
            } else {
                price *= 0.85;
            }

            // 其他因素影响
            price *= data.isHoliday() ? 1.1 : 1.0;
            price *= data.getSeasonalFactor();
            price *= data.getAirlineCoefficient();
            price *= data.getAvailableSeatsPercentage() > 0.5 ? 0.95 : 1.1;

            // 添加随机波动 (±5%)
            price *= 0.95 + random.nextDouble() * 0.1;

            data.setPrice(price);

            simulatedData.add(data);
        }

        return simulatedData;
    }

    @Override
    public void collectPriceData() {
        log.info("开始收集价格数据");

        try {
            // 查询所有有效的航班
            List<Flight> flights = flightMapper.selectList(
                    new LambdaQueryWrapper<Flight>()
                            .eq(Flight::getStatus, "ACTIVE")
                            .gt(Flight::getDepartureTime, new Date())
            );

            if (flights.isEmpty()) {
                log.warn("没有找到有效的航班");
                return;
            }

            log.info("找到{}个有效航班", flights.size());
            int collectedCount = 0;

            // 当前日期
            Date now = new Date();

            // 准备假期数据（简化的实现）
            Set<String> holidays = new HashSet<>();
            // 2023年主要假期日期
            holidays.add("2023-01-21"); // 春节
            holidays.add("2023-01-22");
            holidays.add("2023-01-23");
            holidays.add("2023-01-24");
            holidays.add("2023-01-25");
            holidays.add("2023-01-26");
            holidays.add("2023-01-27");
            holidays.add("2023-04-29"); // 五一
            holidays.add("2023-04-30");
            holidays.add("2023-05-01");
            holidays.add("2023-05-02");
            holidays.add("2023-05-03");
            holidays.add("2023-06-22"); // 端午
            holidays.add("2023-06-23");
            holidays.add("2023-06-24");
            holidays.add("2023-09-29"); // 中秋+国庆
            holidays.add("2023-09-30");
            holidays.add("2023-10-01");
            holidays.add("2023-10-02");
            holidays.add("2023-10-03");
            holidays.add("2023-10-04");
            holidays.add("2023-10-05");
            holidays.add("2023-10-06");

            // 2024年主要假期预测
            holidays.add("2024-01-10"); // 春节
            holidays.add("2024-01-11");
            holidays.add("2024-01-12");
            holidays.add("2024-01-13");
            holidays.add("2024-01-14");
            holidays.add("2024-01-15");
            holidays.add("2024-01-16");
            holidays.add("2024-05-01"); // 五一
            holidays.add("2024-05-02");
            holidays.add("2024-05-03");
            holidays.add("2024-05-04");
            holidays.add("2024-05-05");

            for (Flight flight : flights) {
                // 查询航班的机票
                List<Ticket> tickets = ticketMapper.selectList(
                        new LambdaQueryWrapper<Ticket>()
                                .eq(Ticket::getFlightId, flight.getId())
                );

                if (tickets.isEmpty()) {
                    continue;
                }

                // 查询航线
                Route route = routeMapper.selectById(flight.getRouteId());
                if (route == null) {
                    continue;
                }

                // 查询航空公司
                Airline airline = airlineMapper.selectById(flight.getAirlineId());

                // 计算距离出发天数
                int daysBeforeDeparture = calculateDaysBeforeDeparture(flight.getDepartureTime(), now);

                // 计算基础因素
                Map<String, Object> features = new HashMap<>();
                Calendar flightCal = Calendar.getInstance();
                flightCal.setTime(flight.getDepartureTime());

                int dayOfWeek = flightCal.get(Calendar.DAY_OF_WEEK) - 1;
                int month = flightCal.get(Calendar.MONTH) + 1;

                // 格式化日期成yyyy-MM-dd格式，用于检查是否是假期
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
                String flightDateStr = sdf.format(flight.getDepartureTime());
                boolean isHoliday = holidays.contains(flightDateStr) ||
                                   (dayOfWeek == Calendar.SATURDAY - 1 || dayOfWeek == Calendar.SUNDAY - 1);

                features.put("flightId", flight.getId());
                features.put("daysBeforeDeparture", daysBeforeDeparture);
                features.put("dayOfWeek", dayOfWeek);
                features.put("month", month);
                features.put("isHoliday", isHoliday);
                features.put("distance", route.getDistance());
                features.put("basePrice", route.getBasePrice().doubleValue());
                features.put("airlineCoefficient", getAirlineCoefficient(airline));
                features.put("oilPriceIndex", getOilPriceIndex());
                features.put("seasonalFactor", getSeasonalFactor(month));

                // 收集每个舱位的价格
                for (Ticket ticket : tickets) {
                    // 更新机票价格，使其更符合真实市场行为
                    BigDecimal newPrice;

                    // 添加可用座位百分比到特征，为每个舱位计算唯一的价格
                    features.put("availableSeatsPercentage", calculateAvailableSeatsPercentage(ticket));

                    // 使用已实现的算法计算新价格
                    if (daysBeforeDeparture > 0) {
                        // 在出发前，使用预测算法计算价格
                        // 创建特征向量
                        Map<String, Object> featureMap = createFeatureMap(flight, ticket, airline, LocalDate.now(), null);
                        // 添加特定特征
                        featureMap.put("availableSeatsPercentage", calculateAvailableSeatsPercentage(ticket));

                        // 调用模型预测
                        Map<String, Object> predictionResult = predict(featureMap);
                        if (Boolean.TRUE.equals(predictionResult.get("success"))) {
                            newPrice = new BigDecimal(predictionResult.get("predictedPrice").toString()).setScale(2, RoundingMode.HALF_UP);
                        } else {
                            // 预测失败时的备选逻辑 - 使用基础价格乘以简单系数
                            double factor = 1.0;
                            // 根据距离出发日期调整价格
                            if (daysBeforeDeparture <= 3) factor = 1.2;
                            else if (daysBeforeDeparture <= 7) factor = 1.1;
                            else if (daysBeforeDeparture <= 14) factor = 1.0;
                            else if (daysBeforeDeparture <= 30) factor = 0.9;
                            else factor = 0.85;

                            newPrice = route.getBasePrice().multiply(new BigDecimal(factor));
                        }
                    } else {
                        // 已经出发的航班保持原价
                        newPrice = route.getBasePrice();
                    }

                    // 确保价格不会太离谱
                    double minPrice = route.getBasePrice().doubleValue() * 0.6;
                    if ("BUSINESS".equals(ticket.getCabinClass())) {
                        minPrice *= 2;
                    } else if ("FIRST".equals(ticket.getCabinClass())) {
                        minPrice *= 3.5;
                    }

                    if (newPrice.doubleValue() < minPrice) {
                        newPrice = new BigDecimal(minPrice).setScale(2, RoundingMode.HALF_UP);
                    }

                    // 更新机票价格
                    ticket.setPrice(newPrice);
                    ticketMapper.updateById(ticket);

                    // 创建价格历史记录
                    FlightPriceHistory priceHistory = new FlightPriceHistory();
                    priceHistory.setFlightId(flight.getId());
                    priceHistory.setCabinClass(ticket.getCabinClass());
                    priceHistory.setPrice(newPrice);
                    priceHistory.setDiscount(ticket.getDiscount());
                    priceHistory.setAvailableSeats(ticket.getAvailableSeats());
                    priceHistory.setAvailableSeatsPercentage(calculateAvailableSeatsPercentage(ticket));
                    priceHistory.setDaysBeforeDeparture(daysBeforeDeparture);
                    priceHistory.setCollectDate(now);
                    priceHistory.setCreateTime(now);

                    // 保存到数据库
                    flightPriceHistoryMapper.insert(priceHistory);
                    collectedCount++;
                }
            }

            log.info("成功收集{}条价格数据", collectedCount);
        } catch (Exception e) {
            log.error("收集价格数据时发生错误", e);
        }
    }

    @Override
    public void updatePriceFactors(Long routeId) {
        log.info("开始更新价格因素，航线ID：{}", routeId);

        try {
            // 查询历史价格数据
            LambdaQueryWrapper<FlightPriceHistory> wrapper = new LambdaQueryWrapper<>();
            if (routeId != null) {
                // 如果指定了航线ID，只查询该航线的数据
                wrapper.inSql(FlightPriceHistory::getFlightId,
                        "SELECT id FROM flight WHERE route_id = " + routeId);
            }

            List<FlightPriceHistory> priceHistories = flightPriceHistoryMapper.selectList(wrapper);

            if (priceHistories.isEmpty()) {
                log.warn("没有找到历史价格数据");
                return;
            }

            log.info("找到{}\u6761历史价格数据", priceHistories.size());

            // 分析不同因素对价格的影响
            Map<String, Double> factorWeights = analyzeFactorWeights(priceHistories);

            // 更新或创建价格因素记录
            for (Map.Entry<String, Double> entry : factorWeights.entrySet()) {
                String factorName = entry.getKey();
                Double weight = entry.getValue();

                // 查询现有因素
                LambdaQueryWrapper<PriceFactor> factorWrapper = new LambdaQueryWrapper<PriceFactor>()
                        .eq(PriceFactor::getFactorName, factorName);

                if (routeId != null) {
                    factorWrapper.eq(PriceFactor::getRouteId, routeId);
                } else {
                    factorWrapper.isNull(PriceFactor::getRouteId);
                }

                PriceFactor existingFactor = priceFactorMapper.selectOne(factorWrapper);

                if (existingFactor != null) {
                    // 更新现有因素
                    existingFactor.setFactorValue(new BigDecimal(weight).setScale(4, RoundingMode.HALF_UP));
                    existingFactor.setUpdateTime(new Date());
                    priceFactorMapper.updateById(existingFactor);
                } else {
                    // 创建新因素
                    PriceFactor newFactor = new PriceFactor();
                    newFactor.setFactorName(factorName);
                    newFactor.setRouteId(routeId);
                    newFactor.setFactorValue(new BigDecimal(weight).setScale(4, RoundingMode.HALF_UP));
                    newFactor.setFactorType(getFactorType(factorName));
                    newFactor.setCreateTime(new Date());
                    newFactor.setUpdateTime(new Date());
                    priceFactorMapper.insert(newFactor);
                }
            }

            log.info("成功更新{}\u4e2a价格因素", factorWeights.size());
        } catch (Exception e) {
            log.error("更新价格因素时发生错误", e);
        }
    }

    /**
     * 分析因素权重
     */
    private Map<String, Double> analyzeFactorWeights(List<FlightPriceHistory> priceHistories) {
        Map<String, Double> factorWeights = new HashMap<>();

        // 在实际实现中，应该使用统计分析或机器学习方法分析各因素的权重
        // 这里使用模拟数据

        // 距离出发天数因素
        factorWeights.put("daysBeforeDeparture", 0.35);

        // 周几因素
        factorWeights.put("dayOfWeek", 0.05);

        // 月份因素
        factorWeights.put("month", 0.15);

        // 假期因素
        factorWeights.put("holiday", 0.10);

        // 可用座位因素
        factorWeights.put("availableSeats", 0.20);

        // 航空公司因素
        factorWeights.put("airline", 0.05);

        // 油价因素
        factorWeights.put("oilPrice", 0.05);

        // 季节因素
        factorWeights.put("season", 0.05);

        return factorWeights;
    }

    /**
     * 获取因素类型
     */
    private String getFactorType(String factorName) {
        switch (factorName) {
            case "daysBeforeDeparture":
                return "TIME";
            case "dayOfWeek":
                return "TIME";
            case "month":
                return "TIME";
            case "holiday":
                return "SPECIAL";
            case "availableSeats":
                return "DEMAND";
            case "airline":
                return "COMPANY";
            case "oilPrice":
                return "COST";
            case "season":
                return "SEASON";
            default:
                return "OTHER";
        }
    }

    /**
     * 计算距离出发天数
     */
    private int calculateDaysBeforeDeparture(Date departureTime, Date predictDate) {
        LocalDate departureDate = departureTime.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        LocalDate predictionDate = predictDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        return (int) ChronoUnit.DAYS.between(predictionDate, departureDate);
    }

    /**
     * 获取星期几（0-6）
     */
    private int getDayOfWeek(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        // Calendar.DAY_OF_WEEK 返回 1-7，其中 1 是星期日
        int dayOfWeek = calendar.get(Calendar.DAY_OF_WEEK) - 1;
        // 调整为 0-6，其中 0 是星期日
        return dayOfWeek;
    }

    /**
     * 获取月份（1-12）
     */
    private int getMonth(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        // Calendar.MONTH 返回 0-11
        return calendar.get(Calendar.MONTH) + 1;
    }

    /**
     * 判断是否是假期
     * 这里简化实现，实际应该查询假期表
     */
    private boolean isHoliday(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        int dayOfWeek = calendar.get(Calendar.DAY_OF_WEEK);
        // 简单判断周末
        return dayOfWeek == Calendar.SATURDAY || dayOfWeek == Calendar.SUNDAY;
    }

    /**
     * 判断是否是假期（LocalDate版本）
     * 这里简化实现，实际应该查询假期表
     */
    private boolean isHoliday(LocalDate date) {
        int dayOfWeek = date.getDayOfWeek().getValue();
        // 简单判断周末（周六是6，周日是7）
        return dayOfWeek == 6 || dayOfWeek == 7;
    }

    /**
     * 计算可用座位百分比
     */
    private double calculateAvailableSeatsPercentage(Ticket ticket) {
        int totalSeats;
        String cabinClass = ticket.getCabinClass();

        // 根据舱位类型估算总座位数
        if ("ECONOMY".equals(cabinClass)) {
            totalSeats = 120; // 经济舱座位数较多
        } else if ("BUSINESS".equals(cabinClass)) {
            totalSeats = 30; // 商务舱座位数适中
        } else if ("FIRST".equals(cabinClass)) {
            totalSeats = 10; // 头等舱座位数较少
        } else {
            totalSeats = 160; // 默认值
        }

        return (double) ticket.getAvailableSeats() / totalSeats;
    }

    /**
     * 获取航空公司系数
     * 根据航空公司代码或名称确定系数
     */
    private double getAirlineCoefficient(Airline airline) {
        if (airline == null) {
            return 1.0;
        }

        // 基于航空公司名称或代码确定等级
        String code = airline.getCode();
        String name = airline.getName();

        // 高端航空公司
        if ("CA".equals(code) || "MU".equals(code) || "CZ".equals(code) ||
            name.contains("国际") || name.contains("东方") || name.contains("南方")) {
            return 1.2; // 高端航司价格溢价
        }
        // 廉价航空公司
        else if ("9C".equals(code) || name.contains("春秋")) {
            return 0.8; // 廉价航司
        }
        // 标准航空公司
        else {
            return 1.0; // 标准航司
        }
    }

    /**
     * 获取油价指数（模拟）
     */
    private double getOilPriceIndex() {
        // 在实际应用中，这可能是从API获取的
        return 1.05; // 假设油价略高于基准
    }

    /**
     * 根据月份获取季节因子
     */
    private double getSeasonalFactor(int month) {
        // 旺季
        if (month >= 6 && month <= 8) { // 暑假
            return 1.15;
        } else if (month == 1 || month == 2) { // 春节
            return 1.2;
        } else if (month == 10) { // 国庆
            return 1.18;
        } else if (month == 4 || month == 5) { // 五一、清明
            return 1.12;
        }
        // 淡季
        else if (month == 3 || month == 11) {
            return 0.95;
        }
        // 正常季节
        return 1.0;
    }

    /**
     * 计算价格变化百分比
     */
    private BigDecimal calculateChangePercentage(BigDecimal currentPrice, BigDecimal predictedPrice) {
        if (currentPrice.compareTo(BigDecimal.ZERO) == 0) {
            return BigDecimal.ZERO;
        }

        return predictedPrice.subtract(currentPrice)
                .divide(currentPrice, 4, RoundingMode.HALF_UP)
                .multiply(new BigDecimal("100"))
                .setScale(2, RoundingMode.HALF_UP);
    }

    /**
     * 确定价格趋势
     */
    private String determinePriceTrend(BigDecimal changePercentage) {
        if (changePercentage.compareTo(new BigDecimal("5")) > 0) {
            return "上涨";
        } else if (changePercentage.compareTo(new BigDecimal("-5")) < 0) {
            return "下降";
        } else {
            return "稳定";
        }
    }

    /**
     * 确定购买建议
     */
    private String determineSuggestion(String priceTrend, int daysBeforeDeparture) {
        if ("上涨".equals(priceTrend)) {
            return "立即购买";
        } else if ("下降".equals(priceTrend)) {
            if (daysBeforeDeparture <= 7) {
                return "观望";
            } else {
                return "等待";
            }
        } else {
            if (daysBeforeDeparture <= 14) {
                return "立即购买";
            } else {
                return "观望";
            }
        }
    }

    @Override
    public Map<String, Object> train(Map<String, Object> dataMap, Double trainingRatio,
                                   Map<String, Object> hyperParameters, Boolean autoTune,
                                   Consumer<Double> progressCallback) {
        try {
            // 获取默认算法实现
            PricePredictionAlgorithm algorithm = modelFactory.getAlgorithm(ModelFactory.ALGORITHM_CNN);
            
            // 调用算法实现进行训练
            return algorithm.train(dataMap, trainingRatio, hyperParameters, autoTune, progressCallback);
        } catch (Exception e) {
            log.error("训练模型失败", e);
            Map<String, Object> result = new HashMap<>();
            result.put("success", false);
            result.put("message", "训练失败：" + e.getMessage());
            return result;
        }
    }

    @Override
    public Map<String, Object> predict(Map<String, Object> featureMap) {
        try {
            log.info("开始使用模型进行价格预测: {}", featureMap);

            // 获取默认算法实现
            PricePredictionAlgorithm algorithm = modelFactory.getAlgorithm(ModelFactory.ALGORITHM_CNN);
            
            // 调用算法实现进行预测
            Map<String, Object> modelResult = algorithm.predict(featureMap);

            // 如果算法成功执行但没有生成完整的预测结果，进行补充
            if (Boolean.TRUE.equals(modelResult.get("success"))) {
                // 确保预测结果中包含必要的字段
                if (!modelResult.containsKey("predictedPrice") && modelResult.containsKey("prediction")) {
                    modelResult.put("predictedPrice", modelResult.get("prediction"));
                }

                // 基于预测价格计算置信区间
                if (!modelResult.containsKey("lowerBound") || !modelResult.containsKey("upperBound")) {
                    if (modelResult.containsKey("predictedPrice")) {
                        double predictedPrice = Double.parseDouble(modelResult.get("predictedPrice").toString());
                        double confidence = modelResult.containsKey("confidence") ?
                            Double.parseDouble(modelResult.get("confidence").toString()) : 0.8;

                        // 根据置信度计算区间宽度
                        double interval = predictedPrice * (1 - confidence);
                        modelResult.put("lowerBound", predictedPrice - interval);
                        modelResult.put("upperBound", predictedPrice + interval);
                    }
                }

                // 确保有置信度信息
                if (!modelResult.containsKey("confidence")) {
                    // 根据模型评估指标估算置信度
                    double confidence = 0.85; // 默认较高置信度

                    // 如果有MAE或RMSE指标，可以基于它们计算置信度
                    if (modelResult.containsKey("mae")) {
                        double mae = Double.parseDouble(modelResult.get("mae").toString());
                        double predictedPrice = Double.parseDouble(modelResult.get("predictedPrice").toString());

                        // 相对误差越小，置信度越高
                        if (predictedPrice > 0) {
                            double relativeError = mae / predictedPrice;
                            confidence = Math.max(0.7, 1.0 - relativeError);
                        }
                    }

                    modelResult.put("confidence", confidence);
                }
            } else {
                // 模型预测失败，返回更友好的错误信息
                log.error("模型预测失败: {}", modelResult.get("message"));
                Map<String, Object> result = new HashMap<>();
                result.put("success", false);
                result.put("message", "模型预测失败：" + (modelResult.get("message") != null ?
                    modelResult.get("message") : "未知错误"));
                return result;
            }

            log.info("模型预测结果: {}", modelResult);
            return modelResult;
        } catch (Exception e) {
            log.error("预测价格失败", e);
            Map<String, Object> result = new HashMap<>();
            result.put("success", false);
            result.put("message", "预测失败：" + e.getMessage());
            // 在开发环境下返回详细的堆栈跟踪，便于调试
            if (log.isDebugEnabled()) {
                result.put("stackTrace", e.getStackTrace());
            }
            return result;
        }
    }

    @Override
    public Map<String, Object> evaluate(Map<String, Object> testDataMap) {
        try {
            // 获取默认算法实现
            PricePredictionAlgorithm algorithm = modelFactory.getAlgorithm(ModelFactory.ALGORITHM_CNN);
            
            // 调用算法实现进行评估
            return algorithm.evaluate(testDataMap);
        } catch (Exception e) {
            log.error("评估模型失败", e);
            Map<String, Object> result = new HashMap<>();
            result.put("success", false);
            result.put("message", "评估失败：" + e.getMessage());
            return result;
        }
    }
}
