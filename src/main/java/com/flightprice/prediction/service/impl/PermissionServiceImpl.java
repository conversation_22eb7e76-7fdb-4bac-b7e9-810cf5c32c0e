package com.flightprice.prediction.service.impl;

import com.flightprice.prediction.dto.PermissionDTO;
import com.flightprice.prediction.entity.Permission;
import com.flightprice.prediction.repository.PermissionRepository;
import com.flightprice.prediction.service.PermissionService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import javax.persistence.criteria.Predicate;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 权限服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class PermissionServiceImpl implements PermissionService {

    private final PermissionRepository permissionRepository;

    @Override
    public Page<PermissionDTO> getPermissionPage(String keyword, String type, Pageable pageable) {
        Specification<Permission> specification = (root, query, criteriaBuilder) -> {
            List<Predicate> predicates = new ArrayList<>();
            
            if (StringUtils.hasText(keyword)) {
                Predicate namePredicate = criteriaBuilder.like(root.get("name"), "%" + keyword + "%");
                Predicate codePredicate = criteriaBuilder.like(root.get("code"), "%" + keyword + "%");
                Predicate descriptionPredicate = criteriaBuilder.like(root.get("description"), "%" + keyword + "%");
                predicates.add(criteriaBuilder.or(namePredicate, codePredicate, descriptionPredicate));
            }
            
            if (StringUtils.hasText(type)) {
                predicates.add(criteriaBuilder.equal(root.get("category"), type));
            }
            
            return criteriaBuilder.and(predicates.toArray(new Predicate[0]));
        };
        
        Page<Permission> permissionPage = permissionRepository.findAll(specification, pageable);
        return permissionPage.map(this::convertToDTO);
    }

    @Override
    public List<PermissionDTO> getAllPermissions() {
        List<Permission> permissions = permissionRepository.findAll();
        return permissions.stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
    }

    @Override
    public List<PermissionDTO> getPermissionTree() {
        List<Permission> allPermissions = permissionRepository.findAll();
        List<PermissionDTO> allPermissionDTOs = allPermissions.stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
        
        // 构建树形结构
        List<PermissionDTO> rootPermissions = allPermissionDTOs.stream()
                .filter(p -> p.getParentId() == null)
                .collect(Collectors.toList());
        
        rootPermissions.forEach(root -> buildPermissionTree(root, allPermissionDTOs));
        
        return rootPermissions;
    }

    private void buildPermissionTree(PermissionDTO parent, List<PermissionDTO> allPermissions) {
        List<PermissionDTO> children = allPermissions.stream()
                .filter(p -> parent.getId().equals(p.getParentId()))
                .collect(Collectors.toList());
        
        if (!children.isEmpty()) {
            parent.setChildren(children);
            children.forEach(child -> buildPermissionTree(child, allPermissions));
        }
    }

    @Override
    public List<PermissionDTO> getPermissionsByType(String type) {
        List<Permission> permissions = permissionRepository.findByCategory(type);
        return permissions.stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
    }

    @Override
    public PermissionDTO getPermissionById(Long id) {
        Optional<Permission> permissionOptional = permissionRepository.findById(id);
        return permissionOptional.map(this::convertToDTO).orElse(null);
    }

    @Override
    @Transactional
    public Long createPermission(PermissionDTO permissionDTO) {
        Permission permission = convertToEntity(permissionDTO);
        Permission savedPermission = permissionRepository.save(permission);
        return savedPermission.getId();
    }

    @Override
    @Transactional
    public boolean updatePermission(PermissionDTO permissionDTO) {
        if (permissionDTO.getId() == null) {
            return false;
        }
        
        Optional<Permission> permissionOptional = permissionRepository.findById(permissionDTO.getId());
        if (permissionOptional.isEmpty()) {
            return false;
        }
        
        Permission permission = permissionOptional.get();
        permission.setName(permissionDTO.getName());
        permission.setCode(permissionDTO.getCode());
        permission.setDescription(permissionDTO.getDescription());
        permission.setCategory(permissionDTO.getType());
        permission.setParentId(permissionDTO.getParentId());
        
        permissionRepository.save(permission);
        return true;
    }

    @Override
    @Transactional
    public boolean deletePermission(Long id) {
        if (id == null) {
            return false;
        }
        
        // 检查是否有子权限
        List<Permission> children = permissionRepository.findByParentId(id);
        if (!children.isEmpty()) {
            return false;
        }
        
        permissionRepository.deleteById(id);
        return true;
    }

    @Override
    @Transactional
    public boolean batchDeletePermission(List<Long> ids) {
        if (ids == null || ids.isEmpty()) {
            return false;
        }
        
        // 检查是否有子权限
        for (Long id : ids) {
            List<Permission> children = permissionRepository.findByParentId(id);
            if (!children.isEmpty()) {
                return false;
            }
        }
        
        permissionRepository.deleteAllById(ids);
        return true;
    }
    
    /**
     * 将实体转换为DTO
     */
    private PermissionDTO convertToDTO(Permission permission) {
        PermissionDTO dto = new PermissionDTO();
        dto.setId(permission.getId());
        dto.setName(permission.getName());
        dto.setCode(permission.getCode());
        dto.setDescription(permission.getDescription());
        dto.setType(permission.getCategory());
        dto.setParentId(permission.getParentId());
        return dto;
    }
    
    /**
     * 将DTO转换为实体
     */
    private Permission convertToEntity(PermissionDTO dto) {
        Permission permission = new Permission();
        permission.setId(dto.getId());
        permission.setName(dto.getName());
        permission.setCode(dto.getCode());
        permission.setDescription(dto.getDescription());
        permission.setCategory(dto.getType());
        permission.setParentId(dto.getParentId());
        return permission;
    }
}
