package com.flightprice.prediction.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.flightprice.prediction.dto.TerminalDTO;
import com.flightprice.prediction.entity.Airport;
import com.flightprice.prediction.entity.Terminal;
import com.flightprice.prediction.mapper.TerminalMapper;
import com.flightprice.prediction.service.AirportService;
import com.flightprice.prediction.service.TerminalService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 航站楼服务实现类
 */
@Service
public class TerminalServiceImpl extends ServiceImpl<TerminalMapper, Terminal> implements TerminalService {

    @Autowired
    private AirportService airportService;

    @Override
    public IPage<Terminal> queryTerminalPage(Page<Terminal> page, TerminalDTO terminalDTO) {
        // 使用自定义Mapper方法
        return this.baseMapper.selectTerminalPage(
                page,
                terminalDTO.getName(),
                terminalDTO.getAirportId(),
                terminalDTO.getStatus()
        );
    }

    @Override
    public TerminalDTO getTerminalById(Long id) {
        Terminal terminal = this.getById(id);
        if (terminal == null) {
            return null;
        }
        
        TerminalDTO terminalDTO = new TerminalDTO();
        BeanUtils.copyProperties(terminal, terminalDTO);
        
        // 获取机场信息
        if (terminal.getAirportId() != null) {
            Airport airport = airportService.getById(terminal.getAirportId());
            if (airport != null) {
                terminalDTO.setAirportName(airport.getName());
            }
        }
        
        return terminalDTO;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveOrUpdateTerminal(TerminalDTO terminalDTO) {
        Terminal terminal = new Terminal();
        BeanUtils.copyProperties(terminalDTO, terminal);
        
        // 检查航站楼代码是否已存在
        if (terminalDTO.getId() == null) {
            LambdaQueryWrapper<Terminal> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(Terminal::getTerminalCode, terminalDTO.getTerminalCode());
            queryWrapper.eq(Terminal::getAirportId, terminalDTO.getAirportId());
            if (this.count(queryWrapper) > 0) {
                throw new RuntimeException("航站楼代码已存在");
            }
        }
        
        return this.saveOrUpdate(terminal);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateStatus(Long id, Integer status) {
        Terminal terminal = this.getById(id);
        if (terminal == null) {
            return false;
        }
        
        terminal.setStatus(status);
        return this.updateById(terminal);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteTerminal(Long id) {
        // 这里可以加入业务逻辑，例如检查是否有关联的数据
        return this.removeById(id);
    }
} 