package com.flightprice.prediction.service.impl;

import com.flightprice.prediction.common.api.CommonPage;
import com.flightprice.prediction.dto.AirportDTO;
import com.flightprice.prediction.dto.AirportStatsDTO;
import com.flightprice.prediction.dto.MonthDataDTO;
import com.flightprice.prediction.dto.MonthlyStatsDTO;
import com.flightprice.prediction.entity.Airport;
import com.flightprice.prediction.exception.ResourceNotFoundException;
import com.flightprice.prediction.exception.BusinessException;
import com.flightprice.prediction.repository.AirportRepository;
import com.flightprice.prediction.service.AirportService;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

/**
 * 机场服务实现类
 */
@Service
@RequiredArgsConstructor
public class AirportServiceImpl implements AirportService {

    private final AirportRepository airportRepository;
    private final Random random = new Random();

    @Override
    public Page<Airport> findAll(String keyword, Pageable pageable) {
        return airportRepository.findByKeyword(keyword, pageable);
    }

    @Override
    public Airport findById(Long id) {
        return airportRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("机场不存在，ID: " + id));
    }

    @Override
    public Airport findByIataCode(String iataCode) {
        return airportRepository.findByIataCode(iataCode)
                .orElseThrow(() -> new ResourceNotFoundException("机场不存在，IATA代码: " + iataCode));
    }

    @Override
    public Airport findByIcaoCode(String icaoCode) {
        return airportRepository.findByIcaoCode(icaoCode)
                .orElseThrow(() -> new ResourceNotFoundException("机场不存在，ICAO代码: " + icaoCode));
    }

    @Override
    public List<Airport> findByCity(String city) {
        return airportRepository.findByCity(city);
    }

    @Override
    @Transactional
    public Airport createAirport(AirportDTO airportDTO) {
        // 检查IATA代码是否已存在
        if (airportRepository.existsByIataCode(airportDTO.getIataCode())) {
            throw new BusinessException("IATA代码已存在");
        }

        // 检查ICAO代码是否已存在
        if (airportRepository.existsByIcaoCode(airportDTO.getIcaoCode())) {
            throw new BusinessException("ICAO代码已存在");
        }

        Airport airport = new Airport();
        BeanUtils.copyProperties(airportDTO, airport);
        return airportRepository.save(airport);
    }

    @Override
    @Transactional
    public Airport updateAirport(Long id, AirportDTO airportDTO) {
        Airport airport = findById(id);

        // 如果IATA代码发生变化，需要检查唯一性
        if (!airport.getCode().equals(airportDTO.getIataCode()) &&
                airportRepository.existsByIataCode(airportDTO.getIataCode())) {
            throw new BusinessException("IATA代码已存在");
        }

        // 如果ICAO代码发生变化，需要检查唯一性
        if (!airport.getIcaoCode().equals(airportDTO.getIcaoCode()) &&
                airportRepository.existsByIcaoCode(airportDTO.getIcaoCode())) {
            throw new BusinessException("ICAO代码已存在");
        }

        BeanUtils.copyProperties(airportDTO, airport);
        airport.setId(id); // 确保ID不变
        return airportRepository.save(airport);
    }

    @Override
    @Transactional
    public void deleteAirport(Long id) {
        if (!airportRepository.existsById(id)) {
            throw new ResourceNotFoundException("机场不存在，ID: " + id);
        }
        airportRepository.deleteById(id);
    }

    @Override
    @Transactional
    public Airport updateStatus(Long id, Boolean enabled) {
        Airport airport = findById(id);
        airport.setEnabled(enabled);
        return airportRepository.save(airport);
    }

    @Override
    public Map<String, Object> getStatistics(Long id) {
        // 确保机场存在
        findById(id);

        // 这里应该是从数据库中查询真实的统计数据
        // 为了演示，我们生成一些模拟数据
        Map<String, Object> stats = new HashMap<>();
        stats.put("flightsCount", 100 + random.nextInt(900));
        stats.put("routesCount", 20 + random.nextInt(50));
        stats.put("airlinesCount", 5 + random.nextInt(20));
        stats.put("avgDelay", Math.round((5 + random.nextDouble() * 20) * 10.0) / 10.0);

        // 月度数据
        Map<String, Object> monthlyData = new HashMap<>();
        String[] months = {"Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"};
        int[] flightCounts = new int[12];
        double[] avgDelays = new double[12];
        int[] passengerCounts = new int[12];

        for (int i = 0; i < 12; i++) {
            flightCounts[i] = 50 + random.nextInt(200);
            avgDelays[i] = Math.round((2 + random.nextDouble() * 30) * 10.0) / 10.0;
            passengerCounts[i] = 5000 + random.nextInt(20000);
        }

        monthlyData.put("months", months);
        monthlyData.put("flightCounts", flightCounts);
        monthlyData.put("avgDelays", avgDelays);
        monthlyData.put("passengerCounts", passengerCounts);

        stats.put("monthlyData", monthlyData);

        // 航空公司分布
        Map<String, Integer> airlineDistribution = new HashMap<>();
        String[] airlines = {"中国国航", "东方航空", "南方航空", "海南航空", "深圳航空"};
        for (String airline : airlines) {
            airlineDistribution.put(airline, 10 + random.nextInt(100));
        }
        stats.put("airlineDistribution", airlineDistribution);

        // 目的地分布
        Map<String, Integer> destinationDistribution = new HashMap<>();
        String[] destinations = {"北京", "上海", "广州", "深圳", "成都", "杭州", "重庆", "西安"};
        for (String destination : destinations) {
            destinationDistribution.put(destination, 5 + random.nextInt(50));
        }
        stats.put("destinationDistribution", destinationDistribution);

        return stats;
    }

    @Override
    public AirportStatsDTO getAirportStats(Long id) {
        // 确保机场存在
        findById(id);

        // 这里应该是从数据库中查询真实的统计数据
        // 为了演示，我们生成一些模拟数据
        AirportStatsDTO statsDTO = new AirportStatsDTO();
        statsDTO.setId(id);
        statsDTO.setAirportId(id);
        statsDTO.setFlightCount(1000 + random.nextInt(5000));
        statsDTO.setTotalFlights(1000 + random.nextInt(5000));
        statsDTO.setTotalPassengers(50000 + random.nextInt(500000));
        statsDTO.setAvgDelay(10 + random.nextInt(30));
        statsDTO.setOnTimeRate(Double.valueOf(70 + random.nextInt(20)));

        // 设置热门航线
        Map<String, Integer> popularRoutes = new HashMap<>();
        String[] routes = {"北京-上海", "北京-广州", "上海-深圳", "广州-成都", "北京-成都"};
        for (String route : routes) {
            popularRoutes.put(route, 50 + random.nextInt(200));
        }
        // Set both properties for compatibility
        statsDTO.setPopularRoutes(popularRoutes);

        // Set other required properties
        statsDTO.setAirportName("Airport " + id);
        statsDTO.setAirportCode("AP" + id);

        return statsDTO;
    }

    @Override
    public MonthlyStatsDTO getAirportMonthlyStats(Long id, Integer year) {
        // 确保机场存在
        findById(id);

        // 这里应该是从数据库中查询真实的统计数据
        // 为了演示，我们生成一些模拟数据
        MonthlyStatsDTO statsDTO = new MonthlyStatsDTO();
        statsDTO.setAirportId(id);
        statsDTO.setYear(year);

        // 生成月度数据
        List<MonthDataDTO> monthlyData = new ArrayList<>();
        for (int i = 1; i <= 12; i++) {
            MonthDataDTO monthData = new MonthDataDTO();
            monthData.setMonth(i);
            monthData.setFlightCount(100 + random.nextInt(500));
            monthData.setPassengerCount(5000 + random.nextInt(50000));
            monthData.setAvgDelay(5 + random.nextInt(20));
            monthData.setOnTimeRate(75 + random.nextInt(20));
            monthlyData.add(monthData);
        }

        statsDTO.setMonthlyData(monthlyData);
        return statsDTO;
    }

    @Override
    @Transactional
    public void deleteBatch(List<Long> ids) {
        if (ids == null || ids.isEmpty()) {
            return;
        }
        airportRepository.deleteAllById(ids);
    }

    @Override
    public Airport getById(Long id) {
        return findById(id);
    }

    @Override
    public List<Airport> getAllAirports() {
        return airportRepository.findAll();
    }

    @Override
    public CommonPage<Airport> list(Integer pageNum, Integer pageSize, String keyword) {
        Page<Airport> page = findAll(keyword, PageRequest.of(pageNum - 1, pageSize));
        return CommonPage.restPage(page.getContent(), pageNum, pageSize, page.getTotalElements());
    }

    @Override
    public Airport create(AirportDTO airportDTO) {
        return createAirport(airportDTO);
    }

    @Override
    public Airport update(Long id, AirportDTO airportDTO) {
        return updateAirport(id, airportDTO);
    }

    @Override
    public void delete(Long id) {
        deleteAirport(id);
    }
}