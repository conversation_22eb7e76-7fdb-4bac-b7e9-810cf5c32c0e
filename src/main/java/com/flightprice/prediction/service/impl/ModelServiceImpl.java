package com.flightprice.prediction.service.impl;

import com.flightprice.prediction.dto.ModelDTO;
import com.flightprice.prediction.dto.ModelTrainingHistoryDTO;
import com.flightprice.prediction.entity.Model;
import com.flightprice.prediction.entity.ModelTrainingHistory;
import com.flightprice.prediction.exception.ResourceNotFoundException;
import com.flightprice.prediction.repository.ModelRepository;
import com.flightprice.prediction.repository.ModelTrainingHistoryRepository;
import com.flightprice.prediction.service.ModelService;
import com.flightprice.prediction.service.UserService;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class ModelServiceImpl implements ModelService {

    private final ModelRepository modelRepository;
    private final ModelTrainingHistoryRepository trainingHistoryRepository;
    private final UserService userService;
    private final ObjectMapper objectMapper;

    @Override
    public Page<Model> findAll(String keyword, Pageable pageable) {
        return modelRepository.findByKeyword(keyword, pageable);
    }

    @Override
    public Model findById(Long id) {
        return modelRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("模型不存在，ID: " + id));
    }

    @Override
    @Transactional
    public Model createModel(ModelDTO modelDTO) {
        // 检查模型名称是否已存在
        if (modelRepository.existsByName(modelDTO.getName())) {
            throw new IllegalArgumentException("模型名称已存在: " + modelDTO.getName());
        }

        Model model = new Model();
        model.setName(modelDTO.getName());
        model.setType(modelDTO.getType());
        model.setDescription(modelDTO.getDescription());
        model.setVersion(modelDTO.getVersion());
        model.setStatus("未训练"); // 初始状态为未训练
        model.setIsActive(false);
        model.setCreatedBy(modelDTO.getCreatedBy());

        // 将参数和特征转换为JSON字符串
        try {
            if (modelDTO.getParameters() != null) {
                model.setParameters(objectMapper.writeValueAsString(modelDTO.getParameters()));
            }
            if (modelDTO.getFeatures() != null) {
                model.setFeatures(objectMapper.writeValueAsString(modelDTO.getFeatures()));
            }
        } catch (JsonProcessingException e) {
            throw new RuntimeException("JSON处理异常", e);
        }

        return modelRepository.save(model);
    }

    @Override
    @Transactional
    public Model updateModel(Long id, ModelDTO modelDTO) {
        Model model = findById(id);

        // 检查是否要更新名称，且名称是否已存在
        if (!model.getName().equals(modelDTO.getName()) && 
            modelRepository.existsByName(modelDTO.getName())) {
            throw new IllegalArgumentException("模型名称已存在: " + modelDTO.getName());
        }

        model.setName(modelDTO.getName());
        model.setType(modelDTO.getType());
        model.setDescription(modelDTO.getDescription());
        model.setVersion(modelDTO.getVersion());

        // 将参数和特征转换为JSON字符串
        try {
            if (modelDTO.getParameters() != null) {
                model.setParameters(objectMapper.writeValueAsString(modelDTO.getParameters()));
            }
            if (modelDTO.getFeatures() != null) {
                model.setFeatures(objectMapper.writeValueAsString(modelDTO.getFeatures()));
            }
        } catch (JsonProcessingException e) {
            throw new RuntimeException("JSON处理异常", e);
        }

        return modelRepository.save(model);
    }

    @Override
    @Transactional
    public void deleteModel(Long id) {
        Model model = findById(id);
        
        // 检查模型是否为激活状态
        if (model.getIsActive()) {
            throw new IllegalStateException("不能删除当前激活的模型");
        }
        
        modelRepository.delete(model);
    }

    @Override
    @Transactional
    public Model deployModel(Long id) {
        Model model = findById(id);
        
        // 检查模型状态是否为已训练
        if (!"已训练".equals(model.getStatus())) {
            throw new IllegalStateException("只有已训练的模型才能部署");
        }
        
        // 将所有模型设置为非激活状态
        modelRepository.findByIsActiveTrue().ifPresent(activeModel -> {
            activeModel.setIsActive(false);
            modelRepository.save(activeModel);
        });
        
        // 设置当前模型为激活状态
        model.setIsActive(true);
        model.setStatus("已部署");
        model.setLastDeployedAt(LocalDateTime.now());
        
        return modelRepository.save(model);
    }

    @Override
    @Transactional
    public Model deactivateModel(Long id) {
        Model model = findById(id);
        
        // 检查模型是否为激活状态
        if (!model.getIsActive()) {
            throw new IllegalStateException("模型当前未激活");
        }
        
        model.setIsActive(false);
        model.setStatus("已停用");
        
        return modelRepository.save(model);
    }

    @Override
    @Transactional
    public Long trainModel(Long id, Map<String, Object> parameters) {
        Model model = findById(id);
        
        // 创建训练历史记录
        ModelTrainingHistory trainingHistory = new ModelTrainingHistory();
        trainingHistory.setModelId(id);
        trainingHistory.setStatus("进行中");
        trainingHistory.setStartTime(LocalDateTime.now());
        trainingHistory.setTrainedBy(model.getCreatedBy());
        
        try {
            if (parameters != null) {
                trainingHistory.setParameters(objectMapper.writeValueAsString(parameters));
            }
        } catch (JsonProcessingException e) {
            throw new RuntimeException("JSON处理异常", e);
        }
        
        trainingHistory = trainingHistoryRepository.save(trainingHistory);
        
        // 实际上，这里应该调用模型训练服务进行异步训练
        // 为了演示，我们直接更新训练结果
        simulateTraining(model, trainingHistory);
        
        return trainingHistory.getId();
    }

    // 模拟训练过程
    private void simulateTraining(Model model, ModelTrainingHistory trainingHistory) {
        // 设置一些模拟的训练结果
        trainingHistory.setEndTime(LocalDateTime.now());
        trainingHistory.setDuration(10L); // 假设训练耗时10秒
        trainingHistory.setStatus("成功");
        trainingHistory.setTrainingSize(1000);
        trainingHistory.setTestSize(200);
        trainingHistory.setAccuracy(0.85 + Math.random() * 0.1); // 85%-95%的准确率
        trainingHistory.setMeanError(0.1 + Math.random() * 0.05); // 10%-15%的平均误差
        trainingHistory.setRSquared(0.8 + Math.random() * 0.15); // 0.8-0.95的R方值
        trainingHistory.setMae(20.0 + Math.random() * 10.0); // 20-30的MAE
        trainingHistory.setRmse(25.0 + Math.random() * 15.0); // 25-40的RMSE
        
        // 保存训练历史
        trainingHistoryRepository.save(trainingHistory);
        
        // 更新模型信息
        model.setStatus("已训练");
        model.setLastTrainedAt(LocalDateTime.now());
        model.setAccuracy(trainingHistory.getAccuracy());
        model.setMeanError(trainingHistory.getMeanError());
        model.setTrainingSize(trainingHistory.getTrainingSize());
        model.setTestSize(trainingHistory.getTestSize());
        
        modelRepository.save(model);
    }

    @Override
    public ModelTrainingHistoryDTO getTrainingHistory(Long id) {
        ModelTrainingHistory history = trainingHistoryRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("训练历史不存在，ID: " + id));
        
        return convertTrainingHistoryToDTO(history);
    }

    @Override
    public Page<ModelTrainingHistoryDTO> getTrainingHistories(Long modelId, Pageable pageable) {
        Page<ModelTrainingHistory> histories = trainingHistoryRepository.findByModelId(modelId, pageable);
        
        return histories.map(this::convertTrainingHistoryToDTO);
    }

    @Override
    public Map<String, Object> getModelEvaluation(Long id) {
        Model model = findById(id);
        ModelTrainingHistory latestHistory = trainingHistoryRepository
                .findTopByModelIdAndStatusOrderByEndTimeDesc(id, "成功");
        
        if (latestHistory == null) {
            throw new ResourceNotFoundException("模型没有成功的训练记录");
        }
        
        Map<String, Object> evaluation = new HashMap<>();
        evaluation.put("accuracy", latestHistory.getAccuracy());
        evaluation.put("meanError", latestHistory.getMeanError());
        evaluation.put("rSquared", latestHistory.getRSquared());
        evaluation.put("mae", latestHistory.getMae());
        evaluation.put("rmse", latestHistory.getRmse());
        evaluation.put("trainingSize", latestHistory.getTrainingSize());
        evaluation.put("testSize", latestHistory.getTestSize());
        evaluation.put("trainedAt", latestHistory.getEndTime());
        
        // 添加评估详情（如果有）
        try {
            if (latestHistory.getEvaluationDetails() != null) {
                Map<String, Object> details = objectMapper.readValue(
                        latestHistory.getEvaluationDetails(), Map.class);
                evaluation.put("details", details);
            }
        } catch (JsonProcessingException e) {
            throw new RuntimeException("JSON处理异常", e);
        }
        
        return evaluation;
    }

    @Override
    public Map<String, Object> getModelStatistics(Long id) {
        // 检查模型是否存在
        findById(id);
        
        Map<String, Object> statistics = new HashMap<>();
        
        // 训练次数
        long trainingCount = trainingHistoryRepository.countByModelId(id);
        statistics.put("trainingCount", trainingCount);
        
        // 成功训练次数
        long successCount = trainingHistoryRepository.countByModelIdAndStatus(id, "成功");
        statistics.put("successCount", successCount);
        
        // 失败训练次数
        long failureCount = trainingHistoryRepository.countByModelIdAndStatus(id, "失败");
        statistics.put("failureCount", failureCount);
        
        // 平均准确率
        Double avgAccuracy = trainingHistoryRepository.getAverageAccuracyByModelId(id);
        statistics.put("averageAccuracy", avgAccuracy);
        
        // 最高准确率
        Double maxAccuracy = trainingHistoryRepository.getMaxAccuracyByModelId(id);
        statistics.put("bestAccuracy", maxAccuracy);
        
        // 最低准确率
        Double minAccuracy = trainingHistoryRepository.getMinAccuracyByModelId(id);
        statistics.put("worstAccuracy", minAccuracy);
        
        // 最近一次训练时间
        ModelTrainingHistory latestTraining = trainingHistoryRepository
                .findTopByModelIdOrderByCreatedAtDesc(id);
        
        if (latestTraining != null) {
            statistics.put("lastTrainingTime", latestTraining.getCreatedAt());
        }
        
        return statistics;
    }

    @Override
    public Model getActiveModel() {
        return modelRepository.findByIsActiveTrue()
                .orElseThrow(() -> new ResourceNotFoundException("当前没有激活的模型"));
    }

    @Override
    public List<Model> getModelsByStatus(String status) {
        return modelRepository.findByStatus(status);
    }

    @Override
    public List<String> getModelTypes() {
        List<Model> models = modelRepository.findAll();
        return models.stream()
                .map(Model::getType)
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.toList());
    }

    @Override
    public ModelDTO convertToDTO(Model model) {
        ModelDTO dto = new ModelDTO();
        dto.setId(model.getId());
        dto.setName(model.getName());
        dto.setType(model.getType());
        dto.setDescription(model.getDescription());
        dto.setVersion(model.getVersion());
        dto.setStatus(model.getStatus());
        dto.setIsActive(model.getIsActive());
        dto.setTrainingSize(model.getTrainingSize());
        dto.setTestSize(model.getTestSize());
        dto.setAccuracy(model.getAccuracy());
        dto.setMeanError(model.getMeanError());
        dto.setFilePath(model.getFilePath());
        dto.setCreatedAt(model.getCreatedAt());
        dto.setUpdatedAt(model.getUpdatedAt());
        dto.setLastTrainedAt(model.getLastTrainedAt());
        dto.setLastDeployedAt(model.getLastDeployedAt());
        dto.setCreatedBy(model.getCreatedBy());
        
        // 转换JSON字符串为对象
        try {
            if (model.getParameters() != null) {
                dto.setParameters(objectMapper.readValue(model.getParameters(), Map.class));
            }
            if (model.getFeatures() != null) {
                dto.setFeatures(objectMapper.readValue(model.getFeatures(), String[].class));
            }
        } catch (JsonProcessingException e) {
            throw new RuntimeException("JSON处理异常", e);
        }
        
        // 设置创建者名称
        if (model.getCreatedBy() != null) {
            try {
                String creatorName = userService.findById(model.getCreatedBy()).getUsername();
                dto.setCreatedByName(creatorName);
            } catch (Exception e) {
                dto.setCreatedByName("未知用户");
            }
        }
        
        return dto;
    }
    
    private ModelTrainingHistoryDTO convertTrainingHistoryToDTO(ModelTrainingHistory history) {
        ModelTrainingHistoryDTO dto = new ModelTrainingHistoryDTO();
        dto.setId(history.getId());
        dto.setModelId(history.getModelId());
        dto.setStatus(history.getStatus());
        dto.setStartTime(history.getStartTime());
        dto.setEndTime(history.getEndTime());
        dto.setDuration(history.getDuration());
        dto.setTrainingSize(history.getTrainingSize());
        dto.setTestSize(history.getTestSize());
        dto.setAccuracy(history.getAccuracy());
        dto.setMeanError(history.getMeanError());
        dto.setRSquared(history.getRSquared());
        dto.setMae(history.getMae());
        dto.setRmse(history.getRmse());
        dto.setErrorMessage(history.getErrorMessage());
        dto.setLogFile(history.getLogFile());
        dto.setTrainedBy(history.getTrainedBy());
        dto.setCreatedAt(history.getCreatedAt());
        
        // 获取模型名称
        try {
            Model model = findById(history.getModelId());
            dto.setModelName(model.getName());
        } catch (Exception e) {
            dto.setModelName("未知模型");
        }
        
        // 设置训练者名称
        if (history.getTrainedBy() != null) {
            try {
                String trainerName = userService.findById(history.getTrainedBy()).getUsername();
                dto.setTrainedByName(trainerName);
            } catch (Exception e) {
                dto.setTrainedByName("未知用户");
            }
        }
        
        // 转换JSON字符串为对象
        try {
            if (history.getParameters() != null) {
                dto.setParameters(objectMapper.readValue(history.getParameters(), Map.class));
            }
            if (history.getEvaluationDetails() != null) {
                dto.setEvaluationDetails(objectMapper.readValue(history.getEvaluationDetails(), Map.class));
            }
        } catch (JsonProcessingException e) {
            throw new RuntimeException("JSON处理异常", e);
        }
        
        return dto;
    }
} 