package com.flightprice.prediction.service.impl;

import com.flightprice.prediction.dto.RoleDTO;
import com.flightprice.prediction.entity.Permission;
import com.flightprice.prediction.entity.Role;
import com.flightprice.prediction.entity.RolePermission;
import com.flightprice.prediction.repository.PermissionRepository;
import com.flightprice.prediction.repository.RolePermissionRepository;
import com.flightprice.prediction.repository.RoleRepository;
import com.flightprice.prediction.service.RoleService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import javax.persistence.criteria.Predicate;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 角色服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class RoleServiceImpl implements RoleService {

    private final RoleRepository roleRepository;
    private final PermissionRepository permissionRepository;
    private final RolePermissionRepository rolePermissionRepository;

    @Override
    public Page<RoleDTO> getRolePage(String keyword, Pageable pageable) {
        Specification<Role> specification = (root, query, criteriaBuilder) -> {
            List<Predicate> predicates = new ArrayList<>();
            
            if (StringUtils.hasText(keyword)) {
                Predicate namePredicate = criteriaBuilder.like(root.get("name"), "%" + keyword + "%");
                Predicate codePredicate = criteriaBuilder.like(root.get("code"), "%" + keyword + "%");
                Predicate descriptionPredicate = criteriaBuilder.like(root.get("description"), "%" + keyword + "%");
                predicates.add(criteriaBuilder.or(namePredicate, codePredicate, descriptionPredicate));
            }
            
            return criteriaBuilder.and(predicates.toArray(new Predicate[0]));
        };
        
        Page<Role> rolePage = roleRepository.findAll(specification, pageable);
        return rolePage.map(this::convertToDTO);
    }

    @Override
    public List<RoleDTO> getAllRoles() {
        List<Role> roles = roleRepository.findAll();
        return roles.stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
    }

    @Override
    public RoleDTO getRoleById(Long id) {
        Optional<Role> roleOptional = roleRepository.findById(id);
        return roleOptional.map(this::convertToDTO).orElse(null);
    }

    @Override
    @Transactional
    public Long createRole(RoleDTO roleDTO) {
        Role role = convertToEntity(roleDTO);
        Role savedRole = roleRepository.save(role);
        
        // 保存角色权限关联
        if (roleDTO.getPermissionIds() != null && !roleDTO.getPermissionIds().isEmpty()) {
            saveRolePermissions(savedRole.getId(), roleDTO.getPermissionIds());
        }
        
        return savedRole.getId();
    }

    @Override
    @Transactional
    public boolean updateRole(RoleDTO roleDTO) {
        if (roleDTO.getId() == null) {
            return false;
        }
        
        Optional<Role> roleOptional = roleRepository.findById(roleDTO.getId());
        if (roleOptional.isEmpty()) {
            return false;
        }
        
        Role role = roleOptional.get();
        role.setName(roleDTO.getName());
        role.setCode(roleDTO.getCode());
        role.setDescription(roleDTO.getDescription());
        
        roleRepository.save(role);
        
        // 更新角色权限关联
        if (roleDTO.getPermissionIds() != null) {
            rolePermissionRepository.deleteByRoleId(role.getId());
            if (!roleDTO.getPermissionIds().isEmpty()) {
                saveRolePermissions(role.getId(), roleDTO.getPermissionIds());
            }
        }
        
        return true;
    }

    @Override
    @Transactional
    public boolean deleteRole(Long id) {
        if (id == null) {
            return false;
        }
        
        // 删除角色权限关联
        rolePermissionRepository.deleteByRoleId(id);
        
        // 删除角色
        roleRepository.deleteById(id);
        return true;
    }

    @Override
    @Transactional
    public boolean batchDeleteRole(List<Long> ids) {
        if (ids == null || ids.isEmpty()) {
            return false;
        }
        
        // 删除角色权限关联
        for (Long id : ids) {
            rolePermissionRepository.deleteByRoleId(id);
        }
        
        // 删除角色
        roleRepository.deleteAllById(ids);
        return true;
    }

    @Override
    public List<Long> getRolePermissionIds(Long roleId) {
        List<RolePermission> rolePermissions = rolePermissionRepository.findByRoleId(roleId);
        return rolePermissions.stream()
                .map(RolePermission::getPermissionId)
                .collect(Collectors.toList());
    }

    @Override
    @Transactional
    public boolean assignRolePermissions(Long roleId, List<Long> permissionIds) {
        if (roleId == null) {
            return false;
        }
        
        Optional<Role> roleOptional = roleRepository.findById(roleId);
        if (roleOptional.isEmpty()) {
            return false;
        }
        
        // 删除原有角色权限关联
        rolePermissionRepository.deleteByRoleId(roleId);
        
        // 保存新的角色权限关联
        if (permissionIds != null && !permissionIds.isEmpty()) {
            saveRolePermissions(roleId, permissionIds);
        }
        
        return true;
    }
    
    /**
     * 保存角色权限关联
     */
    private void saveRolePermissions(Long roleId, List<Long> permissionIds) {
        List<RolePermission> rolePermissions = new ArrayList<>();
        for (Long permissionId : permissionIds) {
            RolePermission rolePermission = new RolePermission();
            rolePermission.setRoleId(roleId);
            rolePermission.setPermissionId(permissionId);
            rolePermissions.add(rolePermission);
        }
        rolePermissionRepository.saveAll(rolePermissions);
    }
    
    /**
     * 将实体转换为DTO
     */
    private RoleDTO convertToDTO(Role role) {
        RoleDTO dto = new RoleDTO();
        dto.setId(role.getId());
        dto.setName(role.getName());
        dto.setCode(role.getCode());
        dto.setDescription(role.getDescription());
        
        // 获取角色权限ID列表
        if (role.getPermissions() != null) {
            List<Long> permissionIds = role.getPermissions().stream()
                    .map(Permission::getId)
                    .collect(Collectors.toList());
            dto.setPermissionIds(permissionIds);
        }
        
        return dto;
    }
    
    /**
     * 将DTO转换为实体
     */
    private Role convertToEntity(RoleDTO dto) {
        Role role = new Role();
        role.setId(dto.getId());
        role.setName(dto.getName());
        role.setCode(dto.getCode());
        role.setDescription(dto.getDescription());
        
        // 设置角色权限
        if (dto.getPermissionIds() != null && !dto.getPermissionIds().isEmpty()) {
            Set<Permission> permissions = new HashSet<>();
            for (Long permissionId : dto.getPermissionIds()) {
                permissionRepository.findById(permissionId).ifPresent(permissions::add);
            }
            role.setPermissions(permissions);
        }
        
        return role;
    }
}
