package com.flightprice.prediction.config;

import com.flightprice.prediction.service.FlightDataService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.CommandLineRunner;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Component;
import org.springframework.beans.factory.annotation.Autowired;

import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.StandardCopyOption;

/**
 * 数据导入配置类
 * 项目启动时自动导入Excel数据到MySQL
 */
@Slf4j
@Component
public class DataImportConfig implements CommandLineRunner {

    private final FlightDataService flightDataService;

    @Autowired
    public DataImportConfig(FlightDataService flightDataService) {
        this.flightDataService = flightDataService;
    }

    @Override
    public void run(String... args) throws Exception {
        //参数包含导入,执行下面的代码 ,否则直接返回
        if (args.length == 0 || !args[0].equals("import")) {
            return;
        }
        try {
            log.info("开始执行航班数据导入...");

            // 从classpath中获取数据文件
            ClassPathResource resource = new ClassPathResource("data/中国航空航班数据.xlsx");
            if (!resource.exists()) {
                log.warn("航班数据文件不存在，跳过数据导入");
                return;
            }

            // 将资源文件复制到临时目录
            Path tempFile = Files.createTempFile("flight_data_", ".csv");
            Files.copy(resource.getInputStream(), tempFile, StandardCopyOption.REPLACE_EXISTING);
            String filePath = tempFile.toAbsolutePath().toString();

            // 执行数据导入
            int count = flightDataService.importFromExcel(filePath);

            // 删除临时文件
            Files.deleteIfExists(tempFile);

            log.info("航班数据导入完成，共导入 {} 条数据", count);
        } catch (Exception e) {
            log.error("航班数据导入失败：{}", e.getMessage(), e);
        }
    }
}