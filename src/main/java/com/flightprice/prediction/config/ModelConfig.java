package com.flightprice.prediction.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.Map;

/**
 * 模型配置类
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "model")
public class ModelConfig {
    
    /**
     * 模型文件保存路径
     */
    private String savePath;
    
    /**
     * 是否在启动时训练模型
     */
    private Boolean trainOnStartup = false;
    
    /**
     * 是否使用通用模型（不指定航线ID）
     */
    private Boolean useGeneralModel = true;
    
    /**
     * 训练数据最小条数
     */
    private Integer minTrainingData = 100;
    
    /**
     * 模型参数
     */
    private Map<String, Object> parameters;
} 