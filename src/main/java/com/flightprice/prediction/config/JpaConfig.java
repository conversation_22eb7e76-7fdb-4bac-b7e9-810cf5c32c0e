package com.flightprice.prediction.config;

import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.transaction.annotation.EnableTransactionManagement;

/**
 * JPA配置类
 */
@Configuration
@EnableTransactionManagement
@EntityScan(basePackages = "com.flightprice.prediction.entity")
@EnableJpaRepositories(basePackages = "com.flightprice.prediction.repository")
public class JpaConfig {
    // JPA默认配置即可，无需添加额外配置
} 