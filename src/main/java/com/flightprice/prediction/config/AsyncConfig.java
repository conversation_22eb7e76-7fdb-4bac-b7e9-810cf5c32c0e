package com.flightprice.prediction.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.AsyncConfigurer;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.Executor;
import java.util.concurrent.ThreadPoolExecutor;

/**
 * 异步任务配置
 * 配置异步任务执行器线程池
 */
@Configuration
@Slf4j
public class AsyncConfig implements AsyncConfigurer {

    /**
     * 配置缓存加载专用线程池
     */
    @Bean("cacheTaskExecutor")
    public Executor cacheTaskExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        // 核心线程数：处理缓存加载任务的基本线程数
        executor.setCorePoolSize(2);
        // 最大线程数：最多同时执行的缓存加载任务数
        executor.setMaxPoolSize(4);
        // 队列容量：当核心线程都在工作时，新任务会放入队列等待
        executor.setQueueCapacity(100);
        // 线程名前缀，方便区分和调试
        executor.setThreadNamePrefix("Cache-Loader-");
        // 当队列满了且达到最大线程数时的拒绝策略：调用者所在线程执行任务
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        // 等待所有任务执行完成再关闭线程池
        executor.setWaitForTasksToCompleteOnShutdown(true);
        // 线程池关闭前等待的最长时间
        executor.setAwaitTerminationSeconds(60);
        // 初始化线程池
        executor.initialize();
        log.info("缓存加载专用线程池初始化完成");
        return executor;
    }

    /**
     * 默认异步任务执行器
     */
    @Override
    public Executor getAsyncExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        // 核心线程数
        executor.setCorePoolSize(5);
        // 最大线程数
        executor.setMaxPoolSize(10);
        // 队列容量
        executor.setQueueCapacity(200);
        // 线程名前缀
        executor.setThreadNamePrefix("Async-Task-");
        // 拒绝策略
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        executor.setWaitForTasksToCompleteOnShutdown(true);
        executor.setAwaitTerminationSeconds(60);
        executor.initialize();
        log.info("默认异步任务执行器初始化完成");
        return executor;
    }
} 