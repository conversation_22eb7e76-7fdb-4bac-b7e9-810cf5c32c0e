package com.flightprice.prediction.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.deser.std.DateDeserializers;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 航班实体类
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("flight")
public class Flight extends BaseEntity {

    /**
     * 航班号
     */
    private String flightNumber;

    /**
     * 航空公司ID
     */
    private Long airlineId;

    /**
     * 航线ID
     */
    private Long routeId;

    /**
     * 出发机场ID
     */
    private Long departureAirportId;

    /**
     * 到达机场ID
     */
    private Long arrivalAirportId;

    /**
     * 出发时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @JsonDeserialize(using = com.flightprice.prediction.config.CustomDateDeserializer.class)
    private Date departureTime;

    /**
     * 到达时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @JsonDeserialize(using = com.flightprice.prediction.config.CustomDateDeserializer.class)
    private Date arrivalTime;

    /**
     * 飞机类型
     */
    private String aircraftType;

    /**
     * 状态
     */
    private String status;
} 