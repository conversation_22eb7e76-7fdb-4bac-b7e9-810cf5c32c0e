package com.flightprice.prediction.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 预测模型实体类
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("prediction_model")
public class PredictionModel extends BaseEntity {

    /**
     * 模型名称
     */
    private String modelName;

    /**
     * 模型路径
     */
    private String modelPath;

    /**
     * 适用航线ID，NULL表示通用模型
     */
    private Long routeId;

    /**
     * 准确率
     */
    private BigDecimal accuracy;

    /**
     * 平均绝对误差
     */
    private BigDecimal mae;

    /**
     * 均方误差
     */
    private BigDecimal mse;

    /**
     * 最后训练时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date lastTrained;

    /**
     * 是否激活
     */
    private Boolean isActive;
} 