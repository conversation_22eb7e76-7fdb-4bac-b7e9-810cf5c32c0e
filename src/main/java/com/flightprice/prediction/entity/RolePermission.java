package com.flightprice.prediction.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

/**
 * 角色权限关联实体类
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "role_permission")
@TableName("role_permission")
public class RolePermission extends BaseEntity {
    
    /**
     * 角色ID
     */
    @Column(nullable = false)
    private Long roleId;

    /**
     * 权限ID
     */
    @Column(nullable = false)
    private Long permissionId;
} 