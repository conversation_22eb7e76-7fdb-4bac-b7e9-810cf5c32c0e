package com.flightprice.prediction.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 航班数据实体类，用于从Excel导入数据
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("flight_data")
public class FlightData extends BaseEntity {

    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 出发城市
     */
    private String departureCity;

    /**
     * 出发城市y坐标
     */
    private Double departureCityY;

    /**
     * 出发城市x坐标
     */
    private Double departureCityX;

    /**
     * 到达城市
     */
    private String arrivalCity;

    /**
     * 到达城市y坐标
     */
    private Double arrivalCityY;

    /**
     * 到达城市x坐标
     */
    private Double arrivalCityX;

    /**
     * 里程（公里）
     */
    private Integer mileage;

    /**
     * 航班班次
     */
    private String flightNumber;

    /**
     * 航空公司
     */
    private String airline;

    /**
     * 机型
     */
    private String aircraftType;

    /**
     * 起飞机场
     */
    private String departureAirport;

    /**
     * 起飞机场y坐标
     */
    private Double departureAirportY;

    /**
     * 起飞机场x坐标
     */
    private Double departureAirportX;

    /**
     * 降落机场
     */
    private String arrivalAirport;

    /**
     * 降落机场y坐标
     */
    private Double arrivalAirportY;

    /**
     * 降落机场x坐标
     */
    private Double arrivalAirportX;

    /**
     * 准点率
     */
    private Double punctualityRate;

    /**
     * 平均误点时间
     */
    private String averageDelayTime;

    /**
     * 周一班期
     */
    private String mondaySchedule;

    /**
     * 周二班期
     */
    private String tuesdaySchedule;

    /**
     * 周三班期
     */
    private String wednesdaySchedule;

    /**
     * 周四班期
     */
    private String thursdaySchedule;

    /**
     * 周五班期
     */
    private String fridaySchedule;

    /**
     * 周六班期
     */
    private String saturdaySchedule;

    /**
     * 周日班期
     */
    private String sundaySchedule;

    /**
     * 城市_x
     */
    private String cityX;

    /**
     * 出发省份
     */
    private String departureProvince;

    /**
     * 城市_y
     */
    private String cityY;

    /**
     * 到达省份
     */
    private String arrivalProvince;

    /**
     * 起飞时间
     */
    private String departureTime;

    /**
     * 降落时间
     */
    private String arrivalTime;

    /**
     * 日期
     */
    private String flightDate;

    /**
     * 价格（元）
     */
    private BigDecimal price;

    /**
     * 人数
     */
    private Integer passengerCount;

    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

    @TableLogic
    private Boolean isDeleted;
}