package com.flightprice.prediction.entity;

import lombok.Data;
import javax.persistence.*;
import java.math.BigDecimal;
import java.util.Date;

@Data
@Entity
@Table(name = "price_comparison")
public class PriceComparison {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "flight_id")
    private Long flightId;

    @Column(name = "cabin_class")
    private String cabinClass;

    @Column(name = "original_price")
    private BigDecimal originalPrice;

    @Column(name = "predicted_price")
    private BigDecimal predictedPrice;

    @Column(name = "actual_price")
    private BigDecimal actualPrice;

    @Column(name = "prediction_date")
    private Date predictionDate;

    @Column(name = "actual_date")
    private Date actualDate;

    @Column(name = "accuracy")
    private BigDecimal accuracy;

    @Column(name = "create_time")
    private Date createTime;

    @Column(name = "update_time")
    private Date updateTime;
} 