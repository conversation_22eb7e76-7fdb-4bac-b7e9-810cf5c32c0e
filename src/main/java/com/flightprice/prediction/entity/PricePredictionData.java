package com.flightprice.prediction.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;
import java.util.Map;

/**
 * 价格预测数据实体类
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("price_prediction_data")
public class PricePredictionData extends BaseEntity {

    /**
     * 模型ID
     */
    private Long modelId;

    /**
     * 出发城市
     */
    private String departureCity;

    /**
     * 到达城市
     */
    private String arrivalCity;

    /**
     * 航空公司
     */
    private String airline;

    /**
     * 飞行日期
     */
    private Date flightDate;

    /**
     * 出发时间
     */
    private String departureTime;

    /**
     * 预测价格
     */
    private BigDecimal predictedPrice;

    /**
     * 实际价格
     */
    private BigDecimal actualPrice;

    /**
     * 误差率
     */
    private BigDecimal errorRate;

    /**
     * 预测时间
     */
    private Date predictionTime;

    /**
     * 预测使用的特征数据
     */
    private Map<String, Object> featureData;
} 