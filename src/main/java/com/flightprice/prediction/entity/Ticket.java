package com.flightprice.prediction.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * 机票实体类
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("ticket")
public class Ticket extends BaseEntity {

    /**
     * 航班ID
     */
    private Long flightId;

    /**
     * 票价
     */
    private BigDecimal price;

    /**
     * 折扣
     */
    private BigDecimal discount;

    /**
     * 舱位等级
     */
    private String cabinClass;

    /**
     * 可用座位数
     */
    private Integer availableSeats;
} 