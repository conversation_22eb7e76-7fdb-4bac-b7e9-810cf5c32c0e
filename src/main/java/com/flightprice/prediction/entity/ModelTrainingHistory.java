package com.flightprice.prediction.entity;

import lombok.Data;
import org.hibernate.annotations.CreationTimestamp;

import javax.persistence.*;
import java.time.LocalDateTime;

/**
 * 模型训练历史记录实体类
 */
@Data
@Entity
@Table(name = "model_training_history")
public class ModelTrainingHistory {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 关联的模型ID
     */
    @Column(nullable = false)
    private Long modelId;

    /**
     * 训练参数，JSON格式
     */
    @Column(columnDefinition = "TEXT")
    private String parameters;

    /**
     * 训练集大小
     */
    private Integer trainingSize;

    /**
     * 测试集大小
     */
    private Integer testSize;

    /**
     * 训练准确率
     */
    private Double accuracy;

    /**
     * 训练平均误差
     */
    private Double meanError;

    /**
     * 训练R方值
     */
    private Double rSquared;

    /**
     * 训练MAE值（平均绝对误差）
     */
    private Double mae;

    /**
     * 训练RMSE值（均方根误差）
     */
    private Double rmse;

    /**
     * 训练状态（成功/失败/进行中）
     */
    @Column(length = 20)
    private String status;

    /**
     * 错误信息
     */
    @Column(length = 500)
    private String errorMessage;

    /**
     * 训练开始时间
     */
    private LocalDateTime startTime;

    /**
     * 训练结束时间
     */
    private LocalDateTime endTime;

    /**
     * 训练耗时（秒）
     */
    private Long duration;

    /**
     * 训练日志文件路径
     */
    @Column(length = 200)
    private String logFile;

    /**
     * 训练性能评估详情，JSON格式
     */
    @Column(columnDefinition = "TEXT")
    private String evaluationDetails;

    /**
     * 训练者ID
     */
    private Long trainedBy;

    @CreationTimestamp
    private LocalDateTime createdAt;
} 