package com.flightprice.prediction.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * 航线实体类
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("route")
public class Route extends BaseEntity {

    /**
     * 出发城市ID
     */
    private Long departureCityId;

    /**
     * 到达城市ID
     */
    private Long arrivalCityId;

    /**
     * 距离(公里)
     */
    private Integer distance;

    /**
     * 基础价格
     */
    private BigDecimal basePrice;
} 