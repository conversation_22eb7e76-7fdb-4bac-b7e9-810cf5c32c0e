package com.flightprice.prediction.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.util.Date;

/**
 * 航空公司实体类
 */
@Data
@Entity
@Table(name = "airline")
@TableName("airline")
public class Airline {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 航空公司代码
     */
    private String code;

    /**
     * 航空公司名称
     */
    private String name;

    /**
     * 航空公司LOGO URL
     */
    private String logoUrl;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 航空公司英文名称 (虚拟字段，不存在于数据库中)
     */
    @Transient
    @TableField(exist = false)
    private String englishName;

    /**
     * 国家/地区 (虚拟字段，不存在于数据库中)
     */
    @Transient
    @TableField(exist = false)
    private String country;

    /**
     * 成立时间 (虚拟字段，不存在于数据库中)
     */
    @Transient
    @TableField(exist = false)
    private Integer foundedYear;

    /**
     * 总部所在地 (虚拟字段，不存在于数据库中)
     */
    @Transient
    @TableField(exist = false)
    private String headquarters;

    /**
     * 航空联盟 (虚拟字段，不存在于数据库中)
     */
    @Transient
    @TableField(exist = false)
    private String alliance;

    /**
     * 机队规模 (虚拟字段，不存在于数据库中)
     */
    @Transient
    @TableField(exist = false)
    private Integer fleetSize;

    /**
     * 航线数量 (虚拟字段，不存在于数据库中)
     */
    @Transient
    @TableField(exist = false)
    private Integer routesCount;

    /**
     * 描述 (虚拟字段，不存在于数据库中)
     */
    @Transient
    @TableField(exist = false)
    private String description;

    /**
     * 航空公司LOGO (虚拟字段，不存在于数据库中)
     */
    @Transient
    @TableField(exist = false)
    private String logo;

    /**
     * 官网 (虚拟字段，不存在于数据库中)
     */
    @Transient
    @TableField(exist = false)
    private String website;

    /**
     * 获取航空公司LOGO URL
     * @return LOGO URL
     */
    public String getLogoUrl() {
        return this.logoUrl;
    }
}