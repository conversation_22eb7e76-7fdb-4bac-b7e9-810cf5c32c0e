package com.flightprice.prediction.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.*;

/**
 * 航站楼实体类
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "terminals")
@TableName("terminals")
public class Terminal extends BaseEntity {
    
    /**
     * 航站楼编码
     */
    @Column(length = 20)
    private String terminalCode;

    /**
     * 航站楼名称
     */
    @Column(length = 50)
    private String name;

    /**
     * 所属机场ID
     */
    private Long airportId;

    /**
     * 航站楼类型
     */
    @Column(length = 20)
    private String type;

    /**
     * 登机口数量
     */
    private Integer gateCount;

    /**
     * 状态：0-停用，1-启用
     */
    private Integer status;

    /**
     * 备注
     */
    @Column(length = 500)
    private String remark;
    
    /**
     * 所属机场信息（非数据库字段）
     */
    @TableField(exist = false)
    @Transient
    private Airport airport;
} 