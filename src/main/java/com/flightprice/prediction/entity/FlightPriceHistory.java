package com.flightprice.prediction.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 航班价格历史实体类
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("flight_price_history")
public class FlightPriceHistory extends BaseEntity {

    /**
     * 航班ID
     */
    private Long flightId;

    /**
     * 价格
     */
    private BigDecimal price;

    /**
     * 舱位类型
     */
    private String cabinClass;

    /**
     * 折扣
     */
    private BigDecimal discount;

    /**
     * 可用座位数
     */
    private Integer availableSeats;

    /**
     * 可用座位百分比
     */
    private Double availableSeatsPercentage;

    /**
     * 数据收集时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date collectDate;

    /**
     * 距离起飞天数
     */
    private Integer daysBeforeDeparture;
}