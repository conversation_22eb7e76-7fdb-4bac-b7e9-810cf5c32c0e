package com.flightprice.prediction.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import javax.persistence.*;
import java.time.LocalDateTime;

/**
 * 机场实体类
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "airport")
@TableName("airport")
public class Airport extends BaseEntity {

    /**
     * 机场IATA代码（如PEK）
     */
    @Column(unique = true, nullable = false, length = 3)
    private String iataCode;
    
    /**
     * 机场四字码（ICAO代码）
     */
    @Column(unique = true, nullable = false, length = 4)
    private String icaoCode;
    
    /**
     * 机场名称
     */
    @Column(nullable = false, length = 100)
    private String name;
    
    /**
     * 机场英文名称
     */
    @Column(length = 100)
    private String englishName;
    
    /**
     * 所在城市
     */
    @Column(nullable = false, length = 50)
    private String city;
    
    /**
     * 所在城市ID
     */
    private Long cityId;
    
    /**
     * 所在国家/地区
     */
    @Column(length = 50)
    private String country;
    
    /**
     * 机场类型：国内/国际
     */
    @Column(length = 20)
    private String type;
    
    /**
     * 机场等级
     */
    @Column(length = 20)
    private String level;
    
    /**
     * 经度
     */
    private Double longitude;

    /**
     * 纬度
     */
    private Double latitude;

    /**
     * 海拔高度(米)
     */
    private Integer elevation;

    /**
     * 时区
     */
    @Column(length = 50)
    private String timezone;

    /**
     * 航站楼数量
     */
    private Integer terminalCount;

    /**
     * 跑道数量
     */
    private Integer runwayCount;

    /**
     * 年旅客吞吐量
     */
    private Long annualPassengers;
    
    /**
     * 状态：0-停用，1-启用
     */
    private Integer status;
    
    /**
     * 是否启用
     */
    private Boolean enabled = true;
    
    /**
     * 描述
     */
    @Column(length = 500)
    private String description;
    
    /**
     * 备注
     */
    @Column(length = 500)
    private String remark;
    
    /**
     * 机场图片
     */
    @Column(length = 200)
    private String image;

    /**
     * 官网
     */
    @Column(length = 200)
    private String website;

    /**
     * 获取机场代码 - 使用IATA三字码
     * @return 机场代码
     */
    @TableField(exist = false)
    public String getCode() {
        return this.iataCode;
    }
}