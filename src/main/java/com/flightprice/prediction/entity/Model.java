package com.flightprice.prediction.entity;

import lombok.Data;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import javax.persistence.*;
import java.time.LocalDateTime;

/**
 * 预测模型实体类
 */
@Data
@Entity
@Table(name = "models")
public class Model {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 模型名称
     */
    @Column(nullable = false, length = 100)
    private String name;

    /**
     * 模型类型
     */
    @Column(length = 50)
    private String type;

    /**
     * 模型描述
     */
    @Column(length = 500)
    private String description;

    /**
     * 模型参数，JSON格式
     */
    @Column(columnDefinition = "TEXT")
    private String parameters;

    /**
     * 模型特征，JSON格式
     */
    @Column(columnDefinition = "TEXT")
    private String features;

    /**
     * 模型版本
     */
    @Column(length = 30)
    private String version;

    /**
     * 模型状态（训练中/已训练/已部署/已停用）
     */
    @Column(length = 20)
    private String status;

    /**
     * 训练集大小
     */
    private Integer trainingSize;

    /**
     * 测试集大小
     */
    private Integer testSize;

    /**
     * 准确率
     */
    private Double accuracy;

    /**
     * 平均误差
     */
    private Double meanError;

    /**
     * 是否为当前使用的模型
     */
    private Boolean isActive = false;

    /**
     * 模型文件存储路径
     */
    @Column(length = 200)
    private String filePath;

    @CreationTimestamp
    private LocalDateTime createdAt;

    @UpdateTimestamp
    private LocalDateTime updatedAt;

    /**
     * 最后训练时间
     */
    private LocalDateTime lastTrainedAt;

    /**
     * 最后部署时间
     */
    private LocalDateTime lastDeployedAt;

    /**
     * 创建者ID
     */
    private Long createdBy;
} 