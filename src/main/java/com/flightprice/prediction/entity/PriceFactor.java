package com.flightprice.prediction.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 价格影响因素实体类
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("price_factor")
public class PriceFactor extends BaseEntity {

    /**
     * 航线ID
     */
    private Long routeId;

    /**
     * 因素名称
     */
    private String factorName;

    /**
     * 因素影响值
     */
    private BigDecimal factorValue;

    /**
     * 因素类型
     */
    private String factorType;

    /**
     * 开始日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date startDate;

    /**
     * 结束日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date endDate;
} 