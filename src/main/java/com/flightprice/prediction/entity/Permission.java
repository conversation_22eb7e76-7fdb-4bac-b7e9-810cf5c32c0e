package com.flightprice.prediction.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import javax.persistence.*;
import java.time.LocalDateTime;

/**
 * 权限实体类
 */
@Data
@Entity
@Table(name = "permissions")
@TableName("permissions")
public class Permission {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 权限名称
     */
    @Column(unique = true, nullable = false, length = 100)
    private String name;

    /**
     * 权限编码
     */
    @Column(length = 50)
    private String code;

    /**
     * 权限描述
     */
    @Column(length = 200)
    private String description;

    /**
     * 权限类型（menu-菜单，button-按钮，api-接口）
     */
    @Column(length = 50)
    private String category;

    @CreationTimestamp
    private LocalDateTime createdAt;

    @UpdateTimestamp
    private LocalDateTime updatedAt;

    /**
     * 父权限ID
     */
    private Long parentId;
}