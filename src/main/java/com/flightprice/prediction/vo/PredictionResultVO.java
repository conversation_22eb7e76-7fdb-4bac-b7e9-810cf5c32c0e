package com.flightprice.prediction.vo;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 价格预测结果视图对象
 */
@Data
public class PredictionResultVO {
    
    /**
     * 模型ID
     */
    private Long modelId;
    
    /**
     * 模型名称
     */
    private String modelName;
    
    /**
     * 预测价格
     */
    private BigDecimal predictedPrice;
    
    /**
     * 价格区间下限
     */
    private BigDecimal lowerBound;
    
    /**
     * 价格区间上限
     */
    private BigDecimal upperBound;
    
    /**
     * 置信度
     */
    private BigDecimal confidence;
    
    /**
     * 历史价格趋势（用于图表显示）
     */
    private List<PriceTrend> priceTrends;
    
    /**
     * 影响价格的关键因素
     */
    private Map<String, Object> keyFactors;
    
    /**
     * 预测使用的主要特征
     */
    private Map<String, Object> usedFeatures;
    
    /**
     * 各模型预测结果比较（多模型情况下）
     */
    private List<ModelComparison> modelComparisons;
    
    /**
     * 价格趋势类
     */
    @Data
    public static class PriceTrend {
        private String date;
        private BigDecimal price;
        private Integer passengerCount;
    }
    
    /**
     * 模型比较类
     */
    @Data
    public static class ModelComparison {
        private Long modelId;
        private String modelName;
        private String algorithmType;
        private BigDecimal predictedPrice;
        private BigDecimal accuracy;
    }
} 