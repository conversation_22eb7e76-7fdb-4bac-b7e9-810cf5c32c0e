package com.flightprice.prediction.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 价格预测视图对象
 */
@Data
public class PricePredictionVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 航班号
     */
    private String flightNumber;

    /**
     * 航空公司名称
     */
    private String airlineName;

    /**
     * 出发城市名称
     */
    private String departureCityName;

    /**
     * 到达城市名称
     */
    private String arrivalCityName;

    /**
     * 出发时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date departureTime;

    /**
     * 预测日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date predictDate;

    /**
     * 距离出发天数
     */
    private Integer daysBeforeDeparture;

    /**
     * 舱位等级
     */
    private String cabinClass;

    /**
     * 当前价格
     */
    private BigDecimal currentPrice;

    /**
     * 预测价格
     */
    private BigDecimal predictPrice;

    /**
     * 价格趋势（上涨/下降/稳定）
     */
    private String priceTrend;

    /**
     * 价格变化百分比
     */
    private BigDecimal changePercentage;

    /**
     * 建议（立即购买/等待/观望）
     */
    private String suggestion;

    /**
     * 置信度（0-1之间）
     */
    private BigDecimal confidence;
} 