package com.flightprice.prediction.vo;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 模型评估视图对象
 */
@Data
public class ModelEvaluationVO {
    
    /**
     * 模型ID
     */
    private Long modelId;
    
    /**
     * 模型名称
     */
    private String modelName;
    
    /**
     * 算法类型
     */
    private String algorithmType;
    
    /**
     * 准确率
     */
    private BigDecimal accuracy;
    
    /**
     * 平均绝对误差
     */
    private BigDecimal mae;
    
    /**
     * 均方误差
     */
    private BigDecimal mse;
    
    /**
     * 平均相对误差（百分比）
     */
    private BigDecimal mape;
    
    /**
     * R2分数
     */
    private BigDecimal r2Score;
    
    /**
     * 特征重要性
     */
    private Map<String, Double> featureImportance;
    
    /**
     * 预测vs实际价格（用于图表显示）
     */
    private List<PredictionPoint> predictionVsActual;
    
    /**
     * 残差分布（用于图表显示）
     */
    private List<Double> residuals;
    
    /**
     * 混淆矩阵
     */
    private List<List<Integer>> confusionMatrix;
    
    /**
     * 模型超参数
     */
    private Map<String, Object> hyperParameters;
    
    /**
     * 预测点类（实际价格和预测价格的配对）
     */
    @Data
    public static class PredictionPoint {
        private BigDecimal actual;
        private BigDecimal predicted;
        private String label;
    }
} 