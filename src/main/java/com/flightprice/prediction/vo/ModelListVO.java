package com.flightprice.prediction.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 模型列表视图对象
 */
@Data
public class ModelListVO {
    
    /**
     * 模型ID
     */
    private Long id;
    
    /**
     * 模型名称
     */
    private String modelName;
    
    /**
     * 算法类型
     */
    private String algorithmType;
    
    /**
     * 航线信息（出发城市-到达城市）
     */
    private String routeInfo;
    
    /**
     * 准确率
     */
    private BigDecimal accuracy;
    
    /**
     * 平均绝对误差
     */
    private BigDecimal mae;
    
    /**
     * 均方误差
     */
    private BigDecimal mse;
    
    /**
     * 最后训练时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date lastTrained;
    
    /**
     * 是否激活
     */
    private Boolean isActive;
    
    /**
     * 模型状态（训练中、可用、训练失败）
     */
    private String status;
} 