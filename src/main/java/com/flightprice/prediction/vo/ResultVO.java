package com.flightprice.prediction.vo;

import lombok.Data;

import java.io.Serializable;

/**
 * 通用结果视图对象
 */
@Data
public class ResultVO<T> implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 状态码
     */
    private Integer code;

    /**
     * 消息
     */
    private String message;

    /**
     * 数据
     */
    private T data;

    /**
     * 是否成功
     */
    private Boolean success;

    /**
     * 私有构造函数，通过静态方法创建实例
     */
    private ResultVO() {
    }

    /**
     * 成功结果
     *
     * @param <T> 数据类型
     * @return ResultVO对象
     */
    public static <T> ResultVO<T> success() {
        return success(null);
    }

    /**
     * 成功结果
     *
     * @param data 数据
     * @param <T>  数据类型
     * @return ResultVO对象
     */
    public static <T> ResultVO<T> success(T data) {
        return success("操作成功", data);
    }

    /**
     * 成功结果
     *
     * @param message 消息
     * @param data    数据
     * @param <T>     数据类型
     * @return ResultVO对象
     */
    public static <T> ResultVO<T> success(String message, T data) {
        ResultVO<T> result = new ResultVO<>();
        result.code = 200;
        result.message = message;
        result.data = data;
        result.success = true;
        return result;
    }

    /**
     * 失败结果
     *
     * @param <T> 数据类型
     * @return ResultVO对象
     */
    public static <T> ResultVO<T> error() {
        return error("操作失败");
    }

    /**
     * 失败结果
     *
     * @param message 消息
     * @param <T>     数据类型
     * @return ResultVO对象
     */
    public static <T> ResultVO<T> error(String message) {
        return error(500, message);
    }

    /**
     * 失败结果
     *
     * @param code    状态码
     * @param message 消息
     * @param <T>     数据类型
     * @return ResultVO对象
     */
    public static <T> ResultVO<T> error(Integer code, String message) {
        ResultVO<T> result = new ResultVO<>();
        result.code = code;
        result.message = message;
        result.data = null;
        result.success = false;
        return result;
    }
}
