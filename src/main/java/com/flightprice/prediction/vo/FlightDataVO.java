package com.flightprice.prediction.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 航班数据VO
 */
@Data
public class FlightDataVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    private Long id;

    /**
     * 出发城市
     */
    private String departureCity;

    /**
     * 到达城市
     */
    private String arrivalCity;

    /**
     * 里程
     */
    private Integer mileage;

    /**
     * 航班号
     */
    private String flightNumber;

    /**
     * 航空公司
     */
    private String airline;

    /**
     * 飞机类型
     */
    private String aircraftType;

    /**
     * 出发机场
     */
    private String departureAirport;

    /**
     * 到达机场
     */
    private String arrivalAirport;

    /**
     * 出发时间
     */
    @JsonFormat(pattern = "HH:mm", timezone = "GMT+8")
    private Date departureTime;
    
    /**
     * 出发时间字符串
     */
    private String departureTimeStr;

    /**
     * 到达时间
     */
    @JsonFormat(pattern = "HH:mm", timezone = "GMT+8")
    private Date arrivalTime;
    
    /**
     * 到达时间字符串
     */
    private String arrivalTimeStr;

    /**
     * 飞行日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date flightDate;
    
    /**
     * 飞行日期字符串
     */
    private String flightDateStr;

    /**
     * 价格
     */
    private BigDecimal price;

    /**
     * 乘客数量
     */
    private Integer passengerCount;

    /**
     * 航班准点率
     */
    private Double punctualityRate;

    /**
     * 平均延误时间
     */
    private String averageDelayTime;
    
    /**
     * 计算的飞行时间（分钟）
     */
    private Integer duration;
    
    /**
     * 格式化的飞行时间（如：1h 45m）
     */
    private String durationStr;
} 