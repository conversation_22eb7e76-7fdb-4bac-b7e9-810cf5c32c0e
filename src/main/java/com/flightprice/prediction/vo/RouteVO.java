package com.flightprice.prediction.vo;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 航线视图对象
 */
@Data
public class RouteVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 航线ID
     */
    private Long id;

    /**
     * 出发城市ID
     */
    private Long departureCityId;

    /**
     * 到达城市ID
     */
    private Long arrivalCityId;

    /**
     * 出发城市名称
     */
    private String departureCityName;

    /**
     * 到达城市名称
     */
    private String arrivalCityName;

    /**
     * 距离(公里)
     */
    private Integer distance;

    /**
     * 基础价格
     */
    private BigDecimal basePrice;
} 