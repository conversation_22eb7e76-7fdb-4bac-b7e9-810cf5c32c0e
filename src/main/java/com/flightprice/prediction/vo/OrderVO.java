package com.flightprice.prediction.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 订单视图对象
 */
@Data
public class OrderVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 订单ID
     */
    private Long id;

    /**
     * 订单编号
     */
    private String orderNo;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 机票ID
     */
    private Long ticketId;

    /**
     * 航班号
     */
    private String flightNumber;

    /**
     * 航空公司名称
     */
    private String airlineName;

    /**
     * 航空公司logo
     */
    private String airlineLogo;

    /**
     * 出发城市名称
     */
    private String departureCityName;

    /**
     * 出发机场名称
     */
    private String departureAirportName;

    /**
     * 到达城市名称
     */
    private String arrivalCityName;

    /**
     * 到达机场名称
     */
    private String arrivalAirportName;

    /**
     * 出发时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date departureTime;

    /**
     * 到达时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date arrivalTime;

    /**
     * 舱位等级
     */
    private String cabinClass;

    /**
     * 乘客姓名
     */
    private String passengerName;

    /**
     * 乘客身份证号
     */
    private String passengerIdCard;

    /**
     * 乘客手机号
     */
    private String passengerPhone;

    /**
     * 订单状态
     */
    private String status;

    /**
     * 订单总金额
     */
    private BigDecimal totalAmount;

    /**
     * 支付时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date paymentTime;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;
} 