package com.flightprice.prediction.vo;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 机场视图对象
 */
@Data
public class AirportVO {
    
    private Long id;
    private String iataCode;
    private String icaoCode;
    private String name;
    private String englishName;
    private String city;
    private String country;
    private Double longitude;
    private Double latitude;
    private Integer elevation;
    private String timezone;
    private Integer terminalCount;
    private Integer runwayCount;
    private Long annualPassengers;
    private String description;
    private String image;
    private String website;
    private Boolean enabled;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
    
    // 统计数据
    private Integer flightsCount;
    private Integer routesCount;
    private Integer airlinesCount;
    private Double avgDelay;
} 