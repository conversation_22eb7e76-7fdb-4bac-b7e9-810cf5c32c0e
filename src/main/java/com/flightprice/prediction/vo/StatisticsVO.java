package com.flightprice.prediction.vo;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 统计视图对象
 */
@Data
public class StatisticsVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 总订单数
     */
    private Integer totalOrders;
    
    /**
     * 总订单数 (前端使用)
     */
    private Integer orderCount;

    /**
     * 订单增长率
     */
    private Integer orderIncrease;

    /**
     * 总收入
     */
    private BigDecimal totalRevenue;
    
    /**
     * 总收入 (前端使用)
     */
    private BigDecimal totalSales;

    /**
     * 销售额增长率
     */
    private Integer salesIncrease;

    /**
     * 总用户数
     */
    private Integer totalUsers;
    
    /**
     * 总用户数 (前端使用)
     */
    private Integer userCount;

    /**
     * 用户增长率
     */
    private Integer userIncrease;

    /**
     * 总航班数
     */
    private Integer totalFlights;
    
    /**
     * 总航班数 (前端使用)
     */
    private Integer flightCount;

    /**
     * 航班增长率
     */
    private Integer flightIncrease;
    
    /**
     * 活跃航线数
     */
    private Integer routeCount;
    
    /**
     * 活跃航线增长率
     */
    private Integer routeIncrease;
    
    /**
     * 平均票价
     */
    private BigDecimal avgPrice;
    
    /**
     * 平均票价增长率
     */
    private Integer priceIncrease;

    /**
     * 各航线订单量TOP10
     */
    private List<Map<String, Object>> topRoutes;

    /**
     * 各城市订单量
     */
    private List<Map<String, Object>> cityOrders;

    /**
     * 各舱位销售占比
     */
    private List<Map<String, Object>> cabinSales;

    /**
     * 近30天每日订单量走势
     */
    private List<Map<String, Object>> orderTrend;

    /**
     * 近30天每日收入走势
     */
    private List<Map<String, Object>> revenueTrend;

    /**
     * 各航空公司订单占比
     */
    private List<Map<String, Object>> airlineOrders;

    /**
     * 价格因素影响分析
     */
    private List<Map<String, Object>> priceFactors;

    /**
     * 预测模型准确率统计
     */
    private List<Map<String, Object>> modelAccuracy;
} 