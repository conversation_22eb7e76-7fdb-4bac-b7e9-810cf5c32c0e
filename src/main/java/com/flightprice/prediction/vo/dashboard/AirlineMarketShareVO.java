package com.flightprice.prediction.vo.dashboard;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 航空公司市场份额VO
 */
@Data
public class AirlineMarketShareVO {
    
    /**
     * 航空公司市场份额列表
     */
    private List<AirlineShare> airlineShares;
    
    /**
     * 总航空公司数量
     */
    private Integer totalAirlines;
    
    /**
     * 市场集中度
     */
    private BigDecimal marketConcentration;
    
    /**
     * 航空公司市场份额项
     */
    @Data
    public static class AirlineShare {
        /**
         * 航空公司ID
         */
        private Long airlineId;
        
        /**
         * 航空公司名称
         */
        private String airlineName;
        
        /**
         * 航空公司代码
         */
        private String airlineCode;
        
        /**
         * 市场份额（百分比）
         */
        private BigDecimal marketShare;
        
        /**
         * 旅客数量
         */
        private Integer passengerCount;
        
        /**
         * 航班数量
         */
        private Integer flightCount;
        
        /**
         * 平均票价
         */
        private BigDecimal avgPrice;
        
        /**
         * 同比增长率
         */
        private BigDecimal yearOverYearGrowth;
    }
} 