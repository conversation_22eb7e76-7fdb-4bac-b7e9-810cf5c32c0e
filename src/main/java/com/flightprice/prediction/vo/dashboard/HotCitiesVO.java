package com.flightprice.prediction.vo.dashboard;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 热门城市排行VO
 */
@Data
public class HotCitiesVO {
    
    /**
     * 热门出发城市列表
     */
    private List<HotCity> departureCities;
    
    /**
     * 热门到达城市列表
     */
    private List<HotCity> arrivalCities;
    
    /**
     * 总城市数量
     */
    private Integer totalCities;
    
    /**
     * 热门城市项
     */
    @Data
    public static class HotCity {
        /**
         * 城市ID
         */
        private Long cityId;
        
        /**
         * 城市名称
         */
        private String cityName;
        
        /**
         * 所在省份
         */
        private String province;
        
        /**
         * 航班数量
         */
        private Integer flightCount;
        
        /**
         * 旅客数量
         */
        private Integer passengerCount;
        
        /**
         * 平均票价
         */
        private BigDecimal avgPrice;
        
        /**
         * 热度得分
         */
        private BigDecimal heatScore;
        
        /**
         * 同比增长率
         */
        private BigDecimal yearOverYearGrowth;
        
        /**
         * 城市坐标X
         */
        private Double coordinateX;
        
        /**
         * 城市坐标Y
         */
        private Double coordinateY;
    }
} 