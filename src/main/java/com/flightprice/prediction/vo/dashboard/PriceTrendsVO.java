package com.flightprice.prediction.vo.dashboard;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 价格趋势数据VO
 */
@Data
public class PriceTrendsVO {
    
    /**
     * 时间范围标签（例如："最近30天"、"最近12个月"）
     */
    private String timeRangeLabel;
    
    /**
     * 趋势线数据
     */
    private List<TrendLine> trendLines;
    
    /**
     * 最高价格
     */
    private BigDecimal highestPrice;
    
    /**
     * 最低价格
     */
    private BigDecimal lowestPrice;
    
    /**
     * 平均价格
     */
    private BigDecimal averagePrice;
    
    /**
     * 价格波动率
     */
    private BigDecimal volatility;
    
    /**
     * 趋势线数据项
     */
    @Data
    public static class TrendLine {
        /**
         * 线条名称（例如航线名或航空公司名）
         */
        private String name;
        
        /**
         * 数据点
         */
        private List<DataPoint> dataPoints;
    }
    
    /**
     * 数据点
     */
    @Data
    public static class DataPoint {
        /**
         * 日期标签
         */
        private String date;
        
        /**
         * 价格值
         */
        private BigDecimal price;
        
        /**
         * 乘客数
         */
        private Integer passengerCount;
    }
} 