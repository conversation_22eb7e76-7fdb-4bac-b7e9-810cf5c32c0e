package com.flightprice.prediction.vo.dashboard;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 价格区间分布VO
 */
@Data
public class PriceDistributionVO {

    /**
     * 价格区间列表
     */
    private List<String> priceRanges;

    /**
     * 经济舱各价格区间的航班数量
     */
    private List<Integer> economyCounts;

    /**
     * 商务舱各价格区间的航班数量
     */
    private List<Integer> businessCounts;

    /**
     * 头等舱各价格区间的航班数量
     */
    private List<Integer> firstCounts;

    /**
     * 最低价格
     */
    private BigDecimal minPrice;

    /**
     * 最高价格
     */
    private BigDecimal maxPrice;

    /**
     * 均价
     */
    private BigDecimal avgPrice;

    /**
     * 中位数
     */
    private BigDecimal medianPrice;

    /**
     * 标准差
     */
    private BigDecimal stdDeviation;

    /**
     * 价格区间
     */
    @Data
    public static class PriceRange {
        /**
         * 区间起始价格
         */
        private BigDecimal minPrice;

        /**
         * 区间结束价格
         */
        private BigDecimal maxPrice;

        /**
         * 区间标签（例如"0-500元"）
         */
        private String label;

        /**
         * 该价格区间的航班数量
         */
        private Integer flightCount;

        /**
         * 该价格区间的航班占比
         */
        private BigDecimal percentage;
    }
}