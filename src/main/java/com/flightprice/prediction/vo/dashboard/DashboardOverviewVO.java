package com.flightprice.prediction.vo.dashboard;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 数据大屏顶部概览数据VO
 */
@Data
public class DashboardOverviewVO {
    
    /**
     * 航班总数
     */
    private Integer totalFlights;
    
    /**
     * 今日航班数
     */
    private Integer todayFlights;
    
    /**
     * 累计旅客总数
     */
    private Integer totalPassengers;
    
    /**
     * 今日旅客数
     */
    private Integer todayPassengers;
    
    /**
     * 航线总数
     */
    private Integer totalRoutes;
    
    /**
     * 平均客座率
     */
    private BigDecimal avgLoadFactor;
    
    /**
     * 平均票价
     */
    private BigDecimal avgTicketPrice;
    
    /**
     * 票价涨幅
     */
    private BigDecimal priceIncreaseRate;
    
    /**
     * 航班准点率
     */
    private BigDecimal punctualityRate;
    
    /**
     * 系统预测准确率
     */
    private BigDecimal predictionAccuracy;
    
    /**
     * 活跃航空公司数
     */
    private Integer activeAirlines;
    
    /**
     * 共涉及城市数
     */
    private Integer totalCities;
    
    /**
     * 总收入
     */
    private BigDecimal totalRevenue;
    
    /**
     * 收入增长率
     */
    private Integer revenueIncrease;
    
    /**
     * 航班增长率
     */
    private Integer flightIncrease;
    
    /**
     * 旅客增长率
     */
    private Integer passengerIncrease;
} 