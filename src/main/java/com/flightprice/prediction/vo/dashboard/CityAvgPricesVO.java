package com.flightprice.prediction.vo.dashboard;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 各城市平均价格VO
 */
@Data
public class CityAvgPricesVO {
    
    /**
     * 查询类型（出发/到达）
     */
    private String type;
    
    /**
     * 城市价格列表
     */
    private List<CityPrice> cityPrices;
    
    /**
     * 最高价格城市
     */
    private String highestPriceCity;
    
    /**
     * 最高平均价格
     */
    private BigDecimal highestAvgPrice;
    
    /**
     * 最低价格城市
     */
    private String lowestPriceCity;
    
    /**
     * 最低平均价格
     */
    private BigDecimal lowestAvgPrice;
    
    /**
     * 城市价格项
     */
    @Data
    public static class CityPrice {
        /**
         * 城市ID
         */
        private Long cityId;
        
        /**
         * 城市名称
         */
        private String cityName;
        
        /**
         * 所在省份
         */
        private String province;
        
        /**
         * 平均价格
         */
        private BigDecimal avgPrice;
        
        /**
         * 最低价格
         */
        private BigDecimal minPrice;
        
        /**
         * 最高价格
         */
        private BigDecimal maxPrice;
        
        /**
         * 航班数量
         */
        private Integer flightCount;
        
        /**
         * 城市坐标X
         */
        private Double coordinateX;
        
        /**
         * 城市坐标Y
         */
        private Double coordinateY;
    }
} 