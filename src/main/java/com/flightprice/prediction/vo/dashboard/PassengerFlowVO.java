package com.flightprice.prediction.vo.dashboard;

import lombok.Data;

import java.util.List;

/**
 * 客流量统计VO
 */
@Data
public class PassengerFlowVO {
    
    /**
     * 时间维度（日、周、月、年）
     */
    private String timeDimension;
    
    /**
     * 客流量数据
     */
    private List<FlowData> flowData;
    
    /**
     * 高峰期间
     */
    private String peakPeriod;
    
    /**
     * 高峰期客流量
     */
    private Integer peakVolume;
    
    /**
     * 低谷期间
     */
    private String valleyPeriod;
    
    /**
     * 低谷期客流量
     */
    private Integer valleyVolume;
    
    /**
     * 总客流量
     */
    private Integer totalVolume;
    
    /**
     * 平均客流量
     */
    private Integer averageVolume;
    
    /**
     * 同比增长率
     */
    private Double yearOverYearGrowth;
    
    /**
     * 环比增长率
     */
    private Double monthOverMonthGrowth;
    
    /**
     * 流量数据项
     */
    @Data
    public static class FlowData {
        /**
         * 日期标签
         */
        private String dateLabel;
        
        /**
         * 客流量
         */
        private Integer passengerCount;
        
        /**
         * 航班数
         */
        private Integer flightCount;
    }
} 