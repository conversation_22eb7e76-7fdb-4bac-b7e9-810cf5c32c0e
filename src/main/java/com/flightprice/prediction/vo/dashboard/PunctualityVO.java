package com.flightprice.prediction.vo.dashboard;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 航班准点率统计VO
 */
@Data
public class PunctualityVO {
    
    /**
     * 航空公司准点率列表
     */
    private List<AirlinePunctuality> airlinePunctualities;
    
    /**
     * 整体准点率
     */
    private BigDecimal overallPunctualityRate;
    
    /**
     * 最高准点率
     */
    private BigDecimal highestPunctualityRate;
    
    /**
     * 最高准点率航空公司
     */
    private String highestPunctualityAirline;
    
    /**
     * 最低准点率
     */
    private BigDecimal lowestPunctualityRate;
    
    /**
     * 最低准点率航空公司
     */
    private String lowestPunctualityAirline;
    
    /**
     * 按时段统计的准点率
     */
    private List<TimeSlotPunctuality> timeSlotPunctualities;
    
    /**
     * 航空公司准点率项
     */
    @Data
    public static class AirlinePunctuality {
        /**
         * 航空公司ID
         */
        private Long airlineId;
        
        /**
         * 航空公司名称
         */
        private String airlineName;
        
        /**
         * 准点率
         */
        private BigDecimal punctualityRate;
        
        /**
         * 平均延误时间（分钟）
         */
        private Integer avgDelayTime;
        
        /**
         * 航班总数
         */
        private Integer totalFlights;
        
        /**
         * 准点航班数
         */
        private Integer punctualFlights;
    }
    
    /**
     * 时段准点率项
     */
    @Data
    public static class TimeSlotPunctuality {
        /**
         * 时段（如"06:00-08:00"）
         */
        private String timeSlot;
        
        /**
         * 准点率
         */
        private BigDecimal punctualityRate;
        
        /**
         * 航班数量
         */
        private Integer flightCount;
    }
} 