package com.flightprice.prediction.vo.dashboard;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 预测准确率统计VO
 */
@Data
public class PredictionAccuracyVO {

    /**
     * 整体预测准确率
     */
    private BigDecimal overallAccuracy;

    /**
     * 平均绝对误差
     */
    private BigDecimal mae;

    /**
     * 均方误差
     */
    private BigDecimal mse;

    /**
     * 平均相对误差
     */
    private BigDecimal mape;

    /**
     * 时间范围列表
     */
    private List<String> timeRanges;

    /**
     * 准确率列表
     */
    private List<BigDecimal> accuracyRates;

    /**
     * 误差率列表
     */
    private List<BigDecimal> errorRates;

    /**
     * 平均准确率
     */
    private BigDecimal averageAccuracy;

    /**
     * 算法准确率列表
     */
    private List<AlgorithmAccuracy> algorithmAccuracy;

    /**
     * 各模型准确率列表
     */
    private List<ModelAccuracy> modelAccuracies;

    /**
     * 航线准确率列表
     */
    private List<RouteAccuracy> routeAccuracies;

    /**
     * 时间段准确率
     */
    private List<TimeRangeAccuracy> timeRangeAccuracies;

    /**
     * 价格区间准确率
     */
    private List<PriceRangeAccuracy> priceRangeAccuracies;

    /**
     * 模型准确率项
     */
    @Data
    public static class ModelAccuracy {
        /**
         * 模型ID
         */
        private Long modelId;

        /**
         * 模型名称
         */
        private String modelName;

        /**
         * 算法类型
         */
        private String algorithmType;

        /**
         * 准确率
         */
        private BigDecimal accuracy;

        /**
         * 平均绝对误差
         */
        private BigDecimal mae;

        /**
         * 均方误差
         */
        private BigDecimal mse;

        /**
         * 适用航线
         */
        private String applicableRoute;
    }

    /**
     * 航线准确率项
     */
    @Data
    public static class RouteAccuracy {
        /**
         * 航线ID
         */
        private Long routeId;

        /**
         * 航线名称
         */
        private String routeName;

        /**
         * 准确率
         */
        private BigDecimal accuracy;

        /**
         * 平均绝对误差
         */
        private BigDecimal mae;

        /**
         * 样本数量
         */
        private Integer sampleCount;
    }

    /**
     * 时间段准确率项
     */
    @Data
    public static class TimeRangeAccuracy {
        /**
         * 时间段标签
         */
        private String timeRange;

        /**
         * 准确率
         */
        private BigDecimal accuracy;

        /**
         * 预测样本数
         */
        private Integer sampleCount;
    }

    /**
     * 价格区间准确率项
     */
    @Data
    public static class PriceRangeAccuracy {
        /**
         * 价格区间标签
         */
        private String priceRange;

        /**
         * 准确率
         */
        private BigDecimal accuracy;

        /**
         * 预测样本数
         */
        private Integer sampleCount;
    }

    /**
     * 算法准确率项
     */
    @Data
    public static class AlgorithmAccuracy {
        /**
         * 算法名称
         */
        private String algorithmName;

        /**
         * 准确率
         */
        private BigDecimal accuracy;
    }
}