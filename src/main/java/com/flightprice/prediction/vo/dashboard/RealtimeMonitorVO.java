package com.flightprice.prediction.vo.dashboard;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 实时数据监控VO
 */
@Data
public class RealtimeMonitorVO {
    
    /**
     * 当前在线用户数
     */
    private Integer onlineUsers;
    
    /**
     * 今日访问量
     */
    private Integer todayVisits;
    
    /**
     * 今日预测次数
     */
    private Integer todayPredictions;
    
    /**
     * 今日订单数
     */
    private Integer todayOrders;
    
    /**
     * 系统响应时间(ms)
     */
    private Integer responseTime;
    
    /**
     * CPU使用率
     */
    private BigDecimal cpuUsage;
    
    /**
     * 内存使用率
     */
    private BigDecimal memoryUsage;
    
    /**
     * 磁盘使用率
     */
    private BigDecimal diskUsage;
    
    /**
     * 最近API调用
     */
    private List<ApiCall> recentApiCalls;
    
    /**
     * 活跃城市列表
     */
    private List<ActiveCity> activeCities;
    
    /**
     * 热搜航线
     */
    private List<HotSearch> hotSearches;
    
    /**
     * API调用项
     */
    @Data
    public static class ApiCall {
        /**
         * API路径
         */
        private String path;
        
        /**
         * 调用时间
         */
        private String time;
        
        /**
         * 响应时间(ms)
         */
        private Integer responseTime;
        
        /**
         * 状态码
         */
        private Integer statusCode;
        
        /**
         * 客户端IP
         */
        private String clientIp;
    }
    
    /**
     * 活跃城市项
     */
    @Data
    public static class ActiveCity {
        /**
         * 城市名称
         */
        private String cityName;
        
        /**
         * 搜索次数
         */
        private Integer searchCount;
        
        /**
         * 活跃用户数
         */
        private Integer activeUsers;
    }
    
    /**
     * 热搜航线项
     */
    @Data
    public static class HotSearch {
        /**
         * 航线名称
         */
        private String routeName;
        
        /**
         * 搜索次数
         */
        private Integer searchCount;
        
        /**
         * 平均价格
         */
        private BigDecimal avgPrice;
    }
} 