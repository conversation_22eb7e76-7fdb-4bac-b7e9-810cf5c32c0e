package com.flightprice.prediction.vo.dashboard;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 地图数据VO
 */
@Data
public class MapDataVO {
    
    /**
     * 城市节点列表
     */
    private List<CityNode> cities;
    
    /**
     * 航线连接列表
     */
    private List<RouteConnection> routes;
    
    /**
     * 城市节点
     */
    @Data
    public static class CityNode {
        /**
         * 城市ID
         */
        private Long cityId;
        
        /**
         * 城市名称
         */
        private String cityName;
        
        /**
         * 城市所在省份
         */
        private String province;
        
        /**
         * 经度
         */
        private Double longitude;
        
        /**
         * 纬度
         */
        private Double latitude;
        
        /**
         * 节点大小（基于航班数量）
         */
        private Integer size;
        
        /**
         * 航班数量
         */
        private Integer flightCount;
        
        /**
         * 旅客数量
         */
        private Integer passengerCount;
    }
    
    /**
     * 航线连接
     */
    @Data
    public static class RouteConnection {
        /**
         * 出发城市ID
         */
        private Long sourceCityId;
        
        /**
         * 出发城市名称
         */
        private String sourceCity;
        
        /**
         * 到达城市ID
         */
        private Long targetCityId;
        
        /**
         * 到达城市名称
         */
        private String targetCity;
        
        /**
         * 航线ID
         */
        private Long routeId;
        
        /**
         * 航班数量
         */
        private Integer flightCount;
        
        /**
         * 旅客数量
         */
        private Integer passengerCount;
        
        /**
         * 平均票价
         */
        private BigDecimal avgPrice;
        
        /**
         * 连线宽度（基于航班数量）
         */
        private Integer width;
    }
} 