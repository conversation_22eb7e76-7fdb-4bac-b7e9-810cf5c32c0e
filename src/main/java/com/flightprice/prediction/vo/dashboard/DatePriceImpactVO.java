package com.flightprice.prediction.vo.dashboard;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 日期因素对价格的影响VO
 */
@Data
public class DatePriceImpactVO {
    
    /**
     * 周几对价格的影响
     */
    private List<DayOfWeekImpact> dayOfWeekImpacts;
    
    /**
     * 月份对价格的影响
     */
    private List<MonthImpact> monthImpacts;
    
    /**
     * 节假日对价格的影响
     */
    private List<HolidayImpact> holidayImpacts;
    
    /**
     * 提前预订天数对价格的影响
     */
    private List<AdvanceBookingImpact> advanceBookingImpacts;
    
    /**
     * 周几价格影响项
     */
    @Data
    public static class DayOfWeekImpact {
        /**
         * 周几（1-7，1代表周一）
         */
        private Integer dayOfWeek;
        
        /**
         * 周几名称
         */
        private String dayName;
        
        /**
         * 平均价格
         */
        private BigDecimal avgPrice;
        
        /**
         * 价格影响系数（相对于整体均价的比率）
         */
        private BigDecimal impactFactor;
        
        /**
         * 航班数量
         */
        private Integer flightCount;
    }
    
    /**
     * 月份价格影响项
     */
    @Data
    public static class MonthImpact {
        /**
         * 月份（1-12）
         */
        private Integer month;
        
        /**
         * 月份名称
         */
        private String monthName;
        
        /**
         * 平均价格
         */
        private BigDecimal avgPrice;
        
        /**
         * 价格影响系数（相对于整体均价的比率）
         */
        private BigDecimal impactFactor;
        
        /**
         * 航班数量
         */
        private Integer flightCount;
    }
    
    /**
     * 节假日价格影响项
     */
    @Data
    public static class HolidayImpact {
        /**
         * 节假日名称
         */
        private String holidayName;
        
        /**
         * 平均价格
         */
        private BigDecimal avgPrice;
        
        /**
         * 价格影响系数（相对于非节假日的比率）
         */
        private BigDecimal impactFactor;
        
        /**
         * 数据样本数
         */
        private Integer sampleCount;
    }
    
    /**
     * 提前预订天数价格影响项
     */
    @Data
    public static class AdvanceBookingImpact {
        /**
         * 提前预订天数
         */
        private Integer daysInAdvance;
        
        /**
         * 平均价格
         */
        private BigDecimal avgPrice;
        
        /**
         * 价格影响系数
         */
        private BigDecimal impactFactor;
        
        /**
         * 数据样本数
         */
        private Integer sampleCount;
    }
} 