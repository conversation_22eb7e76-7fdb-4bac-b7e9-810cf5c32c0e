package com.flightprice.prediction.vo.dashboard;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 航线热度数据VO
 */
@Data
public class RouteHeatVO {
    
    /**
     * 热门航线列表
     */
    private List<RouteHeat> hotRoutes;
    
    /**
     * 航线总数
     */
    private Integer totalRoutes;
    
    /**
     * 最高客流量航线
     */
    private String highestVolumeRoute;
    
    /**
     * 最高客流量
     */
    private Integer highestVolume;
    
    /**
     * 最高价格航线
     */
    private String highestPriceRoute;
    
    /**
     * 最高价格
     */
    private BigDecimal highestPrice;
    
    /**
     * 航线热度项
     */
    @Data
    public static class RouteHeat {
        /**
         * 航线ID
         */
        private Long routeId;
        
        /**
         * 出发城市
         */
        private String departureCity;
        
        /**
         * 到达城市
         */
        private String arrivalCity;
        
        /**
         * 航线全称（出发城市-到达城市）
         */
        private String routeName;
        
        /**
         * 平均票价
         */
        private BigDecimal avgPrice;
        
        /**
         * 客流量
         */
        private Integer passengerVolume;
        
        /**
         * 航班频次
         */
        private Integer flightFrequency;
        
        /**
         * 热度得分
         */
        private BigDecimal heatScore;
        
        /**
         * 票价增长率
         */
        private BigDecimal priceGrowthRate;
        
        /**
         * 平均客座率
         */
        private BigDecimal avgLoadFactor;
    }
} 