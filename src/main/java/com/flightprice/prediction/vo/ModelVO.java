package com.flightprice.prediction.vo;

import lombok.Data;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * 预测模型视图对象
 */
@Data
public class ModelVO {

    private Long id;
    private String name;
    private String type;
    private String description;
    private Map<String, Object> parameters;
    private String[] features;
    private String version;
    private String status;
    private Integer trainingSize;
    private Integer testSize;
    private Double accuracy;
    private Double meanError;
    private Boolean isActive;
    private String filePath;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
    private LocalDateTime lastTrainedAt;
    private LocalDateTime lastDeployedAt;
    private Long createdBy;
    private String createdByName;
    
    // 统计数据
    private Integer trainingCount;
    private Double averageAccuracy;
    private Double bestAccuracy;
    private Double worstAccuracy;
    private LocalDateTime lastTrainingTime;
} 