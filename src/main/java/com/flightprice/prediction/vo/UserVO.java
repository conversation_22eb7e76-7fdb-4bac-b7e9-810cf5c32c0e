package com.flightprice.prediction.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.Set;

/**
 * 用户视图对象
 */
@Data
@ApiModel("用户视图对象")
public class UserVO {

    @ApiModelProperty("用户ID")
    private Long id;

    @ApiModelProperty("用户名")
    private String username;

    @ApiModelProperty("昵称")
    private String fullName;

    @ApiModelProperty("邮箱")
    private String email;

    @ApiModelProperty("手机号")
    private String phone;

    @ApiModelProperty("头像URL")
    private String avatar;

    @ApiModelProperty("角色")
    private Set<RoleVO> roles;

    @ApiModelProperty("创建时间")
    private LocalDateTime createdAt;

    @ApiModelProperty("最后登录时间")
    private LocalDateTime lastLogin;

    @ApiModelProperty("描述")
    private String description;

    @ApiModelProperty("是否启用")
    private Boolean enabled;

    @ApiModelProperty("更新时间")
    private LocalDateTime updatedAt;

    @Data
    public static class RoleVO {
        @ApiModelProperty("角色ID")
        private Long id;

        @ApiModelProperty("角色名称")
        private String name;
    }
}
