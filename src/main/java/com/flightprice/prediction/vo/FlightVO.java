package com.flightprice.prediction.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 航班视图对象
 */
@Data
public class FlightVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 航班ID
     */
    private Long id;

    /**
     * 航班号
     */
    private String flightNumber;

    /**
     * 航空公司ID
     */
    private Long airlineId;

    /**
     * 航空公司名称
     */
    private String airlineName;

    /**
     * 航空公司代码
     */
    private String airlineCode;

    /**
     * 航空公司logo
     */
    private String airlineLogo;

    /**
     * 出发城市ID
     */
    private Long departureCityId;

    /**
     * 出发城市名称
     */
    private String departureCityName;

    /**
     * 出发机场ID
     */
    private Long departureAirportId;

    /**
     * 出发机场名称
     */
    private String departureAirportName;

    /**
     * 出发机场代码
     */
    private String departureAirportCode;

    /**
     * 到达城市ID
     */
    private Long arrivalCityId;

    /**
     * 到达城市名称
     */
    private String arrivalCityName;

    /**
     * 到达机场ID
     */
    private Long arrivalAirportId;

    /**
     * 到达机场名称
     */
    private String arrivalAirportName;

    /**
     * 到达机场代码
     */
    private String arrivalAirportCode;

    /**
     * 出发时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date departureTime;

    /**
     * 到达时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date arrivalTime;

    /**
     * 飞行时长（分钟）
     */
    private Integer duration;

    /**
     * 飞机类型
     */
    private String aircraftType;

    /**
     * 状态
     */
    private String status;

    /**
     * 经济舱价格
     */
    private BigDecimal economyPrice;

    /**
     * 商务舱价格
     */
    private BigDecimal businessPrice;

    /**
     * 头等舱价格
     */
    private BigDecimal firstPrice;

    /**
     * 经济舱剩余座位数
     */
    private Integer economySeats;

    /**
     * 商务舱剩余座位数
     */
    private Integer businessSeats;

    /**
     * 头等舱剩余座位数
     */
    private Integer firstSeats;

    /**
     * 经济舱机票ID
     */
    private Long economyTicketId;

    /**
     * 商务舱机票ID
     */
    private Long businessTicketId;

    /**
     * 头等舱机票ID
     */
    private Long firstTicketId;

    /**
     * 当前舱位价格（根据舱位等级决定）
     */
    private BigDecimal price;

    /**
     * 当前舱位可用座位数（根据舱位等级决定）
     */
    private Integer availableSeats;
}