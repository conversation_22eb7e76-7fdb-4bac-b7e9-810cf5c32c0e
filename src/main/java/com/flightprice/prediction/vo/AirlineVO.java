package com.flightprice.prediction.vo;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 航空公司视图对象
 */
@Data
public class AirlineVO {
    
    private Long id;
    private String code;
    private String name;
    private String englishName;
    private String country;
    private Integer foundedYear;
    private String headquarters;
    private String alliance;
    private Integer fleetSize;
    private Integer routesCount;
    private String description;
    private String logo;
    private String website;
    private Boolean enabled;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
    
    // 统计数据
    private Integer flightsCount;
    private Double avgPrice;
    private Double onTimeRate;
    private Integer popularRoutes;
} 