package com.flightprice.prediction.controller;

import com.flightprice.prediction.common.Result;
import com.flightprice.prediction.dto.AirportDTO;
import com.flightprice.prediction.dto.AirportStatsDTO;
import com.flightprice.prediction.dto.MonthlyStatsDTO;
import com.flightprice.prediction.entity.Airport;
import com.flightprice.prediction.service.AirportService;
import com.flightprice.prediction.vo.AirportVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.web.PageableDefault;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 机场控制器
 */
@Api(tags = "机场管理")
@RestController
@RequestMapping("/api/airports")
@RequiredArgsConstructor
public class AirportController {

    private final AirportService airportService;

    @ApiOperation("分页查询机场列表")
    @GetMapping
    public Result<Page<AirportVO>> getAirportList(
            @RequestParam(required = false) String keyword,
            @PageableDefault(sort = "iataCode", direction = Sort.Direction.ASC) Pageable pageable) {
        Page<Airport> airportPage = airportService.findAll(keyword, pageable);
        Page<AirportVO> voPage = airportPage.map(this::convertToVO);
        return Result.success(voPage);
    }

    @ApiOperation("根据ID查询机场")
    @GetMapping("/{id}")
    public Result<AirportVO> getAirportById(@PathVariable Long id) {
        Airport airport = airportService.findById(id);
        return Result.success(convertToVO(airport));
    }

    @ApiOperation("根据IATA代码查询机场")
    @GetMapping("/iata/{iataCode}")
    public Result<AirportVO> getAirportByIataCode(@PathVariable String iataCode) {
        Airport airport = airportService.findByIataCode(iataCode);
        return Result.success(convertToVO(airport));
    }

    @ApiOperation("根据ICAO代码查询机场")
    @GetMapping("/icao/{icaoCode}")
    public Result<AirportVO> getAirportByIcaoCode(@PathVariable String icaoCode) {
        Airport airport = airportService.findByIcaoCode(icaoCode);
        return Result.success(convertToVO(airport));
    }

    @ApiOperation("根据城市查询机场列表")
    @GetMapping("/city/{city}")
    public Result<List<AirportVO>> getAirportsByCity(@PathVariable String city) {
        List<Airport> airports = airportService.findByCity(city);
        List<AirportVO> voList = airports.stream().map(this::convertToVO).collect(Collectors.toList());
        return Result.success(voList);
    }

    @ApiOperation("创建机场")
    @PostMapping
    @PreAuthorize("hasAuthority('airport:create')")
    public Result<AirportVO> createAirport(@Valid @RequestBody AirportDTO airportDTO) {
        Airport airport = airportService.createAirport(airportDTO);
        return Result.success(convertToVO(airport));
    }

    @ApiOperation("更新机场")
    @PutMapping("/{id}")
    @PreAuthorize("hasAuthority('airport:update')")
    public Result<AirportVO> updateAirport(@PathVariable Long id, @Valid @RequestBody AirportDTO airportDTO) {
        Airport airport = airportService.updateAirport(id, airportDTO);
        return Result.success(convertToVO(airport));
    }

    @ApiOperation("删除机场")
    @DeleteMapping("/{id}")
    @PreAuthorize("hasAuthority('airport:delete')")
    public Result<Void> deleteAirport(@PathVariable Long id) {
        airportService.deleteAirport(id);
        return Result.success();
    }

    @ApiOperation("更新机场状态")
    @PutMapping("/{id}/status")
    @PreAuthorize("hasAuthority('airport:update')")
    public Result<AirportVO> updateStatus(@PathVariable Long id, @RequestParam Boolean enabled) {
        Airport airport = airportService.updateStatus(id, enabled);
        return Result.success(convertToVO(airport));
    }

    @ApiOperation("获取机场统计数据")
    @GetMapping("/{id}/statistics")
    public Result<Map<String, Object>> getStatistics(@PathVariable Long id) {
        Map<String, Object> statistics = airportService.getStatistics(id);
        return Result.success(statistics);
    }

    @GetMapping("/{id}/stats")
    @ApiOperation("获取机场统计数据")
    public Result<AirportStatsDTO> getAirportStats(@PathVariable Long id) {
        return Result.success(airportService.getAirportStats(id));
    }

    @GetMapping("/{id}/monthly-stats")
    @ApiOperation("获取机场月度统计数据")
    public Result<MonthlyStatsDTO> getAirportMonthlyStats(
            @PathVariable Long id,
            @RequestParam(defaultValue = "#{T(java.time.Year).now().getValue()}") Integer year) {
        return Result.success(airportService.getAirportMonthlyStats(id, year));
    }

    private AirportVO convertToVO(Airport airport) {
        AirportVO vo = new AirportVO();
        BeanUtils.copyProperties(airport, vo);
        return vo;
    }
} 