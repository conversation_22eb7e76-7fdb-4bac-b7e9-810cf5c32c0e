package com.flightprice.prediction.controller;

import com.flightprice.prediction.common.Result;
import com.flightprice.prediction.dto.RoleDTO;
import com.flightprice.prediction.service.RoleService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 角色控制器
 */
@Api(tags = "角色管理")
@RestController
@RequestMapping("/role")
@RequiredArgsConstructor
public class RoleController {

    private final RoleService roleService;
    
    /**
     * 分页查询角色列表
     */
    @ApiOperation("分页查询角色列表")
    @GetMapping("/page")
    public Result<Page<RoleDTO>> getRolePage(
            @ApiParam("关键字") @RequestParam(required = false) String keyword,
            @ApiParam("页码") @RequestParam(defaultValue = "0") Integer page,
            @ApiParam("每页条数") @RequestParam(defaultValue = "10") Integer size) {
        Pageable pageable = PageRequest.of(page, size, Sort.by(Sort.Direction.DESC, "createTime"));
        Page<RoleDTO> rolePage = roleService.getRolePage(keyword, pageable);
        return Result.success(rolePage);
    }
    
    /**
     * 获取所有角色列表
     */
    @ApiOperation("获取所有角色列表")
    @GetMapping("/list")
    public Result<List<RoleDTO>> getAllRoles() {
        List<RoleDTO> roleList = roleService.getAllRoles();
        return Result.success(roleList);
    }
    
    /**
     * 获取角色详情
     */
    @ApiOperation("获取角色详情")
    @GetMapping("/{id}")
    public Result<RoleDTO> getRoleDetail(@PathVariable Long id) {
        RoleDTO roleDTO = roleService.getRoleById(id);
        return Result.success(roleDTO);
    }
    
    /**
     * 创建角色
     */
    @ApiOperation("创建角色")
    @PostMapping
    public Result<Long> createRole(@Valid @RequestBody RoleDTO roleDTO) {
        Long roleId = roleService.createRole(roleDTO);
        return Result.success(roleId);
    }
    
    /**
     * 更新角色
     */
    @ApiOperation("更新角色")
    @PutMapping("/{id}")
    public Result<Boolean> updateRole(@PathVariable Long id, @Valid @RequestBody RoleDTO roleDTO) {
        roleDTO.setId(id);
        boolean success = roleService.updateRole(roleDTO);
        return Result.success(success);
    }
    
    /**
     * 删除角色
     */
    @ApiOperation("删除角色")
    @DeleteMapping("/{id}")
    public Result<Boolean> deleteRole(@PathVariable Long id) {
        boolean success = roleService.deleteRole(id);
        return Result.success(success);
    }
    
    /**
     * 批量删除角色
     */
    @ApiOperation("批量删除角色")
    @DeleteMapping("/batch")
    public Result<Boolean> batchDeleteRole(@RequestBody List<Long> ids) {
        boolean success = roleService.batchDeleteRole(ids);
        return Result.success(success);
    }
    
    /**
     * 获取角色权限列表
     */
    @ApiOperation("获取角色权限列表")
    @GetMapping("/{id}/permissions")
    public Result<List<Long>> getRolePermissions(@PathVariable Long id) {
        List<Long> permissionIds = roleService.getRolePermissionIds(id);
        return Result.success(permissionIds);
    }
    
    /**
     * 分配角色权限
     */
    @ApiOperation("分配角色权限")
    @PutMapping("/{id}/permissions")
    public Result<Boolean> assignRolePermissions(@PathVariable Long id, @RequestBody List<Long> permissionIds) {
        boolean success = roleService.assignRolePermissions(id, permissionIds);
        return Result.success(success);
    }
} 