package com.flightprice.prediction.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.flightprice.prediction.common.Result;
import com.flightprice.prediction.dto.LoginRequest;
import com.flightprice.prediction.entity.User;
import com.flightprice.prediction.mapper.UserMapper;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

/**
 * 测试登录控制器
 */
@RestController
@RequestMapping("/test")
public class TestLoginController {

    private final UserMapper userMapper;
    private final PasswordEncoder passwordEncoder;

    public TestLoginController(UserMapper userMapper, PasswordEncoder passwordEncoder) {
        this.userMapper = userMapper;
        this.passwordEncoder = passwordEncoder;
    }

    @PostMapping("/login")
    public Result<Map<String, Object>> testLogin(@RequestBody LoginRequest request) {
        // 查询用户
        User user = userMapper.selectOne(new LambdaQueryWrapper<User>()
                .eq(User::getUsername, request.getUsername()));

        Map<String, Object> result = new HashMap<>();

        if (user == null) {
            result.put("message", "用户不存在");
            result.put("exists", false);
            return Result.success(result);
        }

        // 验证密码
        boolean passwordMatch = passwordEncoder.matches(request.getPassword(), user.getPassword());

        result.put("exists", true);
        result.put("passwordMatch", passwordMatch);
        result.put("user", user);

        return Result.success(result);
    }
}
