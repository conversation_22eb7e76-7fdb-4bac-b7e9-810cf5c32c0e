package com.flightprice.prediction.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.flightprice.prediction.entity.User;
import com.flightprice.prediction.mapper.UserMapper;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 用户检查控制器
 */
@RestController
@RequestMapping("/check")
public class UserCheckController {

    private final UserMapper userMapper;

    public UserCheckController(UserMapper userMapper) {
        this.userMapper = userMapper;
    }

    @GetMapping("/users")
    public Map<String, Object> checkUsers() {
        Map<String, Object> result = new HashMap<>();
        
        // 查询所有用户
        List<User> users = userMapper.selectList(new LambdaQueryWrapper<>());
        
        result.put("userCount", users.size());
        result.put("users", users);
        
        return result;
    }
}
