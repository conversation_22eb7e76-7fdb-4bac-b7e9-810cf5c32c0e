package com.flightprice.prediction.controller;

import com.flightprice.prediction.common.PageResult;
import com.flightprice.prediction.common.Result;
import com.flightprice.prediction.dto.FlightSearchRequest;
import com.flightprice.prediction.dto.PricePredictionRequest;
import com.flightprice.prediction.entity.Flight;
import com.flightprice.prediction.service.FlightService;
import com.flightprice.prediction.service.PricePredictionService;
import com.flightprice.prediction.vo.FlightVO;
import com.flightprice.prediction.vo.PricePredictionVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * 航班控制器
 */
@Api(tags = "航班管理")
@RestController
@RequestMapping("/flights")
public class FlightController {

    @Resource
    private FlightService flightService;
    @Resource
    private PricePredictionService pricePredictionService;


    @ApiOperation("搜索航班")
    @PostMapping("/search")
    public Result<PageResult<FlightVO>> searchFlights(@Valid @RequestBody FlightSearchRequest request) {
        PageResult<FlightVO> result = flightService.searchFlights(request);
        return Result.success(result);
    }

    @ApiOperation("获取航班详情")
    @GetMapping("/{id}")
    public Result<FlightVO> getFlightDetail(@PathVariable Long id) {
        if (id == null) {
            return Result.error("航班 ID 不能为空");
        }
        FlightVO flight = flightService.getFlightDetail(id);
        return Result.success(flight);
    }

    @ApiOperation("预测价格")
    @PostMapping("/predict-price")
    public Result<PricePredictionVO> predictPrice(@Valid @RequestBody PricePredictionRequest request) {
        PricePredictionVO predictionResult = pricePredictionService.predictPrice(request);
        return Result.success(predictionResult);
    }

    @ApiOperation("新增航班（管理员）")
    @PostMapping
    @PreAuthorize("hasRole('ROLE_ADMIN')")
    public Result<Long> addFlight(@Valid @RequestBody Flight flight) {
        Long flightId = flightService.addFlight(flight);
        return Result.success(flightId);
    }

    @ApiOperation("修改航班（管理员）")
    @PutMapping("/{id}")
    @PreAuthorize("hasRole('ROLE_ADMIN')")
    public Result<Void> updateFlight(@PathVariable Long id, @Valid @RequestBody Flight flight) {
        flight.setId(id);
        flightService.updateFlight(flight);
        return Result.success();
    }

    @ApiOperation("删除航班（管理员）")
    @DeleteMapping("/{id}")
    @PreAuthorize("hasRole('ROLE_ADMIN')")
    public Result<Void> deleteFlight(@PathVariable Long id) {
        flightService.deleteFlight(id);
        return Result.success();
    }
    
    // =================== 航班模板管理接口 ===================
    
    @ApiOperation("获取航班模板列表（管理员）")
    @GetMapping("/templates")
    @PreAuthorize("hasRole('ROLE_ADMIN')")
    public Result<PageResult<FlightVO>> getFlightTemplates(
            @ApiParam("页码") @RequestParam(defaultValue = "1") Integer pageNum,
            @ApiParam("每页大小") @RequestParam(defaultValue = "10") Integer pageSize,
            @ApiParam("航空公司ID") @RequestParam(required = false) Long airlineId,
            @ApiParam("出发城市ID") @RequestParam(required = false) Long departureCityId,
            @ApiParam("到达城市ID") @RequestParam(required = false) Long arrivalCityId) {
        PageResult<FlightVO> result = flightService.getFlightTemplates(pageNum, pageSize, airlineId, departureCityId, arrivalCityId);
        return Result.success(result);
    }
    
    @ApiOperation("获取航班模板详情（管理员）")
    @GetMapping("/templates/{id}")
    @PreAuthorize("hasRole('ROLE_ADMIN')")
    public Result<FlightVO> getFlightTemplateDetail(@PathVariable Long id) {
        if (id == null) {
            return Result.error("航班模板 ID 不能为空");
        }
        FlightVO flightTemplate = flightService.getFlightTemplateDetail(id);
        return Result.success(flightTemplate);
    }
    
    @ApiOperation("新增航班模板（管理员）")
    @PostMapping("/templates")
    @PreAuthorize("hasRole('ROLE_ADMIN')")
    public Result<Long> addFlightTemplate(@Valid @RequestBody Flight flightTemplate) {
        Long templateId = flightService.addFlightTemplate(flightTemplate);
        return Result.success(templateId);
    }
    
    @ApiOperation("修改航班模板（管理员）")
    @PutMapping("/templates/{id}")
    @PreAuthorize("hasRole('ROLE_ADMIN')")
    public Result<Void> updateFlightTemplate(@PathVariable Long id, @Valid @RequestBody Flight flightTemplate) {
        flightTemplate.setId(id);
        flightService.updateFlightTemplate(flightTemplate);
        return Result.success();
    }
    
    @ApiOperation("删除航班模板（管理员）")
    @DeleteMapping("/templates/{id}")
    @PreAuthorize("hasRole('ROLE_ADMIN')")
    public Result<Void> deleteFlightTemplate(@PathVariable Long id) {
        flightService.deleteFlightTemplate(id);
        return Result.success();
    }
}