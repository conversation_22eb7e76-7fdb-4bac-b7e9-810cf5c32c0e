package com.flightprice.prediction.controller;

import com.flightprice.prediction.common.api.CommonResult;
import com.flightprice.prediction.dto.PricePredictionRequest;
import com.flightprice.prediction.service.PricePredictionService;
import com.flightprice.prediction.vo.PricePredictionVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Map;
import java.util.function.Consumer;

@Slf4j
@RestController
@RequestMapping("/api/prediction")
public class PricePredictionController {

    @Autowired
    private PricePredictionService pricePredictionService;

    @PostMapping("/price-predict")
    public CommonResult<PricePredictionVO> predictPrice(@RequestBody PricePredictionRequest request) {
        try {
            log.info("接收到预测请求参数: {}", request);
            // 这里只有城市名、航空公司名、日期等基本信息，需要先查询对应航班
            
            // 模型ID不是必需的，设置默认值
            if (request.getModelId() == null || request.getModelId().isEmpty()) {
                request.setModelId("default-model");
            }
            
            PricePredictionVO result = pricePredictionService.predictPrice(request);
            return CommonResult.success(result);
        } catch (Exception e) {
            log.error("预测价格失败", e);
            return CommonResult.failed("预测价格失败：" + e.getMessage());
        }
    }

    @PostMapping("/train")
    public CommonResult<Long> trainModel(@RequestParam(required = false) Long routeId) {
        try {
            Long modelId = pricePredictionService.trainModel(routeId);
            return CommonResult.success(modelId);
        } catch (Exception e) {
            log.error("训练模型失败", e);
            return CommonResult.failed("训练模型失败：" + e.getMessage());
        }
    }

    @PostMapping("/collect")
    public CommonResult<Void> collectPriceData() {
        try {
            pricePredictionService.collectPriceData();
            return CommonResult.success(null);
        } catch (Exception e) {
            log.error("收集价格数据失败", e);
            return CommonResult.failed("收集价格数据失败：" + e.getMessage());
        }
    }

    @PostMapping("/update-factors")
    public CommonResult<Void> updatePriceFactors(@RequestParam(required = false) Long routeId) {
        try {
            pricePredictionService.updatePriceFactors(routeId);
            return CommonResult.success(null);
        } catch (Exception e) {
            log.error("更新价格因素失败", e);
            return CommonResult.failed("更新价格因素失败：" + e.getMessage());
        }
    }

    @PostMapping("/train-model")
    public CommonResult<Map<String, Object>> train(@RequestBody Map<String, Object> dataMap,
                                                 @RequestParam(defaultValue = "0.8") Double trainingRatio,
                                                 @RequestParam(required = false) Map<String, Object> hyperParameters,
                                                 @RequestParam(defaultValue = "false") Boolean autoTune) {
        try {
            Map<String, Object> result = pricePredictionService.train(dataMap, trainingRatio, hyperParameters, autoTune, null);
            return CommonResult.success(result);
        } catch (Exception e) {
            log.error("训练模型失败", e);
            return CommonResult.failed("训练模型失败：" + e.getMessage());
        }
    }

    @PostMapping("/predict-price")
    public CommonResult<Map<String, Object>> predict(@RequestBody Map<String, Object> featureMap) {
        try {
            Map<String, Object> result = pricePredictionService.predict(featureMap);
            return CommonResult.success(result);
        } catch (Exception e) {
            log.error("预测价格失败", e);
            return CommonResult.failed("预测价格失败：" + e.getMessage());
        }
    }

    @PostMapping("/evaluate")
    public CommonResult<Map<String, Object>> evaluate(@RequestBody Map<String, Object> testDataMap) {
        try {
            Map<String, Object> result = pricePredictionService.evaluate(testDataMap);
            return CommonResult.success(result);
        } catch (Exception e) {
            log.error("评估模型失败", e);
            return CommonResult.failed("评估模型失败：" + e.getMessage());
        }
    }
} 