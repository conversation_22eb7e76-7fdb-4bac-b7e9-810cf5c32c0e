package com.flightprice.prediction.controller;

import com.flightprice.prediction.common.Result;
import com.flightprice.prediction.dto.BindEmailRequest;
import com.flightprice.prediction.dto.BindPhoneRequest;
import com.flightprice.prediction.dto.ChangePasswordRequest;
import com.flightprice.prediction.dto.LoginResponse;
import com.flightprice.prediction.dto.UpdateUserInfoRequest;
import com.flightprice.prediction.service.UserService;
import com.flightprice.prediction.vo.UserVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * 用户API控制器 - 兼容前端调用路径
 */
@Api(tags = "用户API")
@RestController
@RequestMapping("/api/user")
@RequiredArgsConstructor
public class UserApiController {

    private final UserService userService;

    /**
     * 获取当前用户信息
     */
    @ApiOperation("获取当前用户信息")
    @GetMapping("/info")
    public Result<LoginResponse> getUserInfo() {
        UserVO userVO = userService.getCurrentUserInfo();

        // 构造与 LoginResponse 格式一致的响应数据
        LoginResponse response = new LoginResponse();
        response.setUserId(userVO.getId());
        response.setUsername(userVO.getUsername());
        response.setName(userVO.getFullName());
        // Set role from the first role in the set if available
        if (userVO.getRoles() != null && !userVO.getRoles().isEmpty()) {
            response.setRole(userVO.getRoles().iterator().next().getName());
        }
        // 不返回 token，因为前端已经有 token

        return Result.success(response);
    }

    /**
     * 更新用户信息
     */
    @ApiOperation("更新用户信息")
    @PutMapping("/info")
    public Result<Boolean> updateUserInfo(@Valid @RequestBody UpdateUserInfoRequest request) {
        boolean success = userService.updateUserInfo(request);
        return Result.success(success);
    }
    
    /**
     * 修改密码
     */
    @ApiOperation("修改密码")
    @PutMapping("/password")
    public Result<Boolean> changePassword(@Valid @RequestBody ChangePasswordRequest request) {
        boolean success = userService.changePassword(request);
        return Result.success(success);
    }
    
    /**
     * 发送手机验证码
     */
    @ApiOperation("发送手机验证码")
    @PostMapping("/send-phone-code")
    public Result<Boolean> sendPhoneVerifyCode(@RequestBody BindPhoneRequest request) {
        // 这里简化处理，实际应该调用短信服务发送验证码
        // 不实现实际的短信发送功能
        return Result.success(true);
    }
    
    /**
     * 绑定手机号
     */
    @ApiOperation("绑定手机号")
    @PostMapping("/bind-phone")
    public Result<Boolean> bindPhone(@Valid @RequestBody BindPhoneRequest request) {
        boolean success = userService.bindPhone(request);
        return Result.success(success);
    }
    
    /**
     * 发送邮箱验证码
     */
    @ApiOperation("发送邮箱验证码")
    @PostMapping("/send-email-code")
    public Result<Boolean> sendEmailVerifyCode(@RequestBody BindEmailRequest request) {
        // 这里简化处理，实际应该调用邮件服务发送验证码
        // 不实现实际的邮件发送功能
        return Result.success(true);
    }
    
    /**
     * 绑定邮箱
     */
    @ApiOperation("绑定邮箱")
    @PostMapping("/bind-email")
    public Result<Boolean> bindEmail(@Valid @RequestBody BindEmailRequest request) {
        boolean success = userService.bindEmail(request);
        return Result.success(success);
    }
} 