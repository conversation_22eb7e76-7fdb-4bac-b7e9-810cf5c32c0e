package com.flightprice.prediction.controller;

import com.flightprice.prediction.common.PageResult;
import com.flightprice.prediction.common.Result;
import com.flightprice.prediction.dto.CreateOrderRequest;
import com.flightprice.prediction.service.OrderService;
import com.flightprice.prediction.vo.OrderVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import com.flightprice.prediction.common.Constants;

import javax.validation.Valid;
import java.util.List;

/**
 * 订单控制器
 */
@Api(tags = "订单管理")
@RestController
@RequestMapping("/orders")
public class OrderController {

    private final OrderService orderService;

    public OrderController(OrderService orderService) {
        this.orderService = orderService;
    }

    /**
     * 获取用户最近的订单
     */
    @ApiOperation("获取用户最近的订单")
    @GetMapping("/recent")
    public Result<List<OrderVO>> getRecentOrders(
            @ApiParam("最大数量") @RequestParam(defaultValue = "5") Integer limit) {
        List<OrderVO> recentOrders = orderService.getRecentOrders(limit);
        return Result.success(recentOrders);
    }

    @ApiOperation("创建订单")
    @PostMapping
    public Result<String> createOrder(@Valid @RequestBody CreateOrderRequest request) {
        String orderNo = orderService.createOrder(request);
        return Result.success(orderNo);
    }

    @ApiOperation("支付订单")
    @PostMapping("/{orderNo}/pay")
    public Result<Boolean> payOrder(@PathVariable String orderNo) {
        boolean success = orderService.payOrder(orderNo);
        return Result.success(success);
    }

    @ApiOperation("取消订单")
    @PostMapping("/{orderNo}/cancel")
    public Result<Void> cancelOrder(@PathVariable String orderNo) {
        orderService.cancelOrder(orderNo);
        return Result.success();
    }

    @ApiOperation("获取订单详情")
    @GetMapping("/{orderNo}")
    public Result<OrderVO> getOrderDetail(@PathVariable String orderNo) {
        OrderVO orderVO = orderService.getOrderDetail(orderNo);
        return Result.success(orderVO);
    }

    @ApiOperation("获取当前用户订单列表")
    @GetMapping("/user")
    public Result<PageResult<OrderVO>> getUserOrders(
            @ApiParam("页码") @RequestParam(defaultValue = "1") Integer pageNum,
            @ApiParam("每页数量") @RequestParam(defaultValue = "10") Integer pageSize,
            @ApiParam("订单状态") @RequestParam(required = false) String status) {
        PageResult<OrderVO> result = orderService.getUserOrders(pageNum, pageSize, status);
        return Result.success(result);
    }

    @ApiOperation("获取当前用户未支付订单列表")
    @GetMapping("/unpaid")
    public Result<PageResult<OrderVO>> getUnpaidOrders(
            @ApiParam("页码") @RequestParam(defaultValue = "1") Integer pageNum,
            @ApiParam("每页数量") @RequestParam(defaultValue = "10") Integer pageSize) {
        // 固定状态为UNPAID
        PageResult<OrderVO> result = orderService.getUserOrders(pageNum, pageSize, Constants.OrderStatus.UNPAID);
        return Result.success(result);
    }

    @ApiOperation("获取当前用户即将出行订单列表")
    @GetMapping("/upcoming")
    public Result<PageResult<OrderVO>> getUpcomingOrders(
            @ApiParam("页码") @RequestParam(defaultValue = "1") Integer pageNum,
            @ApiParam("每页数量") @RequestParam(defaultValue = "10") Integer pageSize) {
        // 固定状态为PAID（已支付的才是即将出行的）
        PageResult<OrderVO> result = orderService.getUserOrders(pageNum, pageSize, Constants.OrderStatus.PAID);
        return Result.success(result);
    }

    @ApiOperation("获取当前用户已完成订单列表")
    @GetMapping("/completed")
    public Result<PageResult<OrderVO>> getCompletedOrders(
            @ApiParam("页码") @RequestParam(defaultValue = "1") Integer pageNum,
            @ApiParam("每页数量") @RequestParam(defaultValue = "10") Integer pageSize) {
        // 固定状态为COMPLETED
        PageResult<OrderVO> result = orderService.getUserOrders(pageNum, pageSize, Constants.OrderStatus.COMPLETED);
        return Result.success(result);
    }

    @ApiOperation("获取当前用户已退款订单列表")
    @GetMapping("/refunded")
    public Result<PageResult<OrderVO>> getRefundedOrders(
            @ApiParam("页码") @RequestParam(defaultValue = "1") Integer pageNum,
            @ApiParam("每页数量") @RequestParam(defaultValue = "10") Integer pageSize) {
        // 系统中可能没有REFUNDED状态，这里使用REFUNDED字符串
        PageResult<OrderVO> result = orderService.getUserOrders(pageNum, pageSize, "REFUNDED");
        return Result.success(result);
    }

    @ApiOperation("获取当前用户已取消订单列表")
    @GetMapping("/cancelled")
    public Result<PageResult<OrderVO>> getCancelledOrders(
            @ApiParam("页码") @RequestParam(defaultValue = "1") Integer pageNum,
            @ApiParam("每页数量") @RequestParam(defaultValue = "10") Integer pageSize) {
        // 使用CANCELLED状态
        PageResult<OrderVO> result = orderService.getUserOrders(pageNum, pageSize, Constants.OrderStatus.CANCELLED);
        return Result.success(result);
    }

    @ApiOperation("获取所有订单列表（管理员）")
    @GetMapping("/all")
    @PreAuthorize("hasRole('ROLE_ADMIN')")
    public Result<PageResult<OrderVO>> getAllOrders(
            @ApiParam("页码") @RequestParam(defaultValue = "1") Integer pageNum,
            @ApiParam("每页数量") @RequestParam(defaultValue = "10") Integer pageSize,
            @ApiParam("订单状态") @RequestParam(required = false) String status) {
        PageResult<OrderVO> result = orderService.getAllOrders(pageNum, pageSize, status);
        return Result.success(result);
    }
}