package com.flightprice.prediction.controller;

import com.flightprice.prediction.common.Result;
import com.flightprice.prediction.service.DataDashboardService;
import com.flightprice.prediction.vo.dashboard.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * 数据大屏控制器
 */
@RestController
@RequestMapping("/api/dashboard")
public class DataDashboardController {

    @Autowired
    private DataDashboardService dashboardService;

    /**
     * 获取顶部概览数据
     */
    @GetMapping("/overview")
    public Result<DashboardOverviewVO> getOverview() {
        return Result.success(dashboardService.getOverview());
    }

    /**
     * 获取价格趋势数据
     */
    @GetMapping("/price-trends")
    public Result<PriceTrendsVO> getPriceTrends(
            @RequestParam(required = false) String departureCity,
            @RequestParam(required = false) String arrivalCity,
            @RequestParam(required = false) String airline,
            @RequestParam(required = false) String timeRange) {
        return Result.success(dashboardService.getPriceTrends(departureCity, arrivalCity, airline, timeRange));
    }

    /**
     * 获取航线热度数据
     */
    @GetMapping("/route-heat")
    public Result<RouteHeatVO> getRouteHeat() {
        return Result.success(dashboardService.getRouteHeat());
    }

    /**
     * 获取航空公司市场份额
     */
    @GetMapping("/airline-market-share")
    public Result<AirlineMarketShareVO> getAirlineMarketShare() {
        return Result.success(dashboardService.getAirlineMarketShare());
    }

    /**
     * 获取热门城市排行
     */
    @GetMapping("/hot-cities")
    public Result<HotCitiesVO> getHotCities(@RequestParam(defaultValue = "10") Integer limit) {
        return Result.success(dashboardService.getHotCities(limit));
    }

    /**
     * 获取航班准点率统计
     */
    @GetMapping("/punctuality")
    public Result<PunctualityVO> getPunctuality(
            @RequestParam(required = false) String airline) {
        return Result.success(dashboardService.getPunctuality(airline));
    }

    /**
     * 获取客流量统计
     */
    @GetMapping("/passenger-flow")
    public Result<PassengerFlowVO> getPassengerFlow(
            @RequestParam(required = false) String timeRange) {
        return Result.success(dashboardService.getPassengerFlow(timeRange));
    }

    /**
     * 获取地图数据
     */
    @GetMapping("/map-data")
    public Result<MapDataVO> getMapData() {
        return Result.success(dashboardService.getMapData());
    }
    
    /**
     * 获取价格区间分布
     */
    @GetMapping("/price-distribution")
    public Result<PriceDistributionVO> getPriceDistribution() {
        return Result.success(dashboardService.getPriceDistribution());
    }
    
    /**
     * 获取各城市平均价格
     */
    @GetMapping("/city-avg-prices")
    public Result<CityAvgPricesVO> getCityAvgPrices(@RequestParam(defaultValue = "departure") String type) {
        return Result.success(dashboardService.getCityAvgPrices(type));
    }
    
    /**
     * 获取预测准确率统计
     */
    @GetMapping("/prediction-accuracy")
    public Result<PredictionAccuracyVO> getPredictionAccuracy() {
        return Result.success(dashboardService.getPredictionAccuracy());
    }
    
    /**
     * 获取实时数据监控
     */
    @GetMapping("/realtime-monitor")
    public Result<RealtimeMonitorVO> getRealtimeMonitor() {
        return Result.success(dashboardService.getRealtimeMonitor());
    }
    
    /**
     * 获取日期因素对价格的影响
     */
    @GetMapping("/date-price-impact")
    public Result<DatePriceImpactVO> getDatePriceImpact() {
        return Result.success(dashboardService.getDatePriceImpact());
    }
    
    /**
     * 综合大屏数据（所有数据）
     */
    @GetMapping("/all")
    public Result<Map<String, Object>> getAllDashboardData() {
        return Result.success(dashboardService.getAllDashboardData());
    }
} 