package com.flightprice.prediction.controller;

import com.flightprice.prediction.common.Result;
import com.flightprice.prediction.dto.AirlineDTO;
import com.flightprice.prediction.dto.AirlineStatDTO;
import com.flightprice.prediction.dto.AirlineStatsDTO;
import com.flightprice.prediction.dto.MonthlyStatsDTO;
import com.flightprice.prediction.entity.Airline;
import com.flightprice.prediction.service.AirlineService;
import com.flightprice.prediction.vo.AirlineVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.web.PageableDefault;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * 航空公司控制器
 */
@Api(tags = "航空公司管理")
@RestController
@RequestMapping("/api/airlines")
@RequiredArgsConstructor
public class AirlineController {

    private final AirlineService airlineService;

    /**
     * 分页查询航空公司列表
     */
    @ApiOperation("分页查询航空公司列表")
    @GetMapping
    public Result<Page<AirlineVO>> getAirlineList(
            @RequestParam(required = false) String keyword,
            @PageableDefault(sort = "code", direction = Sort.Direction.ASC) Pageable pageable) {
        Page<Airline> airlinePage = airlineService.findAll(keyword, pageable);
        Page<AirlineVO> voPage = airlinePage.map(this::convertToVO);
        return Result.success(voPage);
    }

    /**
     * 获取所有航空公司列表
     */
    @ApiOperation("获取所有航空公司列表")
    @GetMapping("/list")
    public Result<List<AirlineDTO>> getAllAirlines() {
        List<AirlineDTO> airlineList = airlineService.getAllAirlines();
        return Result.success(airlineList);
    }

    /**
     * 获取所有启用的航空公司列表
     */
    @ApiOperation("获取所有启用的航空公司列表")
    @GetMapping("/list/enabled")
    public Result<List<AirlineDTO>> getAllEnabledAirlines() {
        List<AirlineDTO> airlineList = airlineService.getAllEnabledAirlines();
        return Result.success(airlineList);
    }

    /**
     * 获取航空公司详情
     */
    @ApiOperation("根据ID查询航空公司")
    @GetMapping("/{id}")
    public Result<AirlineVO> getAirlineById(@PathVariable Long id) {
        Airline airline = airlineService.findById(id);
        return Result.success(convertToVO(airline));
    }

    /**
     * 根据代码获取航空公司详情
     */
    @ApiOperation("根据代码查询航空公司")
    @GetMapping("/code/{code}")
    public Result<AirlineVO> getAirlineByCode(@PathVariable String code) {
        Airline airline = airlineService.findByCode(code);
        return Result.success(convertToVO(airline));
    }

    /**
     * 创建航空公司
     */
    @ApiOperation("创建航空公司")
    @PostMapping
    @PreAuthorize("hasAuthority('airline:create')")
    public Result<AirlineVO> createAirline(@Valid @RequestBody AirlineDTO airlineDTO) {
        Airline airline = airlineService.createAirline(airlineDTO);
        return Result.success(convertToVO(airline));
    }

    /**
     * 更新航空公司
     */
    @ApiOperation("更新航空公司")
    @PutMapping("/{id}")
    @PreAuthorize("hasAuthority('airline:update')")
    public Result<AirlineVO> updateAirline(@PathVariable Long id, @Valid @RequestBody AirlineDTO airlineDTO) {
        Airline airline = airlineService.updateAirline(id, airlineDTO);
        return Result.success(convertToVO(airline));
    }

    /**
     * 删除航空公司
     */
    @ApiOperation("删除航空公司")
    @DeleteMapping("/{id}")
    @PreAuthorize("hasAuthority('airline:delete')")
    public Result<Void> deleteAirline(@PathVariable Long id) {
        airlineService.deleteAirline(id);
        return Result.success();
    }

    /**
     * 批量删除航空公司
     */
    @ApiOperation("批量删除航空公司")
    @DeleteMapping("/batch")
    public Result<Boolean> batchDeleteAirline(@RequestBody List<Long> ids) {
        boolean success = airlineService.batchDeleteAirline(ids);
        return Result.success(success);
    }

    /**
     * 启用/禁用航空公司
     */
    @ApiOperation("更新航空公司状态")
    @PutMapping("/{id}/status")
    @PreAuthorize("hasAuthority('airline:update')")
    public Result<AirlineVO> updateStatus(@PathVariable Long id, @RequestParam Boolean enabled) {
        Airline airline = airlineService.updateStatus(id, enabled);
        return Result.success(convertToVO(airline));
    }

    /**
     * 获取航空公司统计数据
     */
    @ApiOperation("获取航空公司统计数据")
    @GetMapping("/{id}/statistics")
    public Result<Map<String, Object>> getStatistics(@PathVariable Long id) {
        Map<String, Object> statistics = airlineService.getStatistics(id);
        return Result.success(statistics);
    }

    /**
     * 获取航空公司月度统计数据
     */
    @ApiOperation("获取航空公司月度统计数据")
    @GetMapping("/{id}/monthly-stats")
    public Result<AirlineStatDTO> getAirlineMonthlyStats(
            @PathVariable Long id,
            @RequestParam(defaultValue = "#{T(java.time.Year).now().getValue()}") Integer year) {
        return Result.success(airlineService.getAirlineMonthlyStats(id, year));
    }

    private AirlineVO convertToVO(Airline airline) {
        AirlineVO vo = new AirlineVO();
        BeanUtils.copyProperties(airline, vo);
        return vo;
    }
}
