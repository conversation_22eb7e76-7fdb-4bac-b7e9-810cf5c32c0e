package com.flightprice.prediction.controller;

import com.flightprice.prediction.common.Result;
import com.flightprice.prediction.dto.PermissionDTO;
import com.flightprice.prediction.service.PermissionService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 权限控制器
 */
@Api(tags = "权限管理")
@RestController
@RequestMapping("/permission")
@RequiredArgsConstructor
public class PermissionController {

    private final PermissionService permissionService;
    
    /**
     * 分页查询权限列表
     */
    @ApiOperation("分页查询权限列表")
    @GetMapping("/page")
    public Result<Page<PermissionDTO>> getPermissionPage(
            @ApiParam("关键字") @RequestParam(required = false) String keyword,
            @ApiParam("权限类型") @RequestParam(required = false) String type,
            @ApiParam("页码") @RequestParam(defaultValue = "0") Integer page,
            @ApiParam("每页条数") @RequestParam(defaultValue = "10") Integer size) {
        Pageable pageable = PageRequest.of(page, size, Sort.by(Sort.Direction.ASC, "id"));
        Page<PermissionDTO> permissionPage = permissionService.getPermissionPage(keyword, type, pageable);
        return Result.success(permissionPage);
    }
    
    /**
     * 获取所有权限列表
     */
    @ApiOperation("获取所有权限列表")
    @GetMapping("/list")
    public Result<List<PermissionDTO>> getAllPermissions() {
        List<PermissionDTO> permissionList = permissionService.getAllPermissions();
        return Result.success(permissionList);
    }
    
    /**
     * 获取权限树
     */
    @ApiOperation("获取权限树")
    @GetMapping("/tree")
    public Result<List<PermissionDTO>> getPermissionTree() {
        List<PermissionDTO> permissionTree = permissionService.getPermissionTree();
        return Result.success(permissionTree);
    }
    
    /**
     * 根据类型获取权限列表
     */
    @ApiOperation("根据类型获取权限列表")
    @GetMapping("/type/{type}")
    public Result<List<PermissionDTO>> getPermissionsByType(@PathVariable String type) {
        List<PermissionDTO> permissionList = permissionService.getPermissionsByType(type);
        return Result.success(permissionList);
    }
    
    /**
     * 获取权限详情
     */
    @ApiOperation("获取权限详情")
    @GetMapping("/{id}")
    public Result<PermissionDTO> getPermissionDetail(@PathVariable Long id) {
        PermissionDTO permissionDTO = permissionService.getPermissionById(id);
        return Result.success(permissionDTO);
    }
    
    /**
     * 创建权限
     */
    @ApiOperation("创建权限")
    @PostMapping
    public Result<Long> createPermission(@Valid @RequestBody PermissionDTO permissionDTO) {
        Long permissionId = permissionService.createPermission(permissionDTO);
        return Result.success(permissionId);
    }
    
    /**
     * 更新权限
     */
    @ApiOperation("更新权限")
    @PutMapping("/{id}")
    public Result<Boolean> updatePermission(@PathVariable Long id, @Valid @RequestBody PermissionDTO permissionDTO) {
        permissionDTO.setId(id);
        boolean success = permissionService.updatePermission(permissionDTO);
        return Result.success(success);
    }
    
    /**
     * 删除权限
     */
    @ApiOperation("删除权限")
    @DeleteMapping("/{id}")
    public Result<Boolean> deletePermission(@PathVariable Long id) {
        boolean success = permissionService.deletePermission(id);
        return Result.success(success);
    }
    
    /**
     * 批量删除权限
     */
    @ApiOperation("批量删除权限")
    @DeleteMapping("/batch")
    public Result<Boolean> batchDeletePermission(@RequestBody List<Long> ids) {
        boolean success = permissionService.batchDeletePermission(ids);
        return Result.success(success);
    }
} 