package com.flightprice.prediction.controller;

import com.flightprice.prediction.common.Result;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * 价格预测控制器
 */
@RestController
@RequestMapping("/api/prediction")
public class PredictionController {

    /**
     * 预测价格
     */
    @PostMapping("/predict")
    public Result<Map<String, Object>> predictPrice(@RequestBody Map<String, Object> params) {
        // 模拟预测结果
        Map<String, Object> result = new HashMap<>();

        // 生成随机预测价格
        Random random = new Random();
        BigDecimal basePrice = new BigDecimal(800 + random.nextInt(1200));
        BigDecimal predictedPrice = basePrice.setScale(2, RoundingMode.HALF_UP);

        // 设置置信度
        double confidence = 0.75 + random.nextDouble() * 0.2;
        int accuracy = (int) (confidence * 100);

        // 设置价格区间
        BigDecimal lowerBound = predictedPrice.multiply(new BigDecimal("0.9")).setScale(2, RoundingMode.HALF_UP);
        BigDecimal upperBound = predictedPrice.multiply(new BigDecimal("1.1")).setScale(2, RoundingMode.HALF_UP);

        // 生成价格趋势数据
        List<Map<String, Object>> trendData = new ArrayList<>();
        Calendar cal = Calendar.getInstance();
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");

        for (int i = 30; i >= 0; i--) {
            cal.add(Calendar.DAY_OF_MONTH, -1);
            Map<String, Object> point = new HashMap<>();
            // 格式化日期为字符串
            point.put("date", dateFormat.format(cal.getTime()));

            // 生成随机历史价格
            double factor = 0.8 + (i / 30.0) * 0.4 + (random.nextDouble() * 0.2 - 0.1);
            BigDecimal historicalPrice = predictedPrice.multiply(new BigDecimal(factor)).setScale(2, RoundingMode.HALF_UP);
            point.put("historicalPrice", historicalPrice);
            
            // 生成预测价格（未来日期为预测价格，过去日期为null）
            if (i < 7) {
                factor = 0.9 + (random.nextDouble() * 0.2);
                BigDecimal predictedPointPrice = predictedPrice.multiply(new BigDecimal(factor)).setScale(2, RoundingMode.HALF_UP);
                point.put("predictedPrice", predictedPointPrice);
                
                // 添加置信区间
                factor = 0.8 + (random.nextDouble() * 0.4);
                BigDecimal confidenceInterval = predictedPointPrice.multiply(new BigDecimal(factor)).setScale(2, RoundingMode.HALF_UP);
                point.put("confidenceInterval", confidenceInterval);
            } else {
                point.put("predictedPrice", null);
                point.put("confidenceInterval", null);
            }

            trendData.add(point);
        }
        
        // 反转列表，使日期按从早到晚排序
        Collections.reverse(trendData);

        // 生成影响因素
        List<Map<String, Object>> factors = new ArrayList<>();
        
        // 季节因素
        Map<String, Object> seasonFactor = new HashMap<>();
        seasonFactor.put("name", "季节因素（旺季）");
        seasonFactor.put("type", "warning");
        factors.add(seasonFactor);
        
        // 提前预订因素
        Map<String, Object> advanceBooking = new HashMap<>();
        advanceBooking.put("name", "提前预订天数");
        advanceBooking.put("type", "success");
        factors.add(advanceBooking);
        
        // 星期因素
        Map<String, Object> weekdayFactor = new HashMap<>();
        weekdayFactor.put("name", "周末出行");
        weekdayFactor.put("type", "danger");
        factors.add(weekdayFactor);

        // 组装预测结果
        Map<String, Object> prediction = new HashMap<>();
        prediction.put("predictedPrice", predictedPrice);
        prediction.put("lowerBound", lowerBound);
        prediction.put("upperBound", upperBound);
        prediction.put("accuracy", accuracy);
        
        // 生成建议
        String suggestion;
        double randomSuggestion = random.nextDouble();
        if (randomSuggestion < 0.4) {
            suggestion = "立即购买";
        } else if (randomSuggestion < 0.7) {
            suggestion = "等待";
        } else {
            suggestion = "观望";
        }
        prediction.put("suggestion", suggestion);
        prediction.put("factors", factors);

        // 组装最终返回结果
        result.put("prediction", prediction);
        result.put("trendData", trendData);

        return Result.success(result);
    }
    
    /**
     * 获取预测历史记录
     */
    @GetMapping("/history")
    public Result<List<Map<String, Object>>> getPredictionHistory() {
        // 模拟预测历史数据
        List<Map<String, Object>> result = new ArrayList<>();
        Random random = new Random();
        
        // 生成10条历史记录
        for (int i = 0; i < 10; i++) {
            Map<String, Object> record = new HashMap<>();
            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
            Calendar cal = Calendar.getInstance();
            cal.add(Calendar.DAY_OF_MONTH, -i * 2);
            
            // 根据索引生成不同航线
            String[] routes = {"北京-上海", "广州-深圳", "成都-重庆"};
            String route = routes[i % routes.length];
            
            // 解析航线为始发地和目的地
            String[] cities = route.split("-");
            String departureCity = cities[0];
            String arrivalCity = cities[1];
            
            // 随机生成价格
            double basePrice = 800 + random.nextInt(1200);
            double predictedPrice = basePrice * (0.9 + random.nextDouble() * 0.2);
            double actualPrice = predictedPrice * (0.9 + random.nextDouble() * 0.2);
            
            // 计算准确率
            double accuracyValue = 100 - Math.abs(predictedPrice - actualPrice) / predictedPrice * 100;
            int accuracy = (int) Math.min(100, Math.max(0, accuracyValue));
            
            // 设置状态
            String status;
            if (Math.abs(predictedPrice - actualPrice) < predictedPrice * 0.05) {
                status = "准确";
            } else if (predictedPrice > actualPrice) {
                status = "偏高";
            } else {
                status = "偏低";
            }
            
            // 生成出发日期（未来日期）
            Calendar depCal = Calendar.getInstance();
            depCal.add(Calendar.DAY_OF_MONTH, 7 + random.nextInt(30));
            
            // 设置记录字段
            record.put("id", i + 1);
            record.put("date", dateFormat.format(cal.getTime()));
            record.put("route", route);
            record.put("departureCity", departureCity);
            record.put("arrivalCity", arrivalCity);
            record.put("departureDate", dateFormat.format(depCal.getTime()));
            record.put("predictedPrice", new BigDecimal(predictedPrice).setScale(2, RoundingMode.HALF_UP).doubleValue());
            record.put("actualPrice", new BigDecimal(actualPrice).setScale(2, RoundingMode.HALF_UP).doubleValue());
            record.put("accuracy", accuracy);
            record.put("model", getRandomModel());
            record.put("status", status);
            
            result.add(record);
        }
        
        return Result.success(result);
    }
    
    /**
     * 获取随机模型名称
     */
    private String getRandomModel() {
        String[] models = {"LSTM模型", "XGBoost模型", "随机森林模型", "线性回归模型", "CNN模型"};
        return models[new Random().nextInt(models.length)];
    }
}
