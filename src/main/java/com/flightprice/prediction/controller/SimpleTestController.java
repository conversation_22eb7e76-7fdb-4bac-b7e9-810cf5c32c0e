package com.flightprice.prediction.controller;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

/**
 * 简单测试控制器
 */
@RestController
@RequestMapping("/simple")
public class SimpleTestController {

    @GetMapping("/test")
    public Map<String, Object> test() {
        Map<String, Object> result = new HashMap<>();
        result.put("message", "这是一个简单的测试接口");
        result.put("success", true);
        return result;
    }
}
