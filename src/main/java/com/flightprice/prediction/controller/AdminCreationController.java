package com.flightprice.prediction.controller;

import com.flightprice.prediction.common.Constants;
import com.flightprice.prediction.common.Result;
import com.flightprice.prediction.entity.Role;
import com.flightprice.prediction.entity.User;
import com.flightprice.prediction.mapper.UserMapper;
import com.flightprice.prediction.repository.RoleRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDateTime;
import java.util.Collections;
import java.util.HashSet;

/**
 * 管理员创建控制器
 */
@RestController
@RequestMapping("/admin")
public class AdminCreationController {

    private final UserMapper userMapper;
    private final PasswordEncoder passwordEncoder;

    @Autowired
    private RoleRepository roleRepository;

    public AdminCreationController(UserMapper userMapper, PasswordEncoder passwordEncoder) {
        this.userMapper = userMapper;
        this.passwordEncoder = passwordEncoder;
    }

    @GetMapping("/create")
    public Result<User> createAdmin() {
        // 创建管理员用户
        User admin = new User();
        admin.setUsername("admin");
        admin.setPassword(passwordEncoder.encode("admin"));
        admin.setFullName("系统管理员");
        admin.setPhone("13800138000");
        admin.setEmail("<EMAIL>");

        // 设置管理员角色
        Role adminRole = roleRepository.findByName("ADMIN")
                .orElseThrow(() -> new RuntimeException("管理员角色不存在"));
        admin.setRoles(new HashSet<>(Collections.singletonList(adminRole)));

        // 设置创建和更新时间
        LocalDateTime now = LocalDateTime.now();
        admin.setCreatedAt(now);
        admin.setUpdatedAt(now);

        // 保存用户
        userMapper.insert(admin);

        // 返回创建的用户（密码已加密）
        return Result.success(admin);
    }
}
