package com.flightprice.prediction.controller;

import com.flightprice.prediction.common.Result;
import com.flightprice.prediction.service.OrderService;
import com.flightprice.prediction.vo.OrderVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 订单API控制器 - 兼容前端调用路径
 */
@Api(tags = "订单API")
@RestController
@RequestMapping("/api/orders")
@RequiredArgsConstructor
public class OrderApiController {

    private final OrderService orderService;

    /**
     * 获取用户最近的订单
     */
    @ApiOperation("获取用户最近的订单")
    @GetMapping("/recent")
    public Result<List<OrderVO>> getRecentOrders(
            @ApiParam("最大数量") @RequestParam(defaultValue = "5") Integer limit) {
        List<OrderVO> recentOrders = orderService.getRecentOrders(limit);
        return Result.success(recentOrders);
    }
} 