package com.flightprice.prediction.controller;

import com.flightprice.prediction.common.Result;
import com.flightprice.prediction.entity.FlightData;
import com.flightprice.prediction.service.FlightDataService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 价格分析控制器
 */
@RestController
@RequiredArgsConstructor
@Slf4j
@Api(tags = "价格分析接口")
public class PriceController {

    private final FlightDataService flightDataService;
    private final Random random = new Random();
    private final DateTimeFormatter dateFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");

    /**
     * 获取价格历史数据
     */
    @ApiOperation("获取价格历史数据")
    @GetMapping("/price-history")
    public Result<?> getPriceHistory(
            @RequestParam(required = false) String routeCode,
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) {
        log.info("获取价格历史数据, routeCode={}, startDate={}, endDate={}, page={}, size={}", 
                routeCode, startDate, endDate, page, size);

        try {
            // 从flight_data表中获取价格历史数据
            Map<String, Object> result = new HashMap<>();
            
            // 先获取所有数据以计算总量
            List<Map<String, Object>> allData = generatePriceHistoryData(routeCode, startDate, endDate, 0, Integer.MAX_VALUE);
            int totalElements = allData.size();
            
            // 然后获取分页数据
            List<Map<String, Object>> pagedData;
            if (page * size < totalElements) {
                pagedData = allData.subList(page * size, Math.min((page + 1) * size, totalElements));
            } else {
                pagedData = new ArrayList<>();
            }
            
            result.put("content", pagedData);
            result.put("totalElements", totalElements); // 使用实际数据量
            result.put("page", page);
            result.put("size", size);
            
            return Result.success(result);
        } catch (Exception e) {
            log.error("获取价格历史数据失败", e);
            return Result.error("获取价格历史数据失败: " + e.getMessage());
        }
    }

    /**
     * 获取价格趋势数据
     */
    @ApiOperation("获取价格趋势数据")
    @GetMapping("/price-trend")
    public Result<?> getPriceTrend(
            @RequestParam(required = false) String routeCode,
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) {
        log.info("获取价格趋势数据, routeCode={}, startDate={}, endDate={}, page={}, size={}", 
                routeCode, startDate, endDate, page, size);

        try {
            // 从flight_data表中获取价格趋势数据
            Map<String, Object> result = generatePriceTrendData(routeCode, startDate, endDate, page, size);
            return Result.success(result);
        } catch (Exception e) {
            log.error("获取价格趋势数据失败", e);
            return Result.error("获取价格趋势数据失败: " + e.getMessage());
        }
    }

    /**
     * 获取价格比较数据
     */
    @ApiOperation("获取价格比较数据")
    @GetMapping("/price-comparison")
    public Result<?> getPriceComparison(
            @RequestParam(required = false) String routeCode,
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate,
            @RequestParam(required = false) String comparisonType,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) {
        log.info("获取价格比较数据, routeCode={}, startDate={}, endDate={}, comparisonType={}, page={}, size={}", 
                routeCode, startDate, endDate, comparisonType, page, size);

        try {
            // 从flight_data表中获取价格比较数据
            Map<String, Object> result = generatePriceComparisonData(routeCode, startDate, endDate, comparisonType, page, size);
            return Result.success(result);
        } catch (Exception e) {
            log.error("获取价格比较数据失败", e);
            return Result.error("获取价格比较数据失败: " + e.getMessage());
        }
    }

    /**
     * 生成价格历史数据
     */
    private List<Map<String, Object>> generatePriceHistoryData(String routeCode, String startDate, String endDate, int page, int size) {
        // 这里应该从数据库查询真实数据，这里仅做模拟
        List<Map<String, Object>> result = new ArrayList<>();
        LocalDate start = startDate != null && !startDate.isEmpty() ? LocalDate.parse(startDate) : LocalDate.now().minusMonths(1);
        LocalDate end = endDate != null && !endDate.isEmpty() ? LocalDate.parse(endDate) : LocalDate.now();

        // 默认路线：北京-上海
        String departureCity = "北京";
        String arrivalCity = "上海";
        
        // 如果提供了路线代码，解析出发城市和到达城市
        if (routeCode != null && !routeCode.isEmpty()) {
            String[] cities = routeCode.split("-");
            if (cities.length == 2) {
                departureCity = cities[0];
                arrivalCity = cities[1];
            }
        }

        // 基础价格设置为800-1200元
        double basePrice = 800 + random.nextInt(400);
        
        // 生成从起始日期到结束日期的每天价格历史
        for (LocalDate date = start; !date.isAfter(end); date = date.plusDays(1)) {
            // 根据日期添加随机波动
            double dayFactor = 0.9 + (random.nextDouble() * 0.3); // 0.9-1.2之间的随机因子
            
            // 周末价格上浮
            if (date.getDayOfWeek().getValue() >= 6) {
                dayFactor *= 1.1;
            }
            
            // 计算当天价格
            double avgPrice = basePrice * dayFactor;
            double minPrice = avgPrice * 0.9;
            double maxPrice = avgPrice * 1.1;
            
            // 成交量
            int volume = 50 + random.nextInt(100);
            
            // 趋势
            String trend;
            double randomTrend = random.nextDouble();
            if (randomTrend < 0.4) {
                trend = "上涨";
            } else if (randomTrend < 0.7) {
                trend = "下降";
            } else {
                trend = "平稳";
            }
            
            Map<String, Object> data = new HashMap<>();
            data.put("date", date.format(dateFormatter));
            data.put("route", departureCity + "-" + arrivalCity);
            data.put("minPrice", new BigDecimal(minPrice).setScale(2, RoundingMode.HALF_UP).doubleValue());
            data.put("maxPrice", new BigDecimal(maxPrice).setScale(2, RoundingMode.HALF_UP).doubleValue());
            data.put("avgPrice", new BigDecimal(avgPrice).setScale(2, RoundingMode.HALF_UP).doubleValue());
            data.put("volume", volume);
            data.put("trend", trend);
            
            result.add(data);
        }
        
        // 对结果进行分页
        int startIndex = page * size;
        if (result.size() > startIndex) {
            return result.subList(startIndex, Math.min(startIndex + size, result.size()));
        }
        
        return new ArrayList<>();
    }

    /**
     * 生成价格趋势数据
     */
    private Map<String, Object> generatePriceTrendData(String routeCode, String startDate, String endDate, int page, int size) {
        // 这里应该从数据库查询真实数据，这里仅做模拟
        Map<String, Object> result = new HashMap<>();
        
        // 生成历史数据
        List<Map<String, Object>> trendData = new ArrayList<>();
        LocalDate start = startDate != null && !startDate.isEmpty() ? LocalDate.parse(startDate) : LocalDate.now().minusMonths(1);
        LocalDate end = endDate != null && !endDate.isEmpty() ? LocalDate.parse(endDate) : LocalDate.now();
        
        // 默认路线：北京-上海
        String departureCity = "北京";
        String arrivalCity = "上海";
        
        // 如果提供了路线代码，解析出发城市和到达城市
        if (routeCode != null && !routeCode.isEmpty()) {
            String[] cities = routeCode.split("-");
            if (cities.length == 2) {
                departureCity = cities[0];
                arrivalCity = cities[1];
            }
        }
        
        // 基础价格
        double basePrice = 800 + random.nextInt(400);
        double previousPrice = basePrice;
        
        // 累计计算用于趋势分析的变量
        double totalPrice = 0;
        double minPrice = Double.MAX_VALUE;
        double maxPrice = 0;
        double volatility = 0;
        int count = 0;
        double trend = 0; // 正为上涨，负为下跌，接近0为平稳
        
        // 生成从起始日期到结束日期的每天价格趋势
        for (LocalDate date = start; !date.isAfter(end); date = date.plusDays(1)) {
            count++;
            
            // 根据日期添加随机波动，但保持连续性
            double dayFactor = 0.95 + (random.nextDouble() * 0.1); // 0.95-1.05之间的随机因子
            
            // 周末价格上浮
            if (date.getDayOfWeek().getValue() >= 6) {
                dayFactor *= 1.05;
            }
            
            // 计算当天价格，基于前一天的价格加上波动
            double price = previousPrice * dayFactor;
            previousPrice = price;
            
            // 计算价格变化
            double change = ((price / basePrice) - 1) * 100;
            
            // 统计数据
            totalPrice += price;
            minPrice = Math.min(minPrice, price);
            maxPrice = Math.max(maxPrice, price);
            volatility += Math.abs(change);
            trend += change;
            
            // 成交量
            int volume = 50 + random.nextInt(50);
            
            // 趋势分析
            String trendText;
            if (change > 1) {
                trendText = "上涨";
            } else if (change < -1) {
                trendText = "下降";
            } else {
                trendText = "平稳";
            }
            
            // 分析建议
            String analysis = generateAnalysis(trendText, change);
            
            Map<String, Object> data = new HashMap<>();
            data.put("date", date.format(dateFormatter));
            data.put("price", new BigDecimal(price).setScale(2, RoundingMode.HALF_UP).doubleValue());
            data.put("change", new BigDecimal(change).setScale(2, RoundingMode.HALF_UP).doubleValue());
            data.put("volume", volume);
            data.put("trend", trendText);
            data.put("analysis", analysis);
            
            trendData.add(data);
        }
        
        // 计算分析结果
        Map<String, Object> analysis = new HashMap<>();
        double avgPrice = totalPrice / count;
        String overallTrend;
        if (trend > 5) {
            overallTrend = "上涨";
        } else if (trend < -5) {
            overallTrend = "下降";
        } else {
            overallTrend = "平稳";
        }
        
        analysis.put("overallTrend", overallTrend);
        analysis.put("avgPrice", new BigDecimal(avgPrice).setScale(2, RoundingMode.HALF_UP).doubleValue());
        analysis.put("maxPrice", new BigDecimal(maxPrice).setScale(2, RoundingMode.HALF_UP).doubleValue());
        analysis.put("minPrice", new BigDecimal(minPrice).setScale(2, RoundingMode.HALF_UP).doubleValue());
        analysis.put("volatility", new BigDecimal(volatility / count).setScale(2, RoundingMode.HALF_UP).doubleValue());
        
        // 推荐购买时间
        String suggestedTime;
        if (overallTrend.equals("上涨")) {
            suggestedTime = "建议尽快购买，价格有上涨趋势";
        } else if (overallTrend.equals("下降")) {
            suggestedTime = "建议等待价格下降后再购买";
        } else {
            suggestedTime = "价格相对稳定，可以择优购买";
        }
        analysis.put("suggestedTime", suggestedTime);
        
        result.put("trendData", trendData);
        result.put("analysis", analysis);
        
        // 添加分页和总数
        int totalElements = trendData.size();
        List<Map<String, Object>> pagedData;
        if (page * size < totalElements) {
            pagedData = trendData.subList(page * size, Math.min((page + 1) * size, totalElements));
        } else {
            pagedData = new ArrayList<>();
        }
        
        result.put("trendData", pagedData);
        result.put("total", totalElements);
        
        return result;
    }

    /**
     * 生成价格比较数据
     */
    private Map<String, Object> generatePriceComparisonData(String routeCode, String startDate, String endDate, String comparisonType, int page, int size) {
        Map<String, Object> result = new HashMap<>();
        
        // 默认路线：北京-上海
        String departureCity = "北京";
        String arrivalCity = "上海";
        
        // 如果提供了路线代码，解析出发城市和到达城市
        if (routeCode != null && !routeCode.isEmpty()) {
            String[] cities = routeCode.split("-");
            if (cities.length == 2) {
                departureCity = cities[0];
                arrivalCity = cities[1];
            }
        }
        
        // 从数据库中获取真实数据
        List<Map<String, Object>> comparisonData = new ArrayList<>();
        
        try {
            // 构建SQL查询
            StringBuilder sql = new StringBuilder();
            List<Object> params = new ArrayList<>();
            
            if ("airline".equals(comparisonType)) {
                // 按航空公司比较
                sql.append("SELECT airline, COUNT(*) as flight_count, ")
                   .append("MIN(price) as min_price, MAX(price) as max_price, AVG(price) as avg_price ")
                   .append("FROM flight_data ")
                   .append("WHERE is_deleted = 0 AND departure_city = ? AND arrival_city = ? ");
                params.add(departureCity);
                params.add(arrivalCity);
                
                // 添加日期条件
                if (startDate != null && !startDate.isEmpty()) {
                    sql.append("AND STR_TO_DATE(flight_date, '%Y-%m-%d') >= STR_TO_DATE(?, '%Y-%m-%d') ");
                    params.add(startDate);
                }
                if (endDate != null && !endDate.isEmpty()) {
                    sql.append("AND STR_TO_DATE(flight_date, '%Y-%m-%d') <= STR_TO_DATE(?, '%Y-%m-%d') ");
                    params.add(endDate);
                }
                
                sql.append("GROUP BY airline ORDER BY avg_price");
                
                // 执行查询
                List<Map<String, Object>> queryResults = flightDataService.executeQuery(sql.toString(), params.toArray());
                
                // 处理查询结果
                for (Map<String, Object> item : queryResults) {
                    Map<String, Object> data = new HashMap<>();
                    String airline = (String) item.get("airline");
                    if (airline == null || airline.isEmpty()) continue;
                    
                    data.put("item", airline);
                    data.put("flightCount", item.get("flight_count"));
                    data.put("minPrice", item.get("min_price"));
                    data.put("maxPrice", item.get("max_price"));
                    data.put("avgPrice", item.get("avg_price"));
                    
                    // 计算当前航空公司与平均值的差价
                    double avgPrice = Double.parseDouble(item.get("avg_price").toString());
                    data.put("priceDiff", 0); // 先初始化为0
                    
                    comparisonData.add(data);
                }
                
                // 计算总平均价格
                double totalAvgPrice = 0;
                if (!comparisonData.isEmpty()) {
                    double sum = 0;
                    for (Map<String, Object> data : comparisonData) {
                        sum += Double.parseDouble(data.get("avgPrice").toString());
                    }
                    totalAvgPrice = sum / comparisonData.size();
                    
                    // 更新每个航空公司与平均值的差价
                    for (Map<String, Object> data : comparisonData) {
                        double avgPrice = Double.parseDouble(data.get("avgPrice").toString());
                        double priceDiff = avgPrice - totalAvgPrice;
                        data.put("priceDiff", new BigDecimal(priceDiff).setScale(2, RoundingMode.HALF_UP).doubleValue());
                        
                        // 添加价格等级评估
                        if (priceDiff > totalAvgPrice * 0.1) {
                            data.put("priceLevel", "偏高");
                        } else if (priceDiff < -totalAvgPrice * 0.1) {
                            data.put("priceLevel", "偏低");
                        } else {
                            data.put("priceLevel", "适中");
                        }
                    }
                }
                
            } else if ("cabin".equals(comparisonType)) {
                // 舱位比较 - 由于数据库可能没有舱位数据，使用模拟数据
                List<String> cabins = Arrays.asList("经济舱", "公务舱", "头等舱");
                BigDecimal basePrice = null;
                
                // 先查询该航线的平均价格作为基准
                sql.append("SELECT AVG(price) as avg_price ")
                   .append("FROM flight_data ")
                   .append("WHERE is_deleted = 0 AND departure_city = ? AND arrival_city = ? ");
                params.add(departureCity);
                params.add(arrivalCity);
                
                // 添加日期条件
                if (startDate != null && !startDate.isEmpty()) {
                    sql.append("AND STR_TO_DATE(flight_date, '%Y-%m-%d') >= STR_TO_DATE(?, '%Y-%m-%d') ");
                    params.add(startDate);
                }
                if (endDate != null && !endDate.isEmpty()) {
                    sql.append("AND STR_TO_DATE(flight_date, '%Y-%m-%d') <= STR_TO_DATE(?, '%Y-%m-%d') ");
                    params.add(endDate);
                }
                
                List<Map<String, Object>> avgResult = flightDataService.executeQuery(sql.toString(), params.toArray());
                if (!avgResult.isEmpty() && avgResult.get(0).get("avg_price") != null) {
                    basePrice = new BigDecimal(avgResult.get(0).get("avg_price").toString());
                } else {
                    basePrice = new BigDecimal("1000"); // 默认价格
                }
                
                // 生成舱位比较数据
                for (String cabin : cabins) {
                    Map<String, Object> data = new HashMap<>();
                    double factor;
                    
                    if ("经济舱".equals(cabin)) {
                        factor = 1.0;
                    } else if ("公务舱".equals(cabin)) {
                        factor = 2.5 + (random.nextDouble() * 0.5); // 2.5-3.0
                    } else {
                        factor = 4.0 + (random.nextDouble() * 1.0); // 4.0-5.0
                    }
                    
                    double price = basePrice.doubleValue() * factor;
                    
                    data.put("item", cabin);
                    data.put("price", new BigDecimal(price).setScale(2, RoundingMode.HALF_UP).doubleValue());
                    data.put("priceDiff", new BigDecimal(price - basePrice.doubleValue()).setScale(2, RoundingMode.HALF_UP).doubleValue());
                    data.put("factor", new BigDecimal(factor).setScale(2, RoundingMode.HALF_UP).doubleValue());
                    
                    comparisonData.add(data);
                }
            } else {
                // 按星期比较
                sql.append("SELECT DAYOFWEEK(STR_TO_DATE(flight_date, '%Y-%m-%d')) as day_of_week, ")
                   .append("COUNT(*) as flight_count, ")
                   .append("MIN(price) as min_price, MAX(price) as max_price, AVG(price) as avg_price ")
                   .append("FROM flight_data ")
                   .append("WHERE is_deleted = 0 AND departure_city = ? AND arrival_city = ? ");
                params.add(departureCity);
                params.add(arrivalCity);
                
                // 添加日期条件
                if (startDate != null && !startDate.isEmpty()) {
                    sql.append("AND STR_TO_DATE(flight_date, '%Y-%m-%d') >= STR_TO_DATE(?, '%Y-%m-%d') ");
                    params.add(startDate);
                }
                if (endDate != null && !endDate.isEmpty()) {
                    sql.append("AND STR_TO_DATE(flight_date, '%Y-%m-%d') <= STR_TO_DATE(?, '%Y-%m-%d') ");
                    params.add(endDate);
                }
                
                sql.append("GROUP BY day_of_week ORDER BY day_of_week");
                
                List<Map<String, Object>> queryResults = flightDataService.executeQuery(sql.toString(), params.toArray());
                
                String[] weekDays = {"周日", "周一", "周二", "周三", "周四", "周五", "周六"};
                
                // 计算总平均价格
                double totalAvgPrice = 0;
                int totalCount = 0;
                for (Map<String, Object> item : queryResults) {
                    if (item.get("avg_price") != null) {
                        totalAvgPrice += Double.parseDouble(item.get("avg_price").toString()) * Integer.parseInt(item.get("flight_count").toString());
                        totalCount += Integer.parseInt(item.get("flight_count").toString());
                    }
                }
                if (totalCount > 0) {
                    totalAvgPrice /= totalCount;
                }
                
                // 处理查询结果
                for (Map<String, Object> item : queryResults) {
                    Map<String, Object> data = new HashMap<>();
                    
                    int dayOfWeek = Integer.parseInt(item.get("day_of_week").toString()) - 1; // 调整为0-6
                    String dayName = weekDays[dayOfWeek];
                    
                    data.put("item", dayName);
                    data.put("flightCount", item.get("flight_count"));
                    data.put("minPrice", item.get("min_price"));
                    data.put("maxPrice", item.get("max_price"));
                    data.put("avgPrice", item.get("avg_price"));
                    
                    // 计算与平均值的差价
                    double avgPrice = Double.parseDouble(item.get("avg_price").toString());
                    double priceDiff = avgPrice - totalAvgPrice;
                    data.put("priceDiff", new BigDecimal(priceDiff).setScale(2, RoundingMode.HALF_UP).doubleValue());
                    
                    // 添加价格等级评估
                    if (priceDiff > totalAvgPrice * 0.1) {
                        data.put("priceLevel", "偏高");
                    } else if (priceDiff < -totalAvgPrice * 0.1) {
                        data.put("priceLevel", "偏低");
                    } else {
                        data.put("priceLevel", "适中");
                    }
                    
                    comparisonData.add(data);
                }
                
                // 确保所有星期都有数据
                Set<String> existingDays = comparisonData.stream()
                        .map(data -> (String) data.get("item"))
                        .collect(Collectors.toSet());
                
                for (String day : weekDays) {
                    if (!existingDays.contains(day)) {
                        Map<String, Object> data = new HashMap<>();
                        data.put("item", day);
                        data.put("flightCount", 0);
                        data.put("minPrice", 0);
                        data.put("maxPrice", 0);
                        data.put("avgPrice", 0);
                        data.put("priceDiff", 0);
                        data.put("priceLevel", "无数据");
                        comparisonData.add(data);
                    }
                }
                
                // 按周一到周日排序
                comparisonData.sort((a, b) -> {
                    String dayA = (String) a.get("item");
                    String dayB = (String) b.get("item");
                    int idxA = Arrays.asList(weekDays).indexOf(dayA);
                    int idxB = Arrays.asList(weekDays).indexOf(dayB);
                    return Integer.compare(idxA, idxB);
                });
            }
            
        } catch (Exception e) {
            log.error("获取价格比较数据失败：", e);
            // 发生错误时使用模拟数据
            comparisonData = generateMockComparisonData(comparisonType, departureCity, arrivalCity);
        }
        
        // 分页处理
        List<Map<String, Object>> pagedData;
        int totalElements = comparisonData.size();
        int fromIndex = page * size;
        int toIndex = Math.min(fromIndex + size, totalElements);
        
        if (fromIndex < totalElements) {
            pagedData = comparisonData.subList(fromIndex, toIndex);
        } else {
            pagedData = new ArrayList<>();
        }
        
        // 添加一些分析数据
        Map<String, Object> analysis = generateComparisonAnalysis(comparisonData, comparisonType);
        
        // 使用与前端对应的键名
        result.put("comparisonData", pagedData);
        result.put("total", totalElements);
        result.put("analysis", analysis);
        
        // 添加图表数据
        Map<String, Object> chartData = generateChartData(comparisonData, comparisonType);
        result.put("chartData", chartData);
        
        return result;
    }
    
    /**
     * 生成价格比较分析数据
     */
    private Map<String, Object> generateComparisonAnalysis(List<Map<String, Object>> comparisonData, String comparisonType) {
        Map<String, Object> analysis = new HashMap<>();
        
        if (comparisonData.isEmpty()) {
            analysis.put("recommendation", "没有足够数据进行分析");
            return analysis;
        }
        
        if ("airline".equals(comparisonType)) {
            // 寻找最便宜的航空公司
            Map<String, Object> cheapestAirline = comparisonData.stream()
                    .min(Comparator.comparing(item -> Double.parseDouble(item.get("avgPrice").toString())))
                    .orElse(null);
            
            if (cheapestAirline != null) {
                analysis.put("cheapestOption", cheapestAirline.get("item") + " (平均¥" + cheapestAirline.get("avgPrice") + ")");
                analysis.put("recommendation", "选择 " + cheapestAirline.get("item") + " 航空公司可以节省开支");
            }
            
            // 航空公司价格分布
            long highCount = comparisonData.stream()
                    .filter(item -> "偏高".equals(item.get("priceLevel")))
                    .count();
            long mediumCount = comparisonData.stream()
                    .filter(item -> "适中".equals(item.get("priceLevel")))
                    .count();
            long lowCount = comparisonData.stream()
                    .filter(item -> "偏低".equals(item.get("priceLevel")))
                    .count();
            
            analysis.put("priceLevelDistribution", Map.of(
                "高", highCount,
                "中", mediumCount,
                "低", lowCount
            ));
            
        } else if ("cabin".equals(comparisonType)) {
            // 舱位价格比较
            analysis.put("economyPrice", comparisonData.stream()
                .filter(item -> "经济舱".equals(item.get("item")))
                .findFirst()
                .map(item -> item.get("price"))
                .orElse(0));
                
            analysis.put("businessPrice", comparisonData.stream()
                .filter(item -> "公务舱".equals(item.get("item")))
                .findFirst()
                .map(item -> item.get("price"))
                .orElse(0));
                
            analysis.put("firstClassPrice", comparisonData.stream()
                .filter(item -> "头等舱".equals(item.get("item")))
                .findFirst()
                .map(item -> item.get("price"))
                .orElse(0));
                
            analysis.put("recommendation", "经济舱提供最佳价格/价值比");
            
        } else {
            // 星期价格比较
            
            // 寻找最便宜的星期几
            Map<String, Object> cheapestDay = comparisonData.stream()
                    .filter(item -> Integer.parseInt(item.get("flightCount").toString()) > 0) // 确保有航班数据
                    .min(Comparator.comparing(item -> Double.parseDouble(item.get("avgPrice").toString())))
                    .orElse(null);
                    
            if (cheapestDay != null) {
                analysis.put("cheapestDay", cheapestDay.get("item") + " (平均¥" + cheapestDay.get("avgPrice") + ")");
                analysis.put("recommendation", "建议选择 " + cheapestDay.get("item") + " 出行可以获得更便宜的价格");
            }
            
            // 寻找最贵的星期几
            Map<String, Object> mostExpensiveDay = comparisonData.stream()
                    .filter(item -> Integer.parseInt(item.get("flightCount").toString()) > 0) // 确保有航班数据
                    .max(Comparator.comparing(item -> Double.parseDouble(item.get("avgPrice").toString())))
                    .orElse(null);
                    
            if (mostExpensiveDay != null) {
                analysis.put("mostExpensiveDay", mostExpensiveDay.get("item") + " (平均¥" + mostExpensiveDay.get("avgPrice") + ")");
            }
            
            // 检查周末和工作日的价格差异
            double weekdayAvg = comparisonData.stream()
                    .filter(item -> {
                        String day = (String) item.get("item");
                        return !day.equals("周六") && !day.equals("周日");
                    })
                    .filter(item -> Integer.parseInt(item.get("flightCount").toString()) > 0)
                    .mapToDouble(item -> Double.parseDouble(item.get("avgPrice").toString()))
                    .average()
                    .orElse(0);
                    
            double weekendAvg = comparisonData.stream()
                    .filter(item -> {
                        String day = (String) item.get("item");
                        return day.equals("周六") || day.equals("周日");
                    })
                    .filter(item -> Integer.parseInt(item.get("flightCount").toString()) > 0)
                    .mapToDouble(item -> Double.parseDouble(item.get("avgPrice").toString()))
                    .average()
                    .orElse(0);
                    
            analysis.put("weekdayAvgPrice", new BigDecimal(weekdayAvg).setScale(2, RoundingMode.HALF_UP).doubleValue());
            analysis.put("weekendAvgPrice", new BigDecimal(weekendAvg).setScale(2, RoundingMode.HALF_UP).doubleValue());
            
            double priceDiff = weekendAvg - weekdayAvg;
            if (Math.abs(priceDiff) > 50) {
                if (priceDiff > 0) {
                    analysis.put("weekendDiff", "周末价格比工作日高 " + new BigDecimal(priceDiff).setScale(2, RoundingMode.HALF_UP).doubleValue() + " 元");
                } else {
                    analysis.put("weekendDiff", "周末价格比工作日低 " + new BigDecimal(-priceDiff).setScale(2, RoundingMode.HALF_UP).doubleValue() + " 元");
                }
            } else {
                analysis.put("weekendDiff", "周末与工作日价格相近");
            }
        }
        
        return analysis;
    }
    
    /**
     * 生成模拟比较数据（当数据库查询出错时使用）
     */
    private List<Map<String, Object>> generateMockComparisonData(String comparisonType, String departureCity, String arrivalCity) {
        List<Map<String, Object>> mockData = new ArrayList<>();
        double basePrice = 800 + random.nextInt(400);
        
        if ("airline".equals(comparisonType)) {
            String[] airlines = {"国航", "东航", "南航", "海航", "厦航", "春秋航空", "吉祥航空"};
            
            for (String airline : airlines) {
                Map<String, Object> data = new HashMap<>();
                double factor = 0.9 + (random.nextDouble() * 0.3); // 0.9-1.2之间的随机因子
                double price = basePrice * factor;
                
                data.put("item", airline);
                data.put("flightCount", 10 + random.nextInt(30));
                data.put("minPrice", new BigDecimal(price * 0.9).setScale(2, RoundingMode.HALF_UP).doubleValue());
                data.put("maxPrice", new BigDecimal(price * 1.1).setScale(2, RoundingMode.HALF_UP).doubleValue());
                data.put("avgPrice", new BigDecimal(price).setScale(2, RoundingMode.HALF_UP).doubleValue());
                data.put("priceDiff", new BigDecimal((factor - 1.0) * basePrice).setScale(2, RoundingMode.HALF_UP).doubleValue());
                
                if (factor > 1.1) {
                    data.put("priceLevel", "偏高");
                } else if (factor < 0.95) {
                    data.put("priceLevel", "偏低");
                } else {
                    data.put("priceLevel", "适中");
                }
                
                mockData.add(data);
            }
        } else if ("cabin".equals(comparisonType)) {
            String[] cabins = {"经济舱", "公务舱", "头等舱"};
            double[] factors = {1.0, 2.7, 4.5};
            
            for (int i = 0; i < cabins.length; i++) {
                Map<String, Object> data = new HashMap<>();
                double price = basePrice * factors[i];
                
                data.put("item", cabins[i]);
                data.put("price", new BigDecimal(price).setScale(2, RoundingMode.HALF_UP).doubleValue());
                data.put("priceDiff", new BigDecimal(price - basePrice).setScale(2, RoundingMode.HALF_UP).doubleValue());
                data.put("factor", factors[i]);
                
                mockData.add(data);
            }
        } else {
            String[] weekDays = {"周一", "周二", "周三", "周四", "周五", "周六", "周日"};
            
            for (String day : weekDays) {
                Map<String, Object> data = new HashMap<>();
                
                // 周末价格上浮
                double factor;
                if (day.equals("周六") || day.equals("周日")) {
                    factor = 1.1 + (random.nextDouble() * 0.2); // 1.1-1.3
                } else {
                    factor = 0.9 + (random.nextDouble() * 0.2); // 0.9-1.1
                }
                
                double price = basePrice * factor;
                int count = 5 + random.nextInt(20);
                
                data.put("item", day);
                data.put("flightCount", count);
                data.put("minPrice", new BigDecimal(price * 0.9).setScale(2, RoundingMode.HALF_UP).doubleValue());
                data.put("maxPrice", new BigDecimal(price * 1.1).setScale(2, RoundingMode.HALF_UP).doubleValue());
                data.put("avgPrice", new BigDecimal(price).setScale(2, RoundingMode.HALF_UP).doubleValue());
                data.put("priceDiff", new BigDecimal((factor - 1.0) * basePrice).setScale(2, RoundingMode.HALF_UP).doubleValue());
                
                if (factor > 1.1) {
                    data.put("priceLevel", "偏高");
                } else if (factor < 0.95) {
                    data.put("priceLevel", "偏低");
                } else {
                    data.put("priceLevel", "适中");
                }
                
                mockData.add(data);
            }
        }
        
        return mockData;
    }

    /**
     * 生成价格趋势分析文本
     */
    private String generateAnalysis(String trend, double change) {
        if ("上涨".equals(trend)) {
            if (change > 5) {
                return "价格大幅上涨，建议等待回落后再购买";
            } else {
                return "价格小幅上涨，密切关注价格动向";
            }
        } else if ("下降".equals(trend)) {
            if (change < -5) {
                return "价格大幅下降，可能是好的购买时机";
            } else {
                return "价格小幅下降，继续观察价格走势";
            }
        } else {
            return "价格相对稳定，可根据自身需求决定是否购买";
        }
    }

    /**
     * 导出价格历史数据
     */
    @ApiOperation("导出价格历史数据")
    @GetMapping("/price-history/export")
    public Result<?> exportPriceHistory(
            @RequestParam(required = false) String routeCode,
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate) {
        log.info("导出价格历史数据, routeCode={}, startDate={}, endDate={}", routeCode, startDate, endDate);
        // 这里应该实现导出逻辑
        return Result.success("导出成功");
    }

    /**
     * 导出价格趋势数据
     */
    @ApiOperation("导出价格趋势数据")
    @GetMapping("/price-trend/export")
    public Result<?> exportPriceTrend(
            @RequestParam(required = false) String routeCode,
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate) {
        log.info("导出价格趋势数据, routeCode={}, startDate={}, endDate={}", routeCode, startDate, endDate);
        // 这里应该实现导出逻辑
        return Result.success("导出成功");
    }

    /**
     * 导出价格比较数据
     */
    @ApiOperation("导出价格比较数据")
    @GetMapping("/price-comparison/export")
    public Result<?> exportPriceComparison(
            @RequestParam(required = false) String routeCode,
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate,
            @RequestParam(required = false) String comparisonType) {
        log.info("导出价格比较数据, routeCode={}, startDate={}, endDate={}, comparisonType={}", 
                routeCode, startDate, endDate, comparisonType);
        // 这里应该实现导出逻辑
        return Result.success("导出成功");
    }

    /**
     * 生成价格比较图表数据
     */
    private Map<String, Object> generateChartData(List<Map<String, Object>> comparisonData, String comparisonType) {
        Map<String, Object> chartData = new HashMap<>();
        List<String> categories = new ArrayList<>();
        List<Map<String, Object>> series = new ArrayList<>();

        if (comparisonData.isEmpty()) {
            // 返回空数据结构
            chartData.put("categories", categories);
            chartData.put("series", series);
            return chartData;
        }

        if ("airline".equals(comparisonType)) {
            // 航空公司价格比较图表
            Map<String, Object> seriesData = new HashMap<>();
            seriesData.put("name", "价格");
            List<Double> data = new ArrayList<>();
            
            for (Map<String, Object> item : comparisonData) {
                categories.add((String) item.get("item"));
                data.add(Double.parseDouble(item.get("avgPrice").toString()));
            }
            
            seriesData.put("data", data);
            series.add(seriesData);
            
        } else if ("cabin".equals(comparisonType)) {
            // 舱位价格比较图表
            Map<String, Object> seriesData = new HashMap<>();
            seriesData.put("name", "价格");
            List<Double> data = new ArrayList<>();
            
            for (Map<String, Object> item : comparisonData) {
                categories.add((String) item.get("item"));
                
                // 对于舱位类型，价格可能存储在不同的字段名
                Object price = item.get("price");
                if (price == null) {
                    price = item.get("avgPrice");
                }
                
                data.add(Double.parseDouble(price.toString()));
            }
            
            seriesData.put("data", data);
            series.add(seriesData);
            
        } else {
            // 按星期比较价格图表
            Map<String, Object> priceSeriesData = new HashMap<>();
            priceSeriesData.put("name", "平均价格");
            List<Double> priceData = new ArrayList<>();
            
            Map<String, Object> countSeriesData = new HashMap<>();
            countSeriesData.put("name", "航班数量");
            List<Double> countData = new ArrayList<>();
            
            for (Map<String, Object> item : comparisonData) {
                categories.add((String) item.get("item"));
                
                Double avgPrice = Double.parseDouble(item.get("avgPrice").toString());
                Integer flightCount = Integer.parseInt(item.get("flightCount").toString());
                
                priceData.add(avgPrice);
                countData.add(flightCount.doubleValue());
            }
            
            priceSeriesData.put("data", priceData);
            countSeriesData.put("data", countData);
            
            series.add(priceSeriesData);
            series.add(countSeriesData);
        }
        
        chartData.put("categories", categories);
        chartData.put("series", series);
        return chartData;
    }
} 