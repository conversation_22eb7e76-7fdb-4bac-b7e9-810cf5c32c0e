package com.flightprice.prediction.controller;

import com.flightprice.prediction.common.result.R;
import com.flightprice.prediction.entity.User;
import com.flightprice.prediction.service.UserService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Optional;

/**
 * JPA和MyBatis-Plus功能演示控制器
 */
@RestController
@RequestMapping("/demo")
@Api(tags = "JPA和MyBatis-Plus功能演示")
public class JpaDemoController {

    @Autowired
    private UserService userService;

    /**
     * 使用JPA保存用户
     */
    @PostMapping("/jpa/user")
    @ApiOperation("使用JPA保存用户")
    public R<User> saveUserWithJpa(@RequestBody User user) {
        User savedUser = userService.saveUserWithJpa(user);
        return R.ok(savedUser);
    }

    /**
     * 使用MyBatis-Plus保存用户
     */
    @PostMapping("/mybatis/user")
    @ApiOperation("使用MyBatis-Plus保存用户")
    public R<Boolean> saveUserWithMybatis(@RequestBody User user) {
        boolean result = userService.saveUserWithMybatis(user);
        return R.ok(result);
    }

    /**
     * 使用JPA根据用户名查询用户
     */
    @GetMapping("/jpa/user/{username}")
    @ApiOperation("使用JPA根据用户名查询用户")
    public R<User> findUserByUsername(@PathVariable String username) {
        Optional<User> user = userService.findUserByUsername(username);
        return user.map(R::ok).orElseGet(() -> R.fail("用户不存在"));
    }

    /**
     * 使用MyBatis-Plus复杂查询
     */
    @GetMapping("/mybatis/users")
    @ApiOperation("使用MyBatis-Plus复杂查询")
    public R<List<User>> findUsersByCondition(
            @RequestParam(required = false) String role,
            @RequestParam(required = false) String keyword) {
        List<User> users = userService.findUsersByCondition(role, keyword);
        return R.ok(users);
    }
} 