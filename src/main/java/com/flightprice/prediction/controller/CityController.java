package com.flightprice.prediction.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.flightprice.prediction.common.Result;
import com.flightprice.prediction.entity.City;
import com.flightprice.prediction.mapper.CityMapper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 城市控制器
 */
@Api(tags = "城市管理")
@RestController
@RequestMapping("/cities")
public class CityController {

    private final CityMapper cityMapper;

    public CityController(CityMapper cityMapper) {
        this.cityMapper = cityMapper;
    }

    @ApiOperation("获取城市列表")
    @GetMapping
    public Result<List<City>> getCities() {
        List<City> cities = cityMapper.selectList(new LambdaQueryWrapper<City>()
                .orderByAsc(City::getCode));
        return Result.success(cities);
    }
    
    @ApiOperation("获取所有城市列表")
    @GetMapping("/all")
    public Result<List<City>> getAllCities() {
        List<City> cities = cityMapper.selectList(new LambdaQueryWrapper<City>()
                .orderByAsc(City::getCode));
        return Result.success(cities);
    }
}
