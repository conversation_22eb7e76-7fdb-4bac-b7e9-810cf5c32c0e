package com.flightprice.prediction.controller;

import com.flightprice.prediction.common.Result;
import com.flightprice.prediction.dto.ModelDTO;
import com.flightprice.prediction.dto.ModelTrainingHistoryDTO;
import com.flightprice.prediction.entity.Model;
import com.flightprice.prediction.service.ModelService;
import com.flightprice.prediction.vo.ModelVO;
import com.flightprice.prediction.vo.ResultVO;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 预测模型控制器
 */
@RestController
@RequestMapping("/models")
@RequiredArgsConstructor
public class ModelController {

    private final ModelService modelService;

    /**
     * 分页查询模型列表
     *
     * @param page     页码
     * @param size     每页大小
     * @param keyword  关键字
     * @param sortBy   排序字段
     * @param sortDir  排序方向
     * @return 模型分页列表
     */
    @GetMapping
    public ResponseEntity<ResultVO<Page<ModelVO>>> getModels(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(required = false) String keyword,
            @RequestParam(defaultValue = "id") String sortBy,
            @RequestParam(defaultValue = "asc") String sortDir) {

        Sort sort = Sort.by(Sort.Direction.fromString(sortDir), sortBy);
        Pageable pageable = PageRequest.of(page, size, sort);
        Page<Model> modelPage = modelService.findAll(keyword, pageable);

        Page<ModelVO> voPage = modelPage.map(model -> {
            ModelVO vo = new ModelVO();
            ModelDTO dto = modelService.convertToDTO(model);

            // 复制基本属性
            vo.setId(dto.getId());
            vo.setName(dto.getName());
            vo.setType(dto.getType());
            vo.setDescription(dto.getDescription());
            vo.setParameters(dto.getParameters());
            vo.setFeatures(dto.getFeatures());
            vo.setVersion(dto.getVersion());
            vo.setStatus(dto.getStatus());
            vo.setTrainingSize(dto.getTrainingSize());
            vo.setTestSize(dto.getTestSize());
            vo.setAccuracy(dto.getAccuracy());
            vo.setMeanError(dto.getMeanError());
            vo.setIsActive(dto.getIsActive());
            vo.setFilePath(dto.getFilePath());
            vo.setCreatedAt(dto.getCreatedAt());
            vo.setUpdatedAt(dto.getUpdatedAt());
            vo.setLastTrainedAt(dto.getLastTrainedAt());
            vo.setLastDeployedAt(dto.getLastDeployedAt());
            vo.setCreatedBy(dto.getCreatedBy());
            vo.setCreatedByName(dto.getCreatedByName());

            // 获取统计数据
            try {
                Map<String, Object> statistics = modelService.getModelStatistics(model.getId());
                vo.setTrainingCount((Integer) statistics.getOrDefault("trainingCount", 0));
                vo.setAverageAccuracy((Double) statistics.getOrDefault("averageAccuracy", 0.0));
                vo.setBestAccuracy((Double) statistics.getOrDefault("bestAccuracy", 0.0));
                vo.setWorstAccuracy((Double) statistics.getOrDefault("worstAccuracy", 0.0));
                vo.setLastTrainingTime((java.time.LocalDateTime) statistics.get("lastTrainingTime"));
            } catch (Exception e) {
                // 如果获取统计数据失败，忽略错误
            }

            return vo;
        });

        return ResponseEntity.ok(ResultVO.success(voPage));
    }

    /**
     * 根据ID获取模型详情
     *
     * @param id 模型ID
     * @return 模型详情
     */
    @GetMapping("/{id}")
    public ResponseEntity<ResultVO<ModelDTO>> getModel(@PathVariable Long id) {
        Model model = modelService.findById(id);
        ModelDTO dto = modelService.convertToDTO(model);
        return ResponseEntity.ok(ResultVO.success(dto));
    }

    /**
     * 创建模型
     *
     * @param modelDTO 模型DTO
     * @return 创建的模型
     */
    @PostMapping
    public ResponseEntity<ResultVO<ModelDTO>> createModel(@Valid @RequestBody ModelDTO modelDTO) {
        Model model = modelService.createModel(modelDTO);
        ModelDTO dto = modelService.convertToDTO(model);
        return ResponseEntity.ok(ResultVO.success(dto));
    }

    /**
     * 更新模型
     *
     * @param id       模型ID
     * @param modelDTO 模型DTO
     * @return 更新后的模型
     */
    @PutMapping("/{id}")
    public ResponseEntity<ResultVO<ModelDTO>> updateModel(
            @PathVariable Long id, @Valid @RequestBody ModelDTO modelDTO) {
        Model model = modelService.updateModel(id, modelDTO);
        ModelDTO dto = modelService.convertToDTO(model);
        return ResponseEntity.ok(ResultVO.success(dto));
    }

    /**
     * 删除模型
     *
     * @param id 模型ID
     * @return 删除结果
     */
    @DeleteMapping("/{id}")
    public ResponseEntity<ResultVO<Void>> deleteModel(@PathVariable Long id) {
        modelService.deleteModel(id);
        return ResponseEntity.ok(ResultVO.success());
    }

    /**
     * 部署/激活模型
     *
     * @param id 模型ID
     * @return 部署结果
     */
    @PostMapping("/{id}/deploy")
    public ResponseEntity<ResultVO<ModelDTO>> deployModel(@PathVariable Long id) {
        Model model = modelService.deployModel(id);
        ModelDTO dto = modelService.convertToDTO(model);
        return ResponseEntity.ok(ResultVO.success(dto));
    }

    /**
     * 停用模型
     *
     * @param id 模型ID
     * @return 停用结果
     */
    @PostMapping("/{id}/deactivate")
    public ResponseEntity<ResultVO<ModelDTO>> deactivateModel(@PathVariable Long id) {
        Model model = modelService.deactivateModel(id);
        ModelDTO dto = modelService.convertToDTO(model);
        return ResponseEntity.ok(ResultVO.success(dto));
    }

    /**
     * 训练模型
     *
     * @param id         模型ID
     * @param parameters 训练参数
     * @return 训练历史ID
     */
    @PostMapping("/{id}/train")
    public ResponseEntity<ResultVO<Long>> trainModel(
            @PathVariable Long id, @RequestBody(required = false) Map<String, Object> parameters) {
        Long trainingHistoryId = modelService.trainModel(id, parameters);
        return ResponseEntity.ok(ResultVO.success(trainingHistoryId));
    }

    /**
     * 获取模型训练历史
     *
     * @param id        模型ID
     * @param page      页码
     * @param size      每页大小
     * @return 训练历史分页列表
     */
    @GetMapping("/{id}/training-histories")
    public ResponseEntity<ResultVO<Page<ModelTrainingHistoryDTO>>> getTrainingHistories(
            @PathVariable Long id,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) {

        Pageable pageable = PageRequest.of(page, size, Sort.by(Sort.Direction.DESC, "createdAt"));
        Page<ModelTrainingHistoryDTO> histories = modelService.getTrainingHistories(id, pageable);
        return ResponseEntity.ok(ResultVO.success(histories));
    }

    /**
     * 获取训练历史详情
     *
     * @param id 训练历史ID
     * @return 训练历史详情
     */
    @GetMapping("/training-histories/{id}")
    public ResponseEntity<ResultVO<ModelTrainingHistoryDTO>> getTrainingHistory(@PathVariable Long id) {
        ModelTrainingHistoryDTO history = modelService.getTrainingHistory(id);
        return ResponseEntity.ok(ResultVO.success(history));
    }

    /**
     * 获取模型评估指标
     *
     * @param id 模型ID
     * @return 评估指标
     */
    @GetMapping("/{id}/evaluation")
    public ResponseEntity<ResultVO<Map<String, Object>>> getModelEvaluation(@PathVariable Long id) {
        Map<String, Object> evaluation = modelService.getModelEvaluation(id);
        return ResponseEntity.ok(ResultVO.success(evaluation));
    }

    /**
     * 获取模型统计数据
     *
     * @param id 模型ID
     * @return 统计数据
     */
    @GetMapping("/{id}/statistics")
    public ResponseEntity<ResultVO<Map<String, Object>>> getModelStatistics(@PathVariable Long id) {
        Map<String, Object> statistics = modelService.getModelStatistics(id);
        return ResponseEntity.ok(ResultVO.success(statistics));
    }

    /**
     * 获取当前激活的模型
     *
     * @return 激活的模型
     */
    @GetMapping("/active")
    public ResponseEntity<ResultVO<ModelDTO>> getActiveModel() {
        Model model = modelService.getActiveModel();
        ModelDTO dto = modelService.convertToDTO(model);
        return ResponseEntity.ok(ResultVO.success(dto));
    }

    /**
     * 获取所有支持的模型ID列表
     * 
     * @return 支持的模型ID列表
     */
    @GetMapping("/supported-model-ids")
    public ResponseEntity<ResultVO<List<Map<String, String>>>> getSupportedModelIds() {
        // 从ModelFactory获取支持的模型ID列表
        List<Map<String, String>> modelIds = List.of(
            Map.of("id", "default-model", "name", "CNN深度学习模型", "description", "默认的卷积神经网络模型，提供较高的预测准确度"),
            Map.of("id", "random-forest", "name", "随机森林模型", "description", "使用随机森林算法进行预测，适合处理多种特征的数据"),
            Map.of("id", "linear-regression", "name", "线性回归模型", "description", "简单的线性回归模型，计算速度快但准确度较低"),
            Map.of("id", "xgboost", "name", "XGBoost模型", "description", "梯度提升模型，平衡了准确度和速度"),
            Map.of("id", "lstm", "name", "LSTM时序模型", "description", "长短期记忆网络，适合处理时间序列数据")
        );
        
        return ResponseEntity.ok(ResultVO.success(modelIds));
    }

    /**
     * 获取模型类型列表
     *
     * @return 模型类型列表
     */
    @GetMapping("/types")
    public ResponseEntity<ResultVO<List<String>>> getModelTypes() {
        List<String> types = modelService.getModelTypes();
        return ResponseEntity.ok(ResultVO.success(types));
    }

    /**
     * 根据状态获取模型列表
     *
     * @param status 状态
     * @return 模型列表
     */
    @GetMapping("/status/{status}")
    public ResponseEntity<ResultVO<List<ModelDTO>>> getModelsByStatus(@PathVariable String status) {
        List<Model> models = modelService.getModelsByStatus(status);
        List<ModelDTO> dtos = models.stream()
                .map(modelService::convertToDTO)
                .collect(Collectors.toList());
        return ResponseEntity.ok(ResultVO.success(dtos));
    }
} 