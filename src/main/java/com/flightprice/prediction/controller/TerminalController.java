package com.flightprice.prediction.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.flightprice.prediction.common.api.CommonPage;
import com.flightprice.prediction.common.api.CommonResult;
import com.flightprice.prediction.dto.TerminalDTO;
import com.flightprice.prediction.entity.Terminal;
import com.flightprice.prediction.service.TerminalService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 航站楼管理控制器
 */
@Api(tags = "航站楼管理")
@RestController
@RequestMapping("/api/terminals")
public class TerminalController {

    @Autowired
    private TerminalService terminalService;

    @ApiOperation("分页查询航站楼")
    @GetMapping("/list")
    public CommonResult<CommonPage<Terminal>> list(
            @RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum,
            @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize,
            @RequestParam(value = "name", required = false) String name,
            @RequestParam(value = "airportId", required = false) Long airportId,
            @RequestParam(value = "status", required = false) Integer status) {
        
        Page<Terminal> page = new Page<>(pageNum, pageSize);
        TerminalDTO terminalDTO = new TerminalDTO();
        terminalDTO.setName(name);
        terminalDTO.setAirportId(airportId);
        terminalDTO.setStatus(status);
        
        IPage<Terminal> result = terminalService.queryTerminalPage(page, terminalDTO);
        return CommonResult.success(CommonPage.restPage(result));
    }

    @ApiOperation("获取航站楼详情")
    @GetMapping("/{id}")
    public CommonResult<TerminalDTO> getItem(@PathVariable Long id) {
        TerminalDTO terminalDTO = terminalService.getTerminalById(id);
        return CommonResult.success(terminalDTO);
    }

    @ApiOperation("添加航站楼")
    @PostMapping
    @PreAuthorize("hasAuthority('terminal:create')")
    public CommonResult<Boolean> add(@Validated @RequestBody TerminalDTO terminalDTO) {
        boolean success = terminalService.saveOrUpdateTerminal(terminalDTO);
        if (success) {
            return CommonResult.success(true);
        } else {
            return CommonResult.failed();
        }
    }

    @ApiOperation("修改航站楼")
    @PutMapping("/{id}")
    @PreAuthorize("hasAuthority('terminal:update')")
    public CommonResult<Boolean> update(@PathVariable Long id, @Validated @RequestBody TerminalDTO terminalDTO) {
        terminalDTO.setId(id);
        boolean success = terminalService.saveOrUpdateTerminal(terminalDTO);
        if (success) {
            return CommonResult.success(true);
        } else {
            return CommonResult.failed();
        }
    }

    @ApiOperation("修改航站楼状态")
    @PutMapping("/{id}/status/{status}")
    @PreAuthorize("hasAuthority('terminal:update')")
    public CommonResult<Boolean> updateStatus(@PathVariable Long id, @PathVariable Integer status) {
        boolean success = terminalService.updateStatus(id, status);
        if (success) {
            return CommonResult.success(true);
        } else {
            return CommonResult.failed();
        }
    }

    @ApiOperation("删除航站楼")
    @DeleteMapping("/{id}")
    @PreAuthorize("hasAuthority('terminal:delete')")
    public CommonResult<Boolean> delete(@PathVariable Long id) {
        boolean success = terminalService.deleteTerminal(id);
        if (success) {
            return CommonResult.success(true);
        } else {
            return CommonResult.failed();
        }
    }
} 