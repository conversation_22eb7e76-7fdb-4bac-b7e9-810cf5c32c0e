package com.flightprice.prediction.controller;

import com.flightprice.prediction.common.Result;
import com.flightprice.prediction.entity.FlightData;
import com.flightprice.prediction.service.FlightDataService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.Resource;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.StandardCopyOption;
import java.util.List;

/**
 * Spark数据处理控制器
 * 提供Spark数据处理相关的API
 */
@Api(tags = "Spark数据处理")
@RestController
@RequestMapping("/api/spark")
@Slf4j
public class SparkDataController {

    @Autowired
    private FlightDataService flightDataService;

    /**
     * 使用Spark处理Excel数据并导入到MySQL
     *
     * @param file Excel文件
     * @return 处理结果
     */
    @ApiOperation("使用Spark处理Excel数据并导入到MySQL")
    @PostMapping(value = "/process-excel", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public Result<Integer> processExcelWithSpark(
            @ApiParam(value = "Excel文件", required = true) @RequestPart("file") MultipartFile file) {
        try {
            log.info("接收到Excel文件上传请求，文件名: {}, 大小: {} bytes", file.getOriginalFilename(), file.getSize());

            // 将上传的文件保存到临时目录
            Path tempFile = Files.createTempFile("upload_", ".xlsx");
            file.transferTo(tempFile.toFile());
            String filePath = tempFile.toAbsolutePath().toString();

            // 使用Spark处理Excel数据
            int count = flightDataService.importFromExcel(filePath);

            // 删除临时文件
            Files.deleteIfExists(tempFile);

            return Result.success("成功处理" + count + "条数据", count);
        } catch (Exception e) {
            log.error("处理Excel文件失败: {}", e.getMessage(), e);
            return Result.error("处理Excel文件失败: " + e.getMessage());
        }
    }

    /**
     * 使用Spark分析航线价格
     *
     * @param departureCity 出发城市
     * @param arrivalCity 到达城市
     * @return 分析结果
     */
    @ApiOperation("使用Spark分析航线价格")
    @GetMapping("/analyze-route")
    public Result<List<FlightData>> analyzeRoutePrices(
            @ApiParam(value = "出发城市", required = true) @RequestParam String departureCity,
            @ApiParam(value = "到达城市", required = true) @RequestParam String arrivalCity) {
        try {
            log.info("接收到航线价格分析请求: {} -> {}", departureCity, arrivalCity);
            List<FlightData> result = flightDataService.analyzeRoutePrices(departureCity, arrivalCity);
            return Result.success("成功分析" + result.size() + "条航线数据", result);
        } catch (Exception e) {
            log.error("分析航线价格失败: {}", e.getMessage(), e);
            return Result.error("分析航线价格失败: " + e.getMessage());
        }
    }

    /**
     * 使用Spark处理示例数据
     *
     * @return 处理结果
     */
    @ApiOperation("使用Spark处理示例数据")
    @GetMapping("/process-sample")
    public Result<Integer> processSampleData() {
        try {
            log.info("开始处理示例数据");

            // 从classpath中获取数据文件
            ClassPathResource resource = new ClassPathResource("data/中国航空航班数据.xlsx");
            if (!resource.exists()) {
                return Result.error("示例数据文件不存在");
            }

            // 将资源文件复制到临时目录
            Path tempFile = Files.createTempFile("flight_data_", ".xlsx");
            Files.copy(resource.getInputStream(), tempFile, StandardCopyOption.REPLACE_EXISTING);
            String filePath = tempFile.toAbsolutePath().toString();

            // 使用Spark处理Excel数据
            int count = flightDataService.importFromExcel(filePath);

            // 删除临时文件
            Files.deleteIfExists(tempFile);

            return Result.success("成功处理" + count + "条示例数据", count);
        } catch (Exception e) {
            log.error("处理示例数据失败: {}", e.getMessage(), e);
            return Result.error("处理示例数据失败: " + e.getMessage());
        }
    }
} 