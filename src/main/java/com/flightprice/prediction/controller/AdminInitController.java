package com.flightprice.prediction.controller;

import com.flightprice.prediction.common.Result;
import com.flightprice.prediction.util.AdminUserInitializer;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 管理员初始化控制器
 * 注意：此控制器仅用于开发和测试环境，生产环境应该禁用
 */
@RestController
@RequestMapping("/init")
public class AdminInitController {

    @Autowired
    private AdminUserInitializer adminUserInitializer;

    /**
     * 初始化管理员用户
     * 访问此接口会触发管理员账号的创建
     */
    @GetMapping("/admin")
    public Result<String> initAdmin() {
        try {
            adminUserInitializer.run();
            return Result.success("管理员账号初始化成功。账号: admin，密码: admin");
        } catch (Exception e) {
            return Result.error("管理员账号初始化失败: " + e.getMessage());
        }
    }
}