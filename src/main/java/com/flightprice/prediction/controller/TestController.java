package com.flightprice.prediction.controller;

import com.flightprice.prediction.common.Result;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 测试控制器
 */
@RestController
@RequestMapping("/api/test")
public class TestController {

    @GetMapping("/public")
    public Result<String> publicEndpoint() {
        return Result.success("这是一个公开的接口，无需认证");
    }

    @GetMapping("/private")
    public Result<String> privateEndpoint() {
        return Result.success("这是一个私有的接口，需要认证");
    }
}
