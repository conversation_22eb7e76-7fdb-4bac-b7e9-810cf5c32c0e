package com.flightprice.prediction.controller;

import com.flightprice.prediction.common.Result;
import com.flightprice.prediction.service.FlightDataService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import java.util.List;
import java.util.Set;
import com.flightprice.prediction.common.PageResult;
import com.flightprice.prediction.dto.FlightDataSearchRequest;
import com.flightprice.prediction.vo.FlightDataVO;
import com.flightprice.prediction.service.FlightService;
import com.flightprice.prediction.vo.FlightVO;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.Map;

/**
 * 航班数据控制器
 * 提供航班数据相关的API
 */
@Api(tags = "航班数据管理")
@RestController
@RequestMapping("/flight-data")
@RequiredArgsConstructor
@Slf4j
public class FlightDataController {

    private final FlightDataService flightDataService;

    @Resource
    private FlightService flightService;

    @ApiOperation("获取所有出发城市")
    @GetMapping("/departure-cities")
    public Result<List<String>> getAllDepartureCities() {
        return Result.success(flightDataService.getAllDepartureCities());
    }

    @ApiOperation("获取所有到达城市")
    @GetMapping("/arrival-cities")
    public Result<List<String>> getAllArrivalCities() {
        return Result.success(flightDataService.getAllArrivalCities());
    }

    @ApiOperation("获取所有航空公司")
    @GetMapping("/airlines")
    public Result<List<String>> getAllAirlines() {
        return Result.success(flightDataService.getAllAirlines());
    }

    @ApiOperation("根据出发城市获取可到达的城市")
    @GetMapping("/arrival-cities/{departureCity}")
    public Result<List<String>> getArrivalCitiesByDepartureCity(@PathVariable String departureCity) {
        return Result.success(flightDataService.getArrivalCitiesByDepartureCity(departureCity));
    }

    @ApiOperation("搜索航班数据")
    @PostMapping("/search")
    public Result<PageResult<FlightDataVO>> searchFlightData(@Valid @RequestBody FlightDataSearchRequest request) {
        return Result.success(flightDataService.searchFlightData(
                request.getDepartureCity(),
                request.getArrivalCity(),
                request.getDepartureDate(),
                request.getAirline(),
                request.getPageNum(),
                request.getPageSize()
        ));
    }

    @ApiOperation("获取指定月份有航班的日期")
    @GetMapping("/available-dates")
    public Result<Set<String>> getAvailableDatesForMonth(
            @RequestParam String departureCity,
            @RequestParam String arrivalCity,
            @RequestParam int year,
            @RequestParam int month) {
        return Result.success(flightDataService.getAvailableDatesForMonth(
                departureCity, arrivalCity, year, month));
    }

    @ApiOperation("获取航班详情")
    @GetMapping("/{id}")
    public Result<FlightDataVO> getFlightById(@PathVariable Long id) {
        log.info("通过/flight-data/{}获取航班详情", id);
        FlightDataVO flightData = flightDataService.getFlightById(id);
        if (flightData == null) {
            log.warn("航班不存在, id: {}", id);
            return Result.fail("航班不存在");
        }
        return Result.success(flightData);
    }

    @ApiOperation("获取热门航线列表")
    @GetMapping("/hot-routes")
    public Result<List<Map<String, Object>>> getHotRoutes(
            @RequestParam(defaultValue = "10") Integer limit) {
        return Result.success(flightDataService.getHotRoutes(limit));
    }
    
    @ApiOperation("获取特价机票列表")
    @GetMapping("/promotion-tickets")
    public Result<List<Map<String, Object>>> getPromotionTickets(
            @RequestParam(defaultValue = "50") Integer limit) {
        return Result.success(flightDataService.getPromotionTickets(limit));
    }
    
    @ApiOperation("获取航班时刻表数据")
    @GetMapping("/schedules")
    public Result<PageResult<Map<String, Object>>> getFlightSchedules(
            @RequestParam(required = false) String departureCity,
            @RequestParam(required = false) String arrivalCity,
            @RequestParam(required = false) String date,
            @RequestParam(defaultValue = "1") Integer pageNum,
            @RequestParam(defaultValue = "10") Integer pageSize) {
        return Result.success(flightDataService.getFlightSchedules(
                departureCity, arrivalCity, date, pageNum, pageSize));
    }
    
    @ApiOperation("获取季节性航班数据")
    @GetMapping("/seasonal-flights")
    public Result<PageResult<Map<String, Object>>> getSeasonalFlights(
            @RequestParam(required = false) String season,
            @RequestParam(defaultValue = "1") Integer pageNum,
            @RequestParam(defaultValue = "10") Integer pageSize) {
        return Result.success(flightDataService.getSeasonalFlights(season, pageNum, pageSize));
    }
    
    @ApiOperation("获取热门城市航班数据")
    @GetMapping("/international-flights")
    public Result<PageResult<Map<String, Object>>> getInternationalFlights(
            @RequestParam(required = false) String departureCity,
            @RequestParam(required = false) String arrivalCity,
            @RequestParam(defaultValue = "1") Integer pageNum,
            @RequestParam(defaultValue = "10") Integer pageSize) {
        return Result.success(flightDataService.getInternationalFlights(
                departureCity, arrivalCity, pageNum, pageSize));
    }
}
