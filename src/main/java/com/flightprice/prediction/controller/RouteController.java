package com.flightprice.prediction.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.flightprice.prediction.common.Result;
import com.flightprice.prediction.entity.City;
import com.flightprice.prediction.entity.Route;
import com.flightprice.prediction.mapper.CityMapper;
import com.flightprice.prediction.mapper.RouteMapper;
import com.flightprice.prediction.vo.RouteVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;

/**
 * 航线控制器
 */
@Api(tags = "航线管理")
@RestController
@RequestMapping("/routes")
@Slf4j
public class RouteController {

    private final RouteMapper routeMapper;
    private final CityMapper cityMapper;

    public RouteController(RouteMapper routeMapper, CityMapper cityMapper) {
        this.routeMapper = routeMapper;
        this.cityMapper = cityMapper;
    }

    @ApiOperation("获取航线列表")
    @GetMapping
    public Result<List<RouteVO>> getRoutes() {
        List<Route> routes = routeMapper.selectList(new LambdaQueryWrapper<Route>());
        return Result.success(convertToRouteVOList(routes));
    }
    
    @ApiOperation("获取所有航线列表")
    @GetMapping("/all")
    public Result<List<RouteVO>> getAllRoutes() {
        List<Route> routes = routeMapper.selectList(new LambdaQueryWrapper<Route>());
        List<RouteVO> routeVOList = convertToRouteVOList(routes);
        log.info("获取所有航线列表，共 {} 条航线", routeVOList.size());
        return Result.success(routeVOList);
    }
    
    /**
     * 将Route实体列表转换为RouteVO列表
     */
    private List<RouteVO> convertToRouteVOList(List<Route> routes) {
        List<RouteVO> routeVOList = new ArrayList<>();
        for (Route route : routes) {
            RouteVO routeVO = new RouteVO();
            BeanUtils.copyProperties(route, routeVO);
            
            // 查询出发城市
            City departureCity = cityMapper.selectById(route.getDepartureCityId());
            if (departureCity != null) {
                routeVO.setDepartureCityName(departureCity.getName());
            }
            
            // 查询到达城市
            City arrivalCity = cityMapper.selectById(route.getArrivalCityId());
            if (arrivalCity != null) {
                routeVO.setArrivalCityName(arrivalCity.getName());
            }
            
            routeVOList.add(routeVO);
        }
        return routeVOList;
    }
} 