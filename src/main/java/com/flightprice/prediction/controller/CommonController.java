package com.flightprice.prediction.controller;

import com.flightprice.prediction.common.Result;
import com.flightprice.prediction.service.impl.DataDashboardServiceImpl;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 通用数据控制器
 * 提供前端下拉框所需的数据
 */
@RestController
@RequestMapping("/api/common")
public class CommonController {

    private final DataDashboardServiceImpl dataDashboardService;

    public CommonController(DataDashboardServiceImpl dataDashboardService) {
        this.dataDashboardService = dataDashboardService;
    }

    /**
     * 获取城市列表
     */
    @GetMapping("/cities")
    public Result<List<Map<String, String>>> getCities() {
        // 使用DataDashboardServiceImpl中的城市数据
        List<Map<String, String>> cities = Arrays.stream(DataDashboardServiceImpl.CITIES)
                .map(city -> Map.of(
                        "cityName", city,
                        "cityCode", city.substring(0, 1).toUpperCase()
                ))
                .collect(Collectors.toList());
        return Result.success(cities);
    }

    /**
     * 获取航空公司列表
     */
    @GetMapping("/airlines")
    public Result<List<Map<String, String>>> getAirlines() {
        // 使用DataDashboardServiceImpl中的航空公司数据
        List<Map<String, String>> airlines = Arrays.stream(DataDashboardServiceImpl.AIRLINES)
                .map(airline -> Map.of(
                        "airlineName", airline,
                        "airlineCode", airline.substring(0, 2)
                ))
                .collect(Collectors.toList());
        return Result.success(airlines);
    }

    /**
     * 获取特征列表
     */
    @GetMapping("/features")
    public Result<List<String>> getFeatures() {
        List<String> features = Arrays.asList(
                "出发日期",
                "提前预订天数",
                "航空公司",
                "出发时间",
                "到达时间",
                "航班类型",
                "舱位等级",
                "是否直飞",
                "季节因素",
                "节假日因素"
        );
        return Result.success(features);
    }
}
