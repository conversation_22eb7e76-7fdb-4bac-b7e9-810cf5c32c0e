package com.flightprice.prediction.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 月度统计数据DTO
 */
@Data
public class MonthlyStatsDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 机场ID
     */
    private Long airportId;

    /**
     * 年份
     */
    private Integer year;

    /**
     * 月度数据列表
     */
    private List<MonthDataDTO> monthlyData;
}