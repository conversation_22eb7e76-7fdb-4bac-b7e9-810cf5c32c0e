package com.flightprice.prediction.dto;

import lombok.Data;

import java.util.Map;

/**
 * 模型预测数据传输对象
 */
@Data
public class ModelPredictionDTO {
    
    /**
     * 模型ID
     */
    private Long modelId;
    
    /**
     * 出发城市
     */
    private String departureCity;
    
    /**
     * 到达城市
     */
    private String arrivalCity;
    
    /**
     * 航空公司
     */
    private String airline;
    
    /**
     * 起飞日期（格式：yyyy-MM-dd）
     */
    private String flightDate;
    
    /**
     * 起飞时间（格式：HH:mm）
     */
    private String departureTime;
    
    /**
     * 预测的附加特征
     */
    private Map<String, Object> additionalFeatures;
} 