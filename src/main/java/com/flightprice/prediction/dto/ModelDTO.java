package com.flightprice.prediction.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.time.LocalDateTime;
import java.util.Map;

/**
 * 预测模型DTO
 */
@Data
public class ModelDTO {

    private Long id;

    @NotBlank(message = "模型名称不能为空")
    @Size(max = 100, message = "模型名称长度不能超过100个字符")
    private String name;

    @Size(max = 50, message = "模型类型长度不能超过50个字符")
    private String type;

    @Size(max = 500, message = "模型描述长度不能超过500个字符")
    private String description;

    private Map<String, Object> parameters;

    private String[] features;

    @Size(max = 30, message = "模型版本长度不能超过30个字符")
    private String version;

    @Size(max = 20, message = "模型状态长度不能超过20个字符")
    private String status;

    private Integer trainingSize;

    private Integer testSize;

    private Double accuracy;

    private Double meanError;

    private Boolean isActive;

    @Size(max = 200, message = "文件路径长度不能超过200个字符")
    private String filePath;

    private LocalDateTime createdAt;

    private LocalDateTime updatedAt;

    private LocalDateTime lastTrainedAt;

    private LocalDateTime lastDeployedAt;

    private Long createdBy;

    private String createdByName;
} 