package com.flightprice.prediction.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;

/**
 * 航空公司DTO
 */
@Data
public class AirlineDTO {
    
    private Long id;
    
    @NotBlank(message = "航空公司代码不能为空")
    @Size(max = 10, message = "航空公司代码长度不能超过10个字符")
    private String code;
    
    @NotBlank(message = "航空公司名称不能为空")
    @Size(max = 100, message = "航空公司名称长度不能超过100个字符")
    private String name;
    
    @Size(max = 100, message = "英文名称长度不能超过100个字符")
    private String englishName;
    
    @Size(max = 50, message = "国家/地区长度不能超过50个字符")
    private String country;
    
    private Integer foundedYear;
    
    @Size(max = 100, message = "总部所在地长度不能超过100个字符")
    private String headquarters;
    
    @Size(max = 50, message = "航空联盟长度不能超过50个字符")
    private String alliance;
    
    private Integer fleetSize;
    
    private Integer routesCount;
    
    @Size(max = 500, message = "描述长度不能超过500个字符")
    private String description;
    
    @Size(max = 200, message = "LOGO地址长度不能超过200个字符")
    private String logo;
    
    @Size(max = 200, message = "官网地址长度不能超过200个字符")
    private String website;
    
    private Boolean enabled;
} 