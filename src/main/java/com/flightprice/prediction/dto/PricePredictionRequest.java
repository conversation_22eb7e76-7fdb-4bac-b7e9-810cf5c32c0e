package com.flightprice.prediction.dto;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.flightprice.prediction.config.CustomDateDeserializer;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;

/**
 * 价格预测请求DTO
 */
@Data
public class PricePredictionRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 出发城市
     */
    private String departureCity;
    
    /**
     * 到达城市
     */
    private String arrivalCity;
    
    /**
     * 航空公司
     */
    private String airline;
    
    /**
     * 航班日期
     */
    private String flightDate;
    
    /**
     * 出发时间
     */
    private String departureTime;
    
    /**
     * 模型ID
     */
    private String modelId;

    /**
     * 预测日期
     */
    @NotNull(message = "预测日期不能为空")
    @JsonDeserialize(using = CustomDateDeserializer.class)
    private Date predictDate;

    /**
     * 舱位等级
     */
    private String cabinClass;
} 