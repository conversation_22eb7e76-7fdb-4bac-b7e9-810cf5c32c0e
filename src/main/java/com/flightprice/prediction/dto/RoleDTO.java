package com.flightprice.prediction.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * 角色DTO
 */
@Data
public class RoleDTO {
    
    private Long id;
    
    @NotBlank(message = "角色名称不能为空")
    @Size(max = 50, message = "角色名称长度不能超过50个字符")
    private String name;
    
    @NotBlank(message = "角色编码不能为空")
    @Size(max = 50, message = "角色编码长度不能超过50个字符")
    private String code;
    
    @Size(max = 200, message = "角色描述长度不能超过200个字符")
    private String description;
    
    private Boolean isSystem;
    
    private List<Long> permissionIds;
} 