package com.flightprice.prediction.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;

/**
 * 机场DTO
 */
@Data
public class AirportDTO {
    
    /**
     * 主键ID
     */
    private Long id;
    
    /**
     * 机场三字码
     */
    @NotBlank(message = "机场三字码不能为空")
    @Pattern(regexp = "^[A-Z]{3}$", message = "机场三字码必须为3个大写字母")
    private String iataCode;
    
    /**
     * 机场四字码
     */
    @NotBlank(message = "机场四字码不能为空")
    @Pattern(regexp = "^[A-Z]{4}$", message = "机场四字码必须为4个大写字母")
    private String icaoCode;
    
    /**
     * 机场名称
     */
    @NotBlank(message = "机场名称不能为空")
    @Size(max = 100, message = "机场名称长度不能超过100个字符")
    private String name;
    
    /**
     * 英文名称
     */
    @Size(max = 100, message = "英文名称长度不能超过100个字符")
    private String englishName;
    
    /**
     * 所在城市
     */
    @NotBlank(message = "所在城市不能为空")
    @Size(max = 50, message = "所在城市长度不能超过50个字符")
    private String city;
    
    /**
     * 所属国家/地区
     */
    @Size(max = 50, message = "国家/地区长度不能超过50个字符")
    private String country;
    
    /**
     * 经度
     */
    private Double longitude;
    
    /**
     * 纬度
     */
    private Double latitude;
    
    /**
     * 海拔高度
     */
    private Integer elevation;
    
    /**
     * 时区
     */
    @Size(max = 50, message = "时区长度不能超过50个字符")
    private String timezone;
    
    /**
     * 航站楼数量
     */
    private Integer terminalCount;
    
    /**
     * 跑道数量
     */
    private Integer runwayCount;
    
    /**
     * 年旅客吞吐量
     */
    private Long annualPassengers;
    
    /**
     * 简介
     */
    @Size(max = 500, message = "描述长度不能超过500个字符")
    private String description;
    
    /**
     * 图片地址
     */
    @Size(max = 200, message = "图片地址长度不能超过200个字符")
    private String image;
    
    /**
     * 官网地址
     */
    @Size(max = 200, message = "官网地址长度不能超过200个字符")
    private String website;
    
    /**
     * 是否启用
     */
    private Boolean enabled;
} 