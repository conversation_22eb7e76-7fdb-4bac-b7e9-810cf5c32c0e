package com.flightprice.prediction.dto;

import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;

/**
 * 航站楼数据传输对象
 */
@Data
public class TerminalDTO {
    
    private Long id;
    
    /**
     * 航站楼代码
     */
    @NotBlank(message = "航站楼代码不能为空")
    private String terminalCode;
    
    /**
     * 航站楼名称
     */
    @NotBlank(message = "航站楼名称不能为空")
    private String name;
    
    /**
     * 所属机场ID
     */
    @NotNull(message = "所属机场不能为空")
    private Long airportId;
    
    /**
     * 所属机场名称
     */
    private String airportName;
    
    /**
     * 航站楼类型: DOMESTIC(国内), INTERNATIONAL(国际)
     */
    @NotBlank(message = "航站楼类型不能为空")
    private String type;
    
    /**
     * 登机口数量
     */
    @Min(value = 1, message = "登机口数量最小为1")
    @Max(value = 100, message = "登机口数量最大为100")
    private Integer gateCount;
    
    /**
     * 状态: 0-禁用, 1-启用
     */
    @NotNull(message = "状态不能为空")
    private Integer status;
    
    /**
     * 备注
     */
    private String remark;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
} 