package com.flightprice.prediction.dto;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

@Data
public class AssignUserRolesRequest {
    
    /**
     * 用户ID
     */
    @NotNull(message = "用户ID不能为空")
    private Long userId;
    
    /**
     * 角色ID列表
     */
    private List<Long> roleIds;
    
    /**
     * 权限ID列表
     */
    private List<Long> permissionIds;
} 