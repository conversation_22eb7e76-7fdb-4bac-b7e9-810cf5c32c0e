package com.flightprice.prediction.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import java.io.Serializable;

/**
 * 创建订单请求DTO
 */
@Data
public class CreateOrderRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 机票ID
     */
    private Long ticketId;

    /**
     * 航班ID（当机票ID不存在时使用）
     */
    private Long flightId;

    /**
     * 舱位等级（当机票ID不存在时使用）
     */
    private String cabinClass;

    /**
     * 乘客姓名
     */
    @NotBlank(message = "乘客姓名不能为空")
    private String passengerName;

    /**
     * 乘客身份证号
     */
    @NotBlank(message = "乘客身份证号不能为空")
    @Pattern(regexp = "(^\\d{15}$)|(^\\d{18}$)|(^\\d{17}(\\d|X|x)$)", message = "身份证号格式不正确")
    private String passengerIdCard;

    /**
     * 乘客手机号
     */
    @NotBlank(message = "乘客手机号不能为空")
    @Pattern(regexp = "^1[3-9]\\d{9}$", message = "手机号格式不正确")
    private String passengerPhone;
}