package com.flightprice.prediction.dto;

import com.flightprice.prediction.entity.Permission;
import com.flightprice.prediction.entity.Role;
import lombok.Data;

import java.util.List;

@Data
public class UserPermissionsDTO {
    
    /**
     * 用户ID
     */
    private Long userId;
    
    /**
     * 用户名
     */
    private String username;
    
    /**
     * 用户角色
     */
    private List<Role> roles;
    
    /**
     * 用户权限
     */
    private List<Permission> permissions;
} 