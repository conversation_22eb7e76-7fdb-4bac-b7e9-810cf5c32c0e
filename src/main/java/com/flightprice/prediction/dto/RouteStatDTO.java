package com.flightprice.prediction.dto;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 航线统计DTO
 */
@Data
public class RouteStatDTO {
    
    /**
     * 航线ID
     */
    private Long routeId;
    
    /**
     * 出发城市
     */
    private String departureCity;
    
    /**
     * 出发机场
     */
    private String departureAirport;
    
    /**
     * 到达城市
     */
    private String arrivalCity;
    
    /**
     * 到达机场
     */
    private String arrivalAirport;
    
    /**
     * 航班数量
     */
    private Integer flightCount;
    
    /**
     * 平均票价
     */
    private Double averagePrice;
    
    /**
     * 最低票价
     */
    private BigDecimal lowestPrice;
    
    /**
     * 最高票价
     */
    private BigDecimal highestPrice;
    
    /**
     * 准点率
     */
    private Double onTimeRate;
} 