package com.flightprice.prediction.dto;

import lombok.Data;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 模型训练数据传输对象
 */
@Data
public class ModelTrainDTO {
    
    /**
     * 模型名称
     */
    private String modelName;
    
    /**
     * 算法类型：CNN、RandomForest、LinearRegression、XGBoost等
     */
    private String algorithmType;
    
    /**
     * 适用航线ID，NULL表示通用模型
     */
    private Long routeId;
    
    /**
     * 数据起始日期
     */
    private Date startDate;
    
    /**
     * 数据结束日期
     */
    private Date endDate;
    
    /**
     * 训练数据比例 (0-1之间)
     */
    private Double trainingRatio = 0.8;
    
    /**
     * 模型超参数
     */
    private Map<String, Object> hyperParameters;
    
    /**
     * 特征字段列表
     */
    private List<String> features;
    
    /**
     * 是否自动优化超参数
     */
    private Boolean autoTune = false;
} 