package com.flightprice.prediction.dto;

import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
public class AirlineStatsDTO {
    
    /**
     * 航空公司ID
     */
    private Long id;
    
    /**
     * 航空公司名称
     */
    private String airlineName;
    
    /**
     * 航空公司代码
     */
    private String airlineCode;
    
    /**
     * 航空公司Logo
     */
    private String logoUrl;
    
    /**
     * 航班总数
     */
    private Integer flightCount;
    
    /**
     * 航线总数
     */
    private Integer routeCount;
    
    /**
     * 覆盖机场数
     */
    private Integer airportCount;
    
    /**
     * 平均准点率
     */
    private Double onTimeRate;
    
    /**
     * 月度航班数量
     */
    private Map<String, Integer> monthlyFlightCount;
    
    /**
     * 月度平均票价
     */
    private Map<String, Double> monthlyAvgPrice;
    
    /**
     * 热门航线TOP5
     */
    private List<RouteStatDTO> topRoutes;
    
    /**
     * 热门出发城市TOP5
     */
    private List<CityStatDTO> topDepartureCities;
    
    /**
     * 热门到达城市TOP5
     */
    private List<CityStatDTO> topArrivalCities;
    
} 