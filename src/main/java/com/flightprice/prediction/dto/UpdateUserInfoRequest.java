package com.flightprice.prediction.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Email;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;

/**
 * 更新用户信息请求
 */
@Data
@ApiModel("更新用户信息请求")
public class UpdateUserInfoRequest {

    @ApiModelProperty("昵称")
    @Size(max = 50, message = "昵称长度不能超过50个字符")
    private String nickname;

    @ApiModelProperty("邮箱")
    @Email(message = "邮箱格式不正确")
    private String email;

    @ApiModelProperty("手机号")
    @Pattern(regexp = "^1[3-9]\\d{9}$", message = "手机号格式不正确")
    private String phone;

    @ApiModelProperty("头像URL")
    private String avatar;
}
