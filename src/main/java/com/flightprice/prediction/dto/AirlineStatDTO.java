package com.flightprice.prediction.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 航空公司统计数据DTO
 */
@Data
public class AirlineStatDTO {

    /**
     * 航空公司ID
     */
    private Long airlineId;

    /**
     * 航空公司名称
     */
    private String airlineName;

    /**
     * 航空公司代码
     */
    private String airlineCode;

    /**
     * 航空公司Logo
     */
    private String logoUrl;

    /**
     * 航班总数
     */
    private Integer flightCount;

    /**
     * 航线总数
     */
    private Integer routeCount;

    /**
     * 覆盖机场数
     */
    private Integer airportCount;

    /**
     * 覆盖城市数
     */
    private Integer cityCount;

    /**
     * 平均准点率
     */
    private BigDecimal onTimeRate;

    /**
     * 月度航班数量统计
     */
    private Map<String, Integer> monthlyFlightCount;

    /**
     * 月度平均票价统计
     */
    private Map<String, BigDecimal> monthlyAvgPrice;

    /**
     * 热门航线TOP5
     */
    private List<RouteStatDTO> topRoutes;

    /**
     * 热门出发城市TOP5
     */
    private List<CityStatDTO> topDepartureCities;

    /**
     * 热门到达城市TOP5
     */
    private List<CityStatDTO> topArrivalCities;

    /**
     * 百分比
     */
    private Double percentage;

    /**
     * 年份
     */
    private Integer year;

    /**
     * 月度统计数据
     */
    private List<MonthlyStatDTO> monthlyStats;
}