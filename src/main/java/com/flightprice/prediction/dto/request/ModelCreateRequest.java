package com.flightprice.prediction.dto.request;

import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * 模型创建请求
 */
@Data
public class ModelCreateRequest {
    
    /**
     * 模型名称
     */
    private String modelName;
    
    /**
     * 模型描述
     */
    private String description;
    
    /**
     * 起始城市
     */
    private String departureCity;
    
    /**
     * 目的城市
     */
    private String arrivalCity;
    
    /**
     * 航空公司列表
     */
    private List<String> airlines;
    
    /**
     * 算法类型
     */
    private String algorithmType;
    
    /**
     * 算法参数
     */
    private Map<String, Object> parameters;
    
    /**
     * 使用的特征
     */
    private List<String> features;
} 