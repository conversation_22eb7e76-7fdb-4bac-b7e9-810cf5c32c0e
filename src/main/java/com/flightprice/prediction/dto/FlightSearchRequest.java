package com.flightprice.prediction.dto;

import com.flightprice.prediction.common.PageRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;

/**
 * 航班搜索请求DTO
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class FlightSearchRequest extends PageRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 出发城市ID
     */
    private Long departureCityId;

    /**
     * 出发城市名称
     */
    private String departureCity;

    /**
     * 到达城市ID
     */
    private Long arrivalCityId;

    /**
     * 到达城市名称
     */
    private String arrivalCity;

    /**
     * 出发日期
     */
    @NotNull(message = "出发日期不能为空")
    private Date departureDate;

    /**
     * 舱位等级
     */
    private String cabinClass;

    /**
     * 航空公司ID
     */
    private Long airlineId;
} 