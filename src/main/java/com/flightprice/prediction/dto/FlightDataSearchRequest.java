package com.flightprice.prediction.dto;

import com.flightprice.prediction.common.PageRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;

/**
 * 航班数据搜索请求DTO
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class FlightDataSearchRequest extends PageRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 出发城市
     */
    @NotNull(message = "出发城市不能为空")
    private String departureCity;

    /**
     * 到达城市
     */
    @NotNull(message = "到达城市不能为空")
    private String arrivalCity;

    /**
     * 出发日期
     */
    @NotNull(message = "出发日期不能为空")
    private Date departureDate;
    
    /**
     * 航空公司
     */
    private String airline;
} 