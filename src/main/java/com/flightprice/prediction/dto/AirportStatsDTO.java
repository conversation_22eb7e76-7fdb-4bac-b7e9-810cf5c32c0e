package com.flightprice.prediction.dto;

import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
public class AirportStatsDTO {

    /**
     * 机场ID
     */
    private Long id;

    /**
     * 机场ID - 兼容新的实现
     */
    private Long airportId;

    /**
     * 机场名称
     */
    private String airportName;

    /**
     * 机场代码
     */
    private String airportCode;

    /**
     * 航班总数
     */
    private Integer flightCount;

    /**
     * 航班总数 - 兼容新的实现
     */
    private Integer totalFlights;

    /**
     * 航线总数
     */
    private Integer routeCount;

    /**
     * 航空公司数量
     */
    private Integer airlineCount;

    /**
     * 平均准点率
     */
    private Double onTimeRate;

    /**
     * 月度航班数量
     */
    private Map<String, Integer> monthlyFlightCount;

    /**
     * 月度旅客流量
     */
    private Map<String, Integer> monthlyPassengerFlow;

    /**
     * 旅客总数 - 兼容新的实现
     */
    private Integer totalPassengers;

    /**
     * 平均延误时间 - 兼容新的实现
     */
    private Integer avgDelay;

    /**
     * 热门航线 - 兼容新的实现
     */
    private Map<String, Integer> popularRoutes;

    /**
     * 热门出发航线TOP5
     */
    private List<RouteStatDTO> topDepartureRoutes;

    /**
     * 热门到达航线TOP5
     */
    private List<RouteStatDTO> topArrivalRoutes;

    /**
     * 航空公司航班分布
     */
    private List<AirlineStatDTO> airlineDistribution;
}