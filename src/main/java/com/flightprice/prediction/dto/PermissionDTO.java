package com.flightprice.prediction.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * 权限DTO
 */
@Data
public class PermissionDTO {
    
    private Long id;
    
    @NotBlank(message = "权限名称不能为空")
    @Size(max = 50, message = "权限名称长度不能超过50个字符")
    private String name;
    
    @NotBlank(message = "权限编码不能为空")
    @Size(max = 50, message = "权限编码长度不能超过50个字符")
    private String code;
    
    @Size(max = 200, message = "权限描述长度不能超过200个字符")
    private String description;
    
    private String type;
    
    private Long parentId;
    
    private List<PermissionDTO> children;
} 