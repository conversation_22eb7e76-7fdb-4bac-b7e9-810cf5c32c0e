package com.flightprice.prediction.dto;

import lombok.Data;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * 模型训练历史DTO
 */
@Data
public class ModelTrainingHistoryDTO {

    private Long id;
    
    private Long modelId;
    
    private String modelName;
    
    private Map<String, Object> parameters;
    
    private Integer trainingSize;
    
    private Integer testSize;
    
    private Double accuracy;
    
    private Double meanError;
    
    private Double rSquared;
    
    private Double mae;
    
    private Double rmse;
    
    private String status;
    
    private String errorMessage;
    
    private LocalDateTime startTime;
    
    private LocalDateTime endTime;
    
    private Long duration;
    
    private String logFile;
    
    private Map<String, Object> evaluationDetails;
    
    private Long trainedBy;
    
    private String trainedByName;
    
    private LocalDateTime createdAt;
} 