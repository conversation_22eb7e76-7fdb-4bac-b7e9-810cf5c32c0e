package com.flightprice.prediction.util;

import com.flightprice.prediction.entity.FlightData;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.util.StringUtils;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * Excel导入工具类
 */
@Slf4j
public class ExcelImportUtil {

    /**
     * 从Excel读取航班数据
     *
     * @param filePath Excel文件路径
     * @return 航班数据列表
     */
    public static List<FlightData> readFlightDataFromExcel(String filePath) {
        List<FlightData> dataList = new ArrayList<>();

        try (FileInputStream fis = new FileInputStream(new File(filePath));
             Workbook workbook = new XSSFWorkbook(fis)) {

            // 默认读取第一个工作表
            Sheet sheet = workbook.getSheetAt(0);

            // 第一行是表头，从第二行开始读取
            int firstRow = 1;
            int lastRow = sheet.getLastRowNum();

            for (int i = firstRow; i <= lastRow; i++) {
                Row row = sheet.getRow(i);
                if (row == null) {
                    continue;
                }

                FlightData flightData = new FlightData();

                // 设置字段值（按照表头顺序）
                int cellIndex = 0;

                // 出发城市
                flightData.setDepartureCity(getCellValue(row.getCell(cellIndex++)));
                // 出发城市y
                flightData.setDepartureCityY(getDoubleValue(row.getCell(cellIndex++)));
                // 出发城市x
                flightData.setDepartureCityX(getDoubleValue(row.getCell(cellIndex++)));
                // 到达城市
                flightData.setArrivalCity(getCellValue(row.getCell(cellIndex++)));
                // 到达城市y
                flightData.setArrivalCityY(getDoubleValue(row.getCell(cellIndex++)));
                // 到达城市x
                flightData.setArrivalCityX(getDoubleValue(row.getCell(cellIndex++)));
                // 里程（公里）
                flightData.setMileage(getIntValue(row.getCell(cellIndex++)));
                // 航班班次
                flightData.setFlightNumber(getCellValue(row.getCell(cellIndex++)));
                // 航空公司
                flightData.setAirline(getCellValue(row.getCell(cellIndex++)));
                // 机型
                flightData.setAircraftType(getCellValue(row.getCell(cellIndex++)));
                // 起飞机场
                flightData.setDepartureAirport(getCellValue(row.getCell(cellIndex++)));
                // 起飞机场y
                flightData.setDepartureAirportY(getDoubleValue(row.getCell(cellIndex++)));
                // 起飞机场x
                flightData.setDepartureAirportX(getDoubleValue(row.getCell(cellIndex++)));
                // 降落机场
                flightData.setArrivalAirport(getCellValue(row.getCell(cellIndex++)));
                // 降落机场y
                flightData.setArrivalAirportY(getDoubleValue(row.getCell(cellIndex++)));
                // 降落机场x
                flightData.setArrivalAirportX(getDoubleValue(row.getCell(cellIndex++)));
                // 准点率
                flightData.setPunctualityRate(getDoubleValue(row.getCell(cellIndex++)));
                // 平均误点时间
                flightData.setAverageDelayTime(getCellValue(row.getCell(cellIndex++)));
                // 周一班期
                flightData.setMondaySchedule(getCellValue(row.getCell(cellIndex++)));
                // 周二班期
                flightData.setTuesdaySchedule(getCellValue(row.getCell(cellIndex++)));
                // 周三班期
                flightData.setWednesdaySchedule(getCellValue(row.getCell(cellIndex++)));
                // 周四班期
                flightData.setThursdaySchedule(getCellValue(row.getCell(cellIndex++)));
                // 周五班期
                flightData.setFridaySchedule(getCellValue(row.getCell(cellIndex++)));
                // 周六班期
                flightData.setSaturdaySchedule(getCellValue(row.getCell(cellIndex++)));
                // 周日班期
                flightData.setSundaySchedule(getCellValue(row.getCell(cellIndex++)));
                // 城市_x
                flightData.setCityX(getCellValue(row.getCell(cellIndex++)));
                // 出发省份
                flightData.setDepartureProvince(getCellValue(row.getCell(cellIndex++)));
                // 城市_y
                flightData.setCityY(getCellValue(row.getCell(cellIndex++)));
                // 到达省份
                flightData.setArrivalProvince(getCellValue(row.getCell(cellIndex++)));
                // 起飞时间
                flightData.setDepartureTime(getCellValue(row.getCell(cellIndex++)));
                // 降落时间
                flightData.setArrivalTime(getCellValue(row.getCell(cellIndex++)));
                // 日期
                flightData.setFlightDate(getCellValue(row.getCell(cellIndex++)));
                // 价格(元)
                flightData.setPrice(getBigDecimalValue(row.getCell(cellIndex++)));
                // 人数
                flightData.setPassengerCount(getIntValue(row.getCell(cellIndex)));

                dataList.add(flightData);
                log.info("读取到航班数据：{} → {}, 航班号：{}", flightData.getDepartureCity(), flightData.getArrivalCity(), flightData.getFlightNumber());
            }

        } catch (IOException e) {
            log.error("读取Excel文件失败：{}", e.getMessage(), e);
        }

        return dataList;
    }

    /**
     * 获取单元格的字符串值
     */
    private static String getCellValue(Cell cell) {
        if (cell == null) {
            return null;
        }

        String value = null;
        switch (cell.getCellType()) {
            case STRING:
                value = cell.getStringCellValue();
                break;
            case NUMERIC:
                if (DateUtil.isCellDateFormatted(cell)) {
                    value = cell.getDateCellValue().toString();
                } else {
                    value = String.valueOf(cell.getNumericCellValue());
                }
                break;
            case BOOLEAN:
                value = String.valueOf(cell.getBooleanCellValue());
                break;
            case FORMULA:
                try {
                    value = String.valueOf(cell.getStringCellValue());
                } catch (Exception e) {
                    value = String.valueOf(cell.getNumericCellValue());
                }
                break;
            default:
                break;
        }
        return value;
    }

    /**
     * 获取单元格的Double值
     */
    private static Double getDoubleValue(Cell cell) {
        if (cell == null) {
            return null;
        }

        Double value = null;
        switch (cell.getCellType()) {
            case NUMERIC:
                value = cell.getNumericCellValue();
                break;
            case STRING:
                String strValue = cell.getStringCellValue();
                if (StringUtils.hasText(strValue)) {
                    try {
                        value = Double.parseDouble(strValue);
                    } catch (NumberFormatException e) {
                        log.warn("无法将字符串转换为Double：{}", strValue);
                    }
                }
                break;
            default:
                break;
        }
        return value;
    }

    /**
     * 获取单元格的整数值
     */
    private static Integer getIntValue(Cell cell) {
        Double doubleValue = getDoubleValue(cell);
        return doubleValue != null ? doubleValue.intValue() : null;
    }

    /**
     * 获取单元格的BigDecimal值
     */
    private static BigDecimal getBigDecimalValue(Cell cell) {
        Double doubleValue = getDoubleValue(cell);
        return doubleValue != null ? BigDecimal.valueOf(doubleValue) : null;
    }
} 