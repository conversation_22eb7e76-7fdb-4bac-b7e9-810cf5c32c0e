package com.flightprice.prediction.util;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.flightprice.prediction.entity.FlightData;
import com.flightprice.prediction.service.FlightDataService;
import lombok.extern.slf4j.Slf4j;

import java.io.InputStream;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * EasyExcel导入工具类
 */
@Slf4j
public class EasyExcelImportUtil {

    /**
     * 从Excel读取航班数据，并批量保存
     *
     * @param inputStream Excel文件输入流
     * @param flightDataService 航班数据服务
     * @return 导入的数据条数
     */
    public static int readAndSaveFlightDataFromExcel(InputStream inputStream, FlightDataService flightDataService) {
        final int[] totalCount = {0};

        try {
            EasyExcel.read(inputStream, new AnalysisEventListener<Map<Integer, String>>() {
                // 表头映射，用于确定每列对应的字段
                private Map<Integer, String> headMap;

                // 跳过表头行
                private boolean isHeader = true;

                // 批量处理的数据列表
                private List<FlightData> batchList = new ArrayList<>();

                // 批量大小
                private final int BATCH_SIZE = 1000;

                @Override
                public void invokeHeadMap(Map<Integer, String> headMap, AnalysisContext context) {
                    this.headMap = headMap;
                    log.info("解析到表头: {}", headMap);
                }

                @Override
                public void invoke(Map<Integer, String> data, AnalysisContext context) {
                    // 跳过表头行
                    if (isHeader) {
                        isHeader = false;
                        return;
                    }

                    try {
                        FlightData flightData = new FlightData();

                        // 设置字段值
                        flightData.setDepartureCity(data.get(0));
                        flightData.setDepartureCityY(parseDouble(data.get(1)));
                        flightData.setDepartureCityX(parseDouble(data.get(2)));
                        flightData.setArrivalCity(data.get(3));
                        flightData.setArrivalCityY(parseDouble(data.get(4)));
                        flightData.setArrivalCityX(parseDouble(data.get(5)));
                        flightData.setMileage(parseInt(data.get(6)));
                        flightData.setFlightNumber(data.get(7));
                        flightData.setAirline(data.get(8));
                        flightData.setAircraftType(data.get(9));
                        flightData.setDepartureAirport(data.get(10));
                        flightData.setDepartureAirportY(parseDouble(data.get(11)));
                        flightData.setDepartureAirportX(parseDouble(data.get(12)));
                        flightData.setArrivalAirport(data.get(13));
                        flightData.setArrivalAirportY(parseDouble(data.get(14)));
                        flightData.setArrivalAirportX(parseDouble(data.get(15)));
                        flightData.setPunctualityRate(parseDouble(data.get(16)));
                        flightData.setAverageDelayTime(data.get(17));
                        flightData.setMondaySchedule(data.get(18));
                        flightData.setTuesdaySchedule(data.get(19));
                        flightData.setWednesdaySchedule(data.get(20));
                        flightData.setThursdaySchedule(data.get(21));
                        flightData.setFridaySchedule(data.get(22));
                        flightData.setSaturdaySchedule(data.get(23));
                        flightData.setSundaySchedule(data.get(24));
                        flightData.setCityX(data.get(25));
                        flightData.setDepartureProvince(data.get(26));
                        flightData.setCityY(data.get(27));
                        flightData.setArrivalProvince(data.get(28));
                        flightData.setDepartureTime(data.get(29));
                        flightData.setArrivalTime(data.get(30));
                        flightData.setFlightDate(data.get(31));
                        flightData.setPrice(parseBigDecimal(data.get(32)));
                        if (data.size() > 33) {
                            flightData.setPassengerCount(parseInt(data.get(33)));
                        }

                        batchList.add(flightData);

                        // 当批量列表达到指定大小时，执行批量保存
                        if (batchList.size() >= BATCH_SIZE) {
                            int savedCount = flightDataService.batchSave(batchList);
                            totalCount[0] += savedCount;
                            log.info("批量保存航班数据成功，当前进度：{}条", totalCount[0]);
                            batchList = new ArrayList<>();
                        }
                    } catch (Exception e) {
                        log.error("解析数据行失败: {}", data, e);
                    }
                }

                @Override
                public void doAfterAllAnalysed(AnalysisContext context) {
                    // 处理剩余的数据
                    if (!batchList.isEmpty()) {
                        int savedCount = flightDataService.batchSave(batchList);
                        totalCount[0] += savedCount;
                        log.info("批量保存剩余航班数据成功，共保存 {} 条", totalCount[0]);
                    }
                    log.info("所有数据解析完成，共导入 {} 条", totalCount[0]);
                }
            }).sheet().doRead();
        } catch (Exception e) {
            log.error("读取Excel文件失败：{}", e.getMessage(), e);
        }

        return totalCount[0];
    }

    /**
     * 从Excel读取航班数据
     *
     * @param inputStream Excel文件输入流
     * @return 航班数据列表
     */
    public static List<FlightData> readFlightDataFromExcel(InputStream inputStream) {
        List<FlightData> dataList = new ArrayList<>();

        try {
            EasyExcel.read(inputStream, new AnalysisEventListener<Map<Integer, String>>() {
                // 表头映射，用于确定每列对应的字段
                private Map<Integer, String> headMap;

                // 跳过表头行
                private boolean isHeader = true;

                @Override
                public void invokeHeadMap(Map<Integer, String> headMap, AnalysisContext context) {
                    this.headMap = headMap;
                    log.info("解析到表头: {}", headMap);
                }

                @Override
                public void invoke(Map<Integer, String> data, AnalysisContext context) {
                    // 跳过表头行
                    if (isHeader) {
                        isHeader = false;
                        return;
                    }

                    try {
                        FlightData flightData = new FlightData();

                        // 设置字段值
                        flightData.setDepartureCity(data.get(0));
                        flightData.setDepartureCityY(parseDouble(data.get(1)));
                        flightData.setDepartureCityX(parseDouble(data.get(2)));
                        flightData.setArrivalCity(data.get(3));
                        flightData.setArrivalCityY(parseDouble(data.get(4)));
                        flightData.setArrivalCityX(parseDouble(data.get(5)));
                        flightData.setMileage(parseInt(data.get(6)));
                        flightData.setFlightNumber(data.get(7));
                        flightData.setAirline(data.get(8));
                        flightData.setAircraftType(data.get(9));
                        flightData.setDepartureAirport(data.get(10));
                        flightData.setDepartureAirportY(parseDouble(data.get(11)));
                        flightData.setDepartureAirportX(parseDouble(data.get(12)));
                        flightData.setArrivalAirport(data.get(13));
                        flightData.setArrivalAirportY(parseDouble(data.get(14)));
                        flightData.setArrivalAirportX(parseDouble(data.get(15)));
                        flightData.setPunctualityRate(parseDouble(data.get(16)));
                        flightData.setAverageDelayTime(data.get(17));
                        flightData.setMondaySchedule(data.get(18));
                        flightData.setTuesdaySchedule(data.get(19));
                        flightData.setWednesdaySchedule(data.get(20));
                        flightData.setThursdaySchedule(data.get(21));
                        flightData.setFridaySchedule(data.get(22));
                        flightData.setSaturdaySchedule(data.get(23));
                        flightData.setSundaySchedule(data.get(24));
                        flightData.setCityX(data.get(25));
                        flightData.setDepartureProvince(data.get(26));
                        flightData.setCityY(data.get(27));
                        flightData.setArrivalProvince(data.get(28));
                        flightData.setDepartureTime(data.get(29));
                        flightData.setArrivalTime(data.get(30));
                        flightData.setFlightDate(data.get(31));
                        flightData.setPrice(parseBigDecimal(data.get(32)));
                        if (data.size() > 33) {
                            flightData.setPassengerCount(parseInt(data.get(33)));
                        }

                        dataList.add(flightData);
                        log.info("读取到航班数据：{} → {}, 航班号：{}", flightData.getDepartureCity(), flightData.getArrivalCity(), flightData.getFlightNumber());
                    } catch (Exception e) {
                        log.error("解析数据行失败: {}", data, e);
                    }
                }

                @Override
                public void doAfterAllAnalysed(AnalysisContext context) {
                    log.info("所有数据解析完成，共 {} 条", dataList.size());
                }
            }).sheet().doRead();
        } catch (Exception e) {
            log.error("读取Excel文件失败：{}", e.getMessage(), e);
        }

        return dataList;
    }

    /**
     * 解析Double值
     */
    private static Double parseDouble(String value) {
        if (value == null || value.trim().isEmpty()) {
            return 0.0;
        }
        try {
            return Double.parseDouble(value);
        } catch (NumberFormatException e) {
            return 0.0;
        }
    }

    /**
     * 解析Integer值
     */
    private static Integer parseInt(String value) {
        if (value == null || value.trim().isEmpty()) {
            return 0;
        }
        try {
            return Integer.parseInt(value);
        } catch (NumberFormatException e) {
            return 0;
        }
    }

    /**
     * 解析BigDecimal值
     */
    private static BigDecimal parseBigDecimal(String value) {
        if (value == null || value.trim().isEmpty()) {
            return BigDecimal.ZERO;
        }
        try {
            return new BigDecimal(value);
        } catch (NumberFormatException e) {
            return BigDecimal.ZERO;
        }
    }
}
