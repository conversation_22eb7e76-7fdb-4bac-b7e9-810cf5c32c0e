package com.flightprice.prediction.util;

import com.flightprice.prediction.entity.Role;
import com.flightprice.prediction.entity.User;
import com.flightprice.prediction.repository.RoleRepository;
import com.flightprice.prediction.repository.UserRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashSet;
import java.util.Optional;
import java.util.Set;

/**
 * 管理员账号初始化工具类
 * 在应用启动时自动创建管理员账号（如果不存在）
 */
@Slf4j
@Component
public class AdminUserInitializer implements CommandLineRunner {

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private RoleRepository roleRepository;

    @Autowired
    private PasswordEncoder passwordEncoder;

    @Override
    @Transactional
    public void run(String... args) {
        run();
    }

    /**
     * 初始化管理员用户和角色
     */
    @Transactional
    public void run() {
        initAdminRole();
        initAdminUser();
    }

    /**
     * 初始化管理员角色
     */
    @Transactional
    public Role initAdminRole() {
        Optional<Role> existingRole = roleRepository.findByName("ADMIN");
        if (!existingRole.isPresent()) {
            Role adminRole = new Role();
            adminRole.setName("ADMIN");
            adminRole.setCode("ROLE_ADMIN");
            adminRole.setDisplayName("系统管理员");
            adminRole.setDescription("拥有系统所有权限");
            adminRole = roleRepository.save(adminRole);
            log.info("成功创建管理员角色");
            return adminRole;
        } else {
            log.info("管理员角色已存在，无需创建");
            return existingRole.get();
        }
    }

    /**
     * 初始化管理员用户
     */
    @Transactional
    public User initAdminUser() {
        String adminUsername = "admin";
        Optional<User> existingUser = userRepository.findByUsername(adminUsername);

        if (!existingUser.isPresent()) {
            // 获取管理员角色
            Role adminRole = initAdminRole();
            
            // 创建管理员用户
            User adminUser = new User();
            adminUser.setUsername(adminUsername);
            adminUser.setPassword(passwordEncoder.encode("admin"));
            adminUser.setFullName("系统管理员");
            adminUser.setEmail("<EMAIL>");
            adminUser.setEnabled(true);
            
            // 设置用户角色
            Set<Role> roles = new HashSet<>();
            roles.add(adminRole);
            adminUser.setRoles(roles);
            
            // 保存用户
            adminUser = userRepository.save(adminUser);
            log.info("成功创建管理员用户，用户名: admin，密码: admin");
            return adminUser;
        } else {
            log.info("管理员用户已存在，无需创建");
            return existingUser.get();
        }
    }
} 