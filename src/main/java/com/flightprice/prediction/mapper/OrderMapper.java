package com.flightprice.prediction.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.flightprice.prediction.entity.Order;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.sql.Date;
import java.util.List;
import java.util.Map;

/**
 * 订单Mapper接口
 */
@Mapper
public interface OrderMapper extends BaseMapper<Order> {

    /**
     * 查询各航线订单量统计
     * @return 航线订单统计数据
     */
    @Select("SELECT " +
            "CONCAT(dc.name, '-', ac.name) as routeName, " +
            "COUNT(o.id) as orderCount " +
            "FROM orders o " +
            "JOIN ticket t ON o.ticket_id = t.id " +
            "JOIN flight f ON t.flight_id = f.id " +
            "JOIN route r ON f.route_id = r.id " +
            "JOIN city dc ON r.departure_city_id = dc.id " +
            "JOIN city ac ON r.arrival_city_id = ac.id " +
            "WHERE o.status != 'CANCELED' " +
            "GROUP BY dc.name, ac.name " +
            "ORDER BY orderCount DESC " +
            "LIMIT 10")
    List<Map<String, Object>> selectRouteOrderStats();

    /**
     * 查询各航空公司订单占比统计
     * @return 航空公司订单统计数据
     */
    @Select("SELECT a.name as airlineName, COUNT(o.id) as orderCount " +
            "FROM orders o " +
            "JOIN ticket t ON o.ticket_id = t.id " +
            "JOIN flight f ON t.flight_id = f.id " +
            "JOIN airline a ON f.airline_id = a.id " +
            "GROUP BY a.name " +
            "ORDER BY orderCount DESC")
    List<Map<String, Object>> selectAirlineOrderStats();

    /**
     * 获取指定日期范围内的每日订单数量统计
     * 
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 每日订单数量统计列表，每个元素包含 date 和 orderCount
     * 
     * SQL说明：
     * 1. DATE_FORMAT(create_time, '%Y-%m-%d') 将时间格式化为 YYYY-MM-DD
     * 2. COUNT(*) 统计每天的订单数量
     * 3. GROUP BY 按日期分组
     * 4. ORDER BY 按日期升序排序
     */
    @Select("SELECT DATE_FORMAT(create_time, '%Y-%m-%d') as date, " +
            "COUNT(*) as orderCount " +
            "FROM orders " +
            "WHERE create_time >= #{startDate} " +
            "AND create_time <= #{endDate} " +
            "GROUP BY DATE_FORMAT(create_time, '%Y-%m-%d') " +
            "ORDER BY date")
    List<Map<String, Object>> selectOrderTrendStats(@Param("startDate") Date startDate, 
                                                   @Param("endDate") Date endDate);

    /**
     * 查询指定日期范围内的每日收入
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 每日收入统计数据
     */
    @Select("SELECT DATE_FORMAT(o.create_time, '%Y-%m-%d') as date, SUM(o.total_amount) as revenue " +
            "FROM orders o " +
            "WHERE o.status IN ('PAID', 'COMPLETED') " +
            "AND o.create_time BETWEEN #{startDate} AND #{endDate} " +
            "GROUP BY DATE_FORMAT(o.create_time, '%Y-%m-%d') " +
            "ORDER BY date")
    List<Map<String, Object>> selectRevenueTrendStats(@Param("startDate") Date startDate, @Param("endDate") Date endDate);
}