package com.flightprice.prediction.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.flightprice.prediction.entity.Route;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 航线Mapper接口
 */
@Mapper
public interface RouteMapper extends BaseMapper<Route> {
    
    /**
     * 查询热门航线代码
     * @return 热门航线代码列表
     */
    @Select("SELECT CONCAT(dc.name, '-', ac.name) as route_code " +
            "FROM route r " +
            "JOIN flight f ON r.id = f.route_id " +
            "JOIN ticket t ON f.id = t.flight_id " +
            "JOIN orders o ON t.id = o.ticket_id " +
            "JOIN city dc ON r.departure_city_id = dc.id " +
            "JOIN city ac ON r.arrival_city_id = ac.id " +
            "GROUP BY route_code " +
            "ORDER BY COUNT(o.id) DESC " +
            "LIMIT 5")
    List<String> selectPopularRoutes();
    
    /**
     * 查询所有航线代码
     * @return 航线代码列表
     */
    @Select("SELECT route_code FROM route")
    List<String> selectAllRouteCodes();
} 