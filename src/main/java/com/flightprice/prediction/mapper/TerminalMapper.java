package com.flightprice.prediction.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.flightprice.prediction.entity.Terminal;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

/**
 * 航站楼数据访问接口
 */
@Repository
public interface TerminalMapper extends BaseMapper<Terminal> {
    
    /**
     * 分页查询航站楼信息
     *
     * @param page 分页参数
     * @param name 航站楼名称
     * @param airportId 所属机场ID
     * @param status 状态
     * @return 分页结果
     */
    IPage<Terminal> selectTerminalPage(
            Page<Terminal> page,
            @Param("name") String name,
            @Param("airportId") Long airportId,
            @Param("status") Integer status);
} 