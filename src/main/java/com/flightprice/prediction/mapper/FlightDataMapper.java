package com.flightprice.prediction.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.flightprice.prediction.entity.FlightData;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 航班数据Mapper
 */
@Mapper
public interface FlightDataMapper extends BaseMapper<FlightData> {
    
    /**
     * 获取训练数据
     * 
     * @param routeId 航线ID，可以为null表示所有航线
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param features 特征列表
     * @return 训练数据Map
     */
    Map<String, Object> getTrainingData(
            @Param("routeId") Long routeId,
            @Param("startDate") Date startDate,
            @Param("endDate") Date endDate,
            @Param("features") List<String> features);
    
    /**
     * 获取历史价格数据
     * 
     * @param departureCity 出发城市
     * @param arrivalCity 到达城市
     * @param airline 航空公司
     * @return 历史价格数据
     */
    List<Map<String, Object>> getHistoryPrices(
            @Param("departureCity") String departureCity,
            @Param("arrivalCity") String arrivalCity,
            @Param("airline") String airline);
    
    /**
     * 获取所有出发城市
     */
    @Select("SELECT DISTINCT departure_city FROM flight_data WHERE is_deleted = 0 ORDER BY departure_city")
    List<String> getAllDepartureCities();
    
    /**
     * 获取所有到达城市
     */
    @Select("SELECT DISTINCT arrival_city FROM flight_data WHERE is_deleted = 0 ORDER BY arrival_city")
    List<String> getAllArrivalCities();
    
    /**
     * 根据出发城市获取可到达的城市
     */
    @Select("SELECT DISTINCT arrival_city FROM flight_data WHERE departure_city = #{departureCity} AND is_deleted = 0 ORDER BY arrival_city")
    List<String> getArrivalCitiesByDepartureCity(String departureCity);
} 