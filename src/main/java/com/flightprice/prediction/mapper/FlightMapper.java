package com.flightprice.prediction.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.flightprice.prediction.entity.Flight;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;
import java.util.Map;

/**
 * 航班Mapper接口
 */
@Mapper
public interface FlightMapper extends BaseMapper<Flight> {
    
    /**
     * 查询指定航线的价格趋势
     * @param routeCode 航线代码
     * @return 价格趋势数据
     */
    @Select("SELECT DATE_FORMAT(f.departure_time, '%Y-%m-%d') as date, " +
            "AVG(t.price) as avgPrice " +
            "FROM flight f " +
            "JOIN route r ON f.route_id = r.id " +
            "JOIN ticket t ON f.id = t.flight_id " +
            "JOIN city dc ON r.departure_city_id = dc.id " +
            "JOIN city ac ON r.arrival_city_id = ac.id " +
            "WHERE CONCAT(dc.name, '-', ac.name) = #{routeCode} " +
            "AND t.cabin_class = 'ECONOMY' " +
            "AND f.departure_time BETWEEN DATE_SUB(NOW(), INTERVAL 30 DAY) AND DATE_ADD(NOW(), INTERVAL 30 DAY) " +
            "GROUP BY date " +
            "ORDER BY date")
    List<Map<String, Object>> selectRoutePriceTrend(@Param("routeCode") String routeCode);
    
    /**
     * 查询所有航线的当日平均价格
     * @return 航线平均价格数据
     */
    @Select("SELECT CONCAT(dc.name, '-', ac.name) as routeCode, " +
            "AVG(CASE WHEN t.cabin_class = 'ECONOMY' THEN t.price ELSE NULL END) as avgEconomyPrice, " +
            "COUNT(DISTINCT f.id) as flightCount " +
            "FROM flight f " +
            "JOIN route r ON f.route_id = r.id " +
            "JOIN ticket t ON f.id = t.flight_id " +
            "JOIN city dc ON r.departure_city_id = dc.id " +
            "JOIN city ac ON r.arrival_city_id = ac.id " +
            "WHERE DATE(f.departure_time) = CURDATE() " +
            "GROUP BY routeCode " +
            "ORDER BY flightCount DESC, avgEconomyPrice ASC")
    List<Map<String, Object>> selectRouteAvgPrices();
} 