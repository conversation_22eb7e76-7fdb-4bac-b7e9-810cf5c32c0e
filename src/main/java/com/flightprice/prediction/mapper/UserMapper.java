package com.flightprice.prediction.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.flightprice.prediction.entity.User;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 用户Mapper接口
 */
@Mapper
public interface UserMapper extends BaseMapper<User> {
    
    /**
     * 根据用户名查询用户
     * @param username 用户名
     * @return 用户对象
     */
    User selectOneByUsername(@Param("username") String username);
} 