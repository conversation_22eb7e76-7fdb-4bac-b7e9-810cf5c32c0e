package com.flight.controller;

import com.flight.entity.PriceTrend;
import com.flight.service.PriceTrendService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/api/price-trend")
public class PriceTrendController {

    @Autowired
    private PriceTrendService priceTrendService;

    @GetMapping
    public ResponseEntity<Map<String, Object>> getPriceTrend(
            @RequestParam(required = false) String routeCode,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate) {
        
        Map<String, Object> analysis = priceTrendService.getPriceAnalysis(routeCode, startDate, endDate);
        return ResponseEntity.ok(analysis);
    }

    @PostMapping
    public ResponseEntity<Void> createPriceTrend(@RequestBody PriceTrend priceTrend) {
        priceTrendService.savePriceTrend(priceTrend);
        return ResponseEntity.ok().build();
    }

    @PutMapping("/{id}")
    public ResponseEntity<Void> updatePriceTrend(
            @PathVariable Long id,
            @RequestBody PriceTrend priceTrend) {
        priceTrend.setId(id);
        priceTrendService.updatePriceTrend(priceTrend);
        return ResponseEntity.ok().build();
    }

    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deletePriceTrend(@PathVariable Long id) {
        priceTrendService.deletePriceTrend(id);
        return ResponseEntity.ok().build();
    }
} 