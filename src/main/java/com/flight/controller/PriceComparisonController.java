package com.flight.controller;

import com.flight.entity.PriceComparison;
import com.flight.service.PriceComparisonService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/api/price-comparison")
public class PriceComparisonController {

    @Autowired
    private PriceComparisonService priceComparisonService;

    @GetMapping
    public ResponseEntity<Map<String, Object>> comparePrices(
            @RequestParam String routeCode,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate,
            @RequestParam(required = false) String comparisonType) {
        
        Map<String, Object> comparison = priceComparisonService.comparePrices(routeCode, startDate, endDate, comparisonType);
        return ResponseEntity.ok(comparison);
    }

    @GetMapping("/history")
    public ResponseEntity<List<PriceComparison>> getComparisonHistory(
            @RequestParam(required = false) String routeCode,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate) {
        
        List<PriceComparison> history = priceComparisonService.getComparisonHistory(routeCode, startDate, endDate);
        return ResponseEntity.ok(history);
    }

    @PostMapping
    public ResponseEntity<Void> createComparison(@RequestBody PriceComparison comparison) {
        priceComparisonService.saveComparison(comparison);
        return ResponseEntity.ok().build();
    }

    @PutMapping("/{id}")
    public ResponseEntity<Void> updateComparison(
            @PathVariable Long id,
            @RequestBody PriceComparison comparison) {
        comparison.setId(id);
        priceComparisonService.updateComparison(comparison);
        return ResponseEntity.ok().build();
    }

    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deleteComparison(@PathVariable Long id) {
        priceComparisonService.deleteComparison(id);
        return ResponseEntity.ok().build();
    }

    @GetMapping("/analysis")
    public ResponseEntity<Map<String, Object>> getPriceAnalysis(
            @RequestParam String routeCode,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate) {
        
        Map<String, Object> analysis = priceComparisonService.getPriceAnalysis(routeCode, startDate, endDate);
        return ResponseEntity.ok(analysis);
    }

    @GetMapping("/airline")
    public ResponseEntity<List<Map<String, Object>>> getAirlineComparison(
            @RequestParam String routeCode,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate) {
        
        List<Map<String, Object>> comparison = priceComparisonService.getAirlineComparison(routeCode, startDate, endDate);
        return ResponseEntity.ok(comparison);
    }

    @GetMapping("/cabin")
    public ResponseEntity<List<Map<String, Object>>> getCabinComparison(
            @RequestParam String routeCode,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate) {
        
        List<Map<String, Object>> comparison = priceComparisonService.getCabinComparison(routeCode, startDate, endDate);
        return ResponseEntity.ok(comparison);
    }

    @GetMapping("/date")
    public ResponseEntity<List<Map<String, Object>>> getDateComparison(
            @RequestParam String routeCode,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate) {
        
        List<Map<String, Object>> comparison = priceComparisonService.getDateComparison(routeCode, startDate, endDate);
        return ResponseEntity.ok(comparison);
    }
} 