package com.flight.controller;

import com.flight.entity.PriceHistory;
import com.flight.service.PriceHistoryService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;

@RestController
@RequestMapping("/api/price-history")
public class PriceHistoryController {

    @Autowired
    private PriceHistoryService priceHistoryService;

    @GetMapping
    public ResponseEntity<Page<PriceHistory>> getPriceHistory(
            @RequestParam(required = false) String routeCode,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) {
        
        PageRequest pageRequest = PageRequest.of(page, size);
        Page<PriceHistory> priceHistory = priceHistoryService.getPriceHistory(routeCode, startDate, endDate, pageRequest);
        return ResponseEntity.ok(priceHistory);
    }

    @GetMapping("/latest")
    public ResponseEntity<Page<PriceHistory>> getLatestPriceHistory(
            @RequestParam String routeCode,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) {
        
        PageRequest pageRequest = PageRequest.of(page, size);
        Page<PriceHistory> priceHistory = priceHistoryService.getLatestPriceHistory(routeCode, pageRequest);
        return ResponseEntity.ok(priceHistory);
    }

    @PostMapping
    public ResponseEntity<Void> createPriceHistory(@RequestBody PriceHistory priceHistory) {
        priceHistoryService.savePriceHistory(priceHistory);
        return ResponseEntity.ok().build();
    }

    @PutMapping("/{id}")
    public ResponseEntity<Void> updatePriceHistory(
            @PathVariable Long id,
            @RequestBody PriceHistory priceHistory) {
        priceHistory.setId(id);
        priceHistoryService.updatePriceHistory(priceHistory);
        return ResponseEntity.ok().build();
    }

    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deletePriceHistory(@PathVariable Long id) {
        priceHistoryService.deletePriceHistory(id);
        return ResponseEntity.ok().build();
    }
} 