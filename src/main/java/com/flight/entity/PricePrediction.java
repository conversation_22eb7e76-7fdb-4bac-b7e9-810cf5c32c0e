package com.flight.entity;

import lombok.Data;
import javax.persistence.*;
import java.math.BigDecimal;
import java.time.LocalDate;

@Data
@Entity
@Table(name = "price_prediction")
public class PricePrediction {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "route_code", nullable = false)
    private String routeCode;

    @Column(name = "departure_date", nullable = false)
    private LocalDate departureDate;

    @Column(name = "predicted_price", nullable = false)
    private BigDecimal predictedPrice;

    @Column(name = "lower_bound", nullable = false)
    private BigDecimal lowerBound;

    @Column(name = "upper_bound", nullable = false)
    private BigDecimal upperBound;

    @Column(name = "actual_price")
    private BigDecimal actualPrice;

    @Column(name = "accuracy")
    private Double accuracy;

    @Column(name = "model_id", nullable = false)
    private Integer modelId;

    @Column(name = "model_name", nullable = false)
    private String modelName;

    @Column(name = "suggestion", nullable = false)
    private String suggestion;

    @Column(name = "factors", columnDefinition = "TEXT")
    private String factors;

    @Column(name = "status")
    private String status;

    @Column(name = "created_at", nullable = false)
    private LocalDate createdAt;

    @Column(name = "updated_at", nullable = false)
    private LocalDate updatedAt;

    @PrePersist
    protected void onCreate() {
        createdAt = LocalDate.now();
        updatedAt = LocalDate.now();
    }

    @PreUpdate
    protected void onUpdate() {
        updatedAt = LocalDate.now();
    }
} 