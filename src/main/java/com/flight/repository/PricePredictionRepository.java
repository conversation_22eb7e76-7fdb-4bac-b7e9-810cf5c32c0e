package com.flight.repository;

import com.flight.entity.PricePrediction;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.List;

@Repository
public interface PricePredictionRepository extends JpaRepository<PricePrediction, Long> {
    
    @Query("SELECT pp FROM PricePrediction pp WHERE " +
           "(:routeCode IS NULL OR pp.routeCode = :routeCode) AND " +
           "(:departureDate IS NULL OR pp.departureDate = :departureDate) AND " +
           "(:modelId IS NULL OR pp.modelId = :modelId) " +
           "ORDER BY pp.createdAt DESC")
    List<PricePrediction> findByRouteAndDateAndModel(
        @Param("routeCode") String routeCode,
        @Param("departureDate") LocalDate departureDate,
        @Param("modelId") Integer modelId
    );

    @Query("SELECT pp FROM PricePrediction pp WHERE " +
           "pp.routeCode = :routeCode AND " +
           "pp.departureDate BETWEEN :startDate AND :endDate " +
           "ORDER BY pp.departureDate ASC")
    List<PricePrediction> findByRouteAndDateRange(
        @Param("routeCode") String routeCode,
        @Param("startDate") LocalDate startDate,
        @Param("endDate") LocalDate endDate
    );

    @Query("SELECT AVG(pp.accuracy) FROM PricePrediction pp WHERE " +
           "pp.modelId = :modelId AND " +
           "pp.actualPrice IS NOT NULL")
    Double getModelAccuracy(@Param("modelId") Integer modelId);

    @Query("SELECT COUNT(pp) FROM PricePrediction pp WHERE " +
           "pp.modelId = :modelId AND " +
           "pp.actualPrice IS NOT NULL AND " +
           "ABS(pp.predictedPrice - pp.actualPrice) / pp.actualPrice <= 0.1")
    Long getAccuratePredictionCount(@Param("modelId") Integer modelId);
} 