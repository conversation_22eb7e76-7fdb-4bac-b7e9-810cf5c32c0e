package com.flight.repository;

import com.flight.entity.PriceTrend;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.List;

@Repository
public interface PriceTrendRepository extends JpaRepository<PriceTrend, Long> {
    
    @Query("SELECT pt FROM PriceTrend pt WHERE " +
           "(:routeCode IS NULL OR pt.routeCode = :routeCode) AND " +
           "(:startDate IS NULL OR pt.date >= :startDate) AND " +
           "(:endDate IS NULL OR pt.date <= :endDate) " +
           "ORDER BY pt.date ASC")
    List<PriceTrend> findByRouteAndDateRange(
        @Param("routeCode") String routeCode,
        @Param("startDate") LocalDate startDate,
        @Param("endDate") LocalDate endDate
    );

    @Query("SELECT AVG(pt.price) FROM PriceTrend pt WHERE " +
           "pt.routeCode = :routeCode AND " +
           "pt.date BETWEEN :startDate AND :endDate")
    Double getAveragePrice(
        @Param("routeCode") String routeCode,
        @Param("startDate") LocalDate startDate,
        @Param("endDate") LocalDate endDate
    );

    @Query("SELECT MAX(pt.price) FROM PriceTrend pt WHERE " +
           "pt.routeCode = :routeCode AND " +
           "pt.date BETWEEN :startDate AND :endDate")
    Double getMaxPrice(
        @Param("routeCode") String routeCode,
        @Param("startDate") LocalDate startDate,
        @Param("endDate") LocalDate endDate
    );

    @Query("SELECT MIN(pt.price) FROM PriceTrend pt WHERE " +
           "pt.routeCode = :routeCode AND " +
           "pt.date BETWEEN :startDate AND :endDate")
    Double getMinPrice(
        @Param("routeCode") String routeCode,
        @Param("startDate") LocalDate startDate,
        @Param("endDate") LocalDate endDate
    );
} 