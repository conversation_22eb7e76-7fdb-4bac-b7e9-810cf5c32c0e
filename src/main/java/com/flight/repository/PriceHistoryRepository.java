package com.flight.repository;

import com.flight.entity.PriceHistory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;

@Repository
public interface PriceHistoryRepository extends JpaRepository<PriceHistory, Long> {
    
    @Query("SELECT ph FROM PriceHistory ph WHERE " +
           "(:routeCode IS NULL OR ph.routeCode = :routeCode) AND " +
           "(:startDate IS NULL OR ph.date >= :startDate) AND " +
           "(:endDate IS NULL OR ph.date <= :endDate)")
    Page<PriceHistory> findByRouteAndDateRange(
        @Param("routeCode") String routeCode,
        @Param("startDate") LocalDate startDate,
        @Param("endDate") LocalDate endDate,
        Pageable pageable
    );

    @Query("SELECT ph FROM PriceHistory ph WHERE ph.routeCode = :routeCode " +
           "ORDER BY ph.date DESC")
    Page<PriceHistory> findLatestByRoute(
        @Param("routeCode") String routeCode,
        Pageable pageable
    );
} 