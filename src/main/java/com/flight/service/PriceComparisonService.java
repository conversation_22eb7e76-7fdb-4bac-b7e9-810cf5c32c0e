package com.flight.service;

import com.flight.entity.PriceComparison;
import java.time.LocalDate;
import java.util.List;
import java.util.Map;

public interface PriceComparisonService {
    
    Map<String, Object> comparePrices(String routeCode, LocalDate startDate, LocalDate endDate, String comparisonType);
    
    List<PriceComparison> getComparisonHistory(String routeCode, LocalDate startDate, LocalDate endDate);
    
    void saveComparison(PriceComparison comparison);
    
    void updateComparison(PriceComparison comparison);
    
    void deleteComparison(Long id);
    
    Map<String, Object> getPriceAnalysis(String routeCode, LocalDate startDate, LocalDate endDate);
    
    List<Map<String, Object>> getAirlineComparison(String routeCode, LocalDate startDate, LocalDate endDate);
    
    List<Map<String, Object>> getCabinComparison(String routeCode, LocalDate startDate, LocalDate endDate);
    
    List<Map<String, Object>> getDateComparison(String routeCode, LocalDate startDate, LocalDate endDate);
    
    String generateSuggestion(List<PriceComparison> comparisons);
} 