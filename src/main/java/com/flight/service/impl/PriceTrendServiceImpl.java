package com.flight.service.impl;

import com.flight.entity.PriceTrend;
import com.flight.repository.PriceTrendRepository;
import com.flight.service.PriceTrendService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.util.*;

@Service
public class PriceTrendServiceImpl implements PriceTrendService {

    @Autowired
    private PriceTrendRepository priceTrendRepository;

    @Override
    public List<PriceTrend> getPriceTrend(String routeCode, LocalDate startDate, LocalDate endDate) {
        return priceTrendRepository.findByRouteAndDateRange(routeCode, startDate, endDate);
    }

    @Override
    public Map<String, Object> getPriceAnalysis(String routeCode, LocalDate startDate, LocalDate endDate) {
        List<PriceTrend> trends = getPriceTrend(routeCode, startDate, endDate);
        
        Map<String, Object> analysis = new HashMap<>();
        analysis.put("trendData", trends);
        
        Map<String, Object> stats = new HashMap<>();
        stats.put("overallTrend", analyzeTrend(trends));
        stats.put("avgPrice", priceTrendRepository.getAveragePrice(routeCode, startDate, endDate));
        stats.put("maxPrice", priceTrendRepository.getMaxPrice(routeCode, startDate, endDate));
        stats.put("minPrice", priceTrendRepository.getMinPrice(routeCode, startDate, endDate));
        stats.put("volatility", calculateVolatility(trends));
        stats.put("suggestedTime", getSuggestedTime(trends));
        
        analysis.put("analysis", stats);
        return analysis;
    }

    @Override
    @Transactional
    public void savePriceTrend(PriceTrend priceTrend) {
        priceTrendRepository.save(priceTrend);
    }

    @Override
    @Transactional
    public void updatePriceTrend(PriceTrend priceTrend) {
        if (priceTrendRepository.existsById(priceTrend.getId())) {
            priceTrendRepository.save(priceTrend);
        } else {
            throw new RuntimeException("价格趋势记录不存在");
        }
    }

    @Override
    @Transactional
    public void deletePriceTrend(Long id) {
        priceTrendRepository.deleteById(id);
    }

    @Override
    public String analyzeTrend(List<PriceTrend> trends) {
        if (trends.isEmpty()) {
            return "平稳";
        }

        int upCount = 0;
        int downCount = 0;
        int stableCount = 0;

        for (int i = 1; i < trends.size(); i++) {
            BigDecimal change = trends.get(i).getChangePercent();
            if (change.compareTo(new BigDecimal("0.05")) > 0) {
                upCount++;
            } else if (change.compareTo(new BigDecimal("-0.05")) < 0) {
                downCount++;
            } else {
                stableCount++;
            }
        }

        int total = trends.size() - 1;
        if (upCount > total * 0.6) {
            return "上涨";
        } else if (downCount > total * 0.6) {
            return "下跌";
        } else {
            return "平稳";
        }
    }

    @Override
    public String getSuggestedTime(List<PriceTrend> trends) {
        if (trends.isEmpty()) {
            return "暂无建议";
        }

        // 找到价格最低的日期
        PriceTrend lowestPrice = trends.stream()
                .min(Comparator.comparing(PriceTrend::getPrice))
                .orElse(null);

        if (lowestPrice != null) {
            return lowestPrice.getDate().toString();
        }
        return "暂无建议";
    }

    @Override
    public double calculateVolatility(List<PriceTrend> trends) {
        if (trends.size() < 2) {
            return 0.0;
        }

        // 计算价格变化的标准差
        double mean = trends.stream()
                .mapToDouble(pt -> pt.getPrice().doubleValue())
                .average()
                .orElse(0.0);

        double variance = trends.stream()
                .mapToDouble(pt -> Math.pow(pt.getPrice().doubleValue() - mean, 2))
                .average()
                .orElse(0.0);

        double volatility = Math.sqrt(variance) / mean * 100;
        return new BigDecimal(volatility).setScale(2, RoundingMode.HALF_UP).doubleValue();
    }
} 