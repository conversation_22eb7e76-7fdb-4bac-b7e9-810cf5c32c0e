package com.flight.service.impl;

import com.flight.entity.PriceHistory;
import com.flight.repository.PriceHistoryRepository;
import com.flight.service.PriceHistoryService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;

@Service
public class PriceHistoryServiceImpl implements PriceHistoryService {

    @Autowired
    private PriceHistoryRepository priceHistoryRepository;

    @Override
    public Page<PriceHistory> getPriceHistory(String routeCode, LocalDate startDate, LocalDate endDate, Pageable pageable) {
        return priceHistoryRepository.findByRouteAndDateRange(routeCode, startDate, endDate, pageable);
    }

    @Override
    public Page<PriceHistory> getLatestPriceHistory(String routeCode, Pageable pageable) {
        return priceHistoryRepository.findLatestByRoute(routeCode, pageable);
    }

    @Override
    @Transactional
    public void savePriceHistory(PriceHistory priceHistory) {
        priceHistoryRepository.save(priceHistory);
    }

    @Override
    @Transactional
    public void updatePriceHistory(PriceHistory priceHistory) {
        if (priceHistoryRepository.existsById(priceHistory.getId())) {
            priceHistoryRepository.save(priceHistory);
        } else {
            throw new RuntimeException("价格历史记录不存在");
        }
    }

    @Override
    @Transactional
    public void deletePriceHistory(Long id) {
        priceHistoryRepository.deleteById(id);
    }
} 