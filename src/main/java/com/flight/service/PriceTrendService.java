package com.flight.service;

import com.flight.entity.PriceTrend;
import java.time.LocalDate;
import java.util.List;
import java.util.Map;

public interface PriceTrendService {
    List<PriceTrend> getPriceTrend(String routeCode, LocalDate startDate, LocalDate endDate);
    
    Map<String, Object> getPriceAnalysis(String routeCode, LocalDate startDate, LocalDate endDate);
    
    void savePriceTrend(PriceTrend priceTrend);
    
    void updatePriceTrend(PriceTrend priceTrend);
    
    void deletePriceTrend(Long id);
    
    String analyzeTrend(List<PriceTrend> trends);
    
    String getSuggestedTime(List<PriceTrend> trends);
    
    double calculateVolatility(List<PriceTrend> trends);
} 