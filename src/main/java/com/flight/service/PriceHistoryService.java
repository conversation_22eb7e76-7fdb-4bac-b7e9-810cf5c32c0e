package com.flight.service;

import com.flight.entity.PriceHistory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.time.LocalDate;

public interface PriceHistoryService {
    Page<PriceHistory> getPriceHistory(String routeCode, LocalDate startDate, LocalDate endDate, Pageable pageable);
    
    Page<PriceHistory> getLatestPriceHistory(String routeCode, Pageable pageable);
    
    void savePriceHistory(PriceHistory priceHistory);
    
    void updatePriceHistory(PriceHistory priceHistory);
    
    void deletePriceHistory(Long id);
} 